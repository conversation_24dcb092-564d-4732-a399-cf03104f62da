{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1f845db6", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sympy as sp\n", "from tqdm import tqdm\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "2b6a7c43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4009078317304838\n"]}], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "beta  = 1 / kB / T\n", "print(beta)"]}, {"cell_type": "code", "execution_count": 3, "id": "f2dfde40", "metadata": {}, "outputs": [], "source": ["# Initialize the variables x and y for symbolic calculation\n", "x   =  sp.symbols('x')\n", "\n", "# Define the potentials Va, Vb and V with sympy\n", "V   = ( x ** 2 - 1 ) ** 2\n", "\n", "# Calculate derivative and second derivative with sympy\n", "gradVx   =  V.diff(x)\n", "grad2Vx2 =  gradVx.diff(x)\n", "\n", "# To display sympy functions:\n", "# display(gradVx)\n", "\n", "# Convert potential and derivatives in numpy functions\n", "V         =  sp.lambdify((x), V, modules=['numpy'])\n", "gradVx    =  sp.lambdify((x), gradVx, modules=['numpy'])\n", "grad2Vx2  =  sp.lambdify((x), grad2Vx2, modules=['numpy'])"]}, {"cell_type": "code", "execution_count": 4, "id": "51d0d778", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.9959919839679359\n", "0.9959919839679359\n", "-2.220446049250313e-16\n"]}], "source": ["# Grid\n", "xbins     = 500   \n", "xmin      = - 3.5\n", "xmax      = - xmin\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins     = xbins - 1\n", "\n", "iA        = np.argmin(V(xcenters[0:int(xbins/2)]))\n", "iC        = int(xbins/2) + np.argmin(V(xcenters[int(xbins/2):-1]))\n", "iB        = iA   + np.argmax(V(xcenters[iA:iC]))\n", "\n", "print(xcenters[iA])\n", "print(xcenters[iC])\n", "print(xcenters[iB])\n", "\n", "# Approximation at xA and xB\n", "xA        = xcenters[iA]\n", "omegaA    = np.sqrt( np.abs(grad2Vx2(xA)) / mass )\n", "kspringA  = omegaA ** 2 * mass\n", "\n", "xB        = xcenters[iB]\n", "omegaB    = np.sqrt( np.abs(grad2Vx2(xB)) / mass )\n", "kspringB  = omegaB ** 2 * mass\n", "\n", "xC        = xcenters[iC]\n", "omegaC    = np.sqrt( np.abs(grad2Vx2(xC)) / mass )\n", "kspringC  = omegaC ** 2 * mass\n", "\n", "# Energy barriers\n", "Eb_plus   = V(xB) - V(xA)\n", "Eb_minus  = V(xB) - V(xC)"]}, {"cell_type": "markdown", "id": "d08cf2bb", "metadata": {}, "source": ["The next block defines the BBK integrator for Langevin dynamics."]}, {"cell_type": "code", "execution_count": 5, "id": "0f1e04a7", "metadata": {}, "outputs": [], "source": ["def langevin_bbk_step(Q, P, M, gamma, beta, der_V, dt):\n", "    L = 1 / (1 + 0.5 * gamma*dt)\n", "    \n", "    # Deterministic force\n", "    F  =  - der_V(Q)\n", "    \n", "    # Random force \n", "    R  =  np.random.normal(0, 1, size = np.array(Q).shape)\n", "    \n", "    # update p_{n+1/2}\n", "    Phalf = ( 1 - 0.5 * gamma * dt ) * P + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R\n", "    \n", "    # update q_{n+1}\n", "    Q  =  Q + Phalf / M * dt\n", "    \n", "    # Recalculate deterministic force (but not the random force)\n", "    F  =  - der_V(Q)\n", "    \n", "    # update p_{n+1}\n", "    P = ( Phalf + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R ) / ( 1 + 0.5 * gamma * dt ) \n", "\n", "    return Q, P"]}, {"cell_type": "code", "execution_count": 6, "id": "781ac34d", "metadata": {}, "outputs": [], "source": ["# Number of gamma values being test\n", "Ng     = 30 #25\n", "\n", "# Generate gamma values between 0.05 and 10\n", "# Use logspace to generate more points close to 0.05 than 10\n", "gammas =  np.logspace(np.log(0.01), np.log(25), Ng, base=np.exp(1))"]}, {"cell_type": "markdown", "id": "6f6455ef", "metadata": {}, "source": ["### Numerical experiment"]}, {"cell_type": "code", "execution_count": 7, "id": "68140f57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gamma:  0.010000000000000007\n", "Gamma:  0.013096955295775894\n", "Gamma:  0.017153023801955224\n", "Gamma:  0.02246523859215873\n", "Gamma:  0.029422622555044218\n", "Gamma:  0.03853467722879015\n", "Gamma:  0.05046869450026176\n", "Gamma:  0.06609862357060987\n", "Gamma:  0.08656907180165961\n", "Gamma:  0.11337912633831491\n", "Gamma:  0.14849213491270372\n", "Gamma:  0.1944794852726002\n", "Gamma:  0.254708912456075\n", "Gamma:  0.33359112398729096\n", "Gamma:  0.436902803792918\n", "Gamma:  0.5722096489874995\n", "Gamma:  0.7494204192600892\n", "Gamma:  0.9815125728791008\n", "Gamma:  1.2854826289239565\n", "Gamma:  1.6835908524513516\n", "Gamma:  2.2049914130932584\n", "Gamma:  2.8878673964852104\n", "Gamma:  3.7822270191895493\n", "Gamma:  4.953565818880125\n", "Gamma:  6.4876630084556455\n", "Gamma:  8.496863239580248\n", "Gamma:  11.128303800310405\n", "Gamma:  14.574689739047827\n", "Gamma:  19.08840599621129\n", "Gamma:  24.99999999999999\n"]}], "source": ["# Number of simulations\n", "Nreps = 500\n", "\n", "# Integrator timestep\n", "dt  =  0.005 # ps\n", "sdt =  np.sqrt(dt)\n", "\n", "# Array to store simulation time for each replica and each gamma value\n", "simTimes = np.zeros((<PERSON><PERSON><PERSON>,<PERSON>))\n", "\n", "# For loop over gamma values\n", "for g, gamma in enumerate(gammas):\n", "    \n", "    print(\"Gamma: \", str(gamma))\n", "          \n", "    # Recalculate diffusion constant\n", "    D     = kB * T / mass / gamma # nm2 ps-1\n", "    sigma = np.sqrt(2 * D)\n", "\n", "    for r in range(Nreps):\n", "        \n", "        #print(\"Gamma: \", str(gamma), \"; Replica: \", str(r))\n", "        \n", "        # Initial position\n", "        x0  =  xA \n", "\n", "        # Final position\n", "        xF  =  xC\n", "        \n", "        # Initial momentum drawn from the <PERSON><PERSON><PERSON> distribution\n", "        p0  =  np.random.normal(0, 1) * np.sqrt( mass / beta )\n", "\n", "        # Initialize position and velocity\n", "        x   =   x0\n", "        v   =   p0 / mass\n", "        \n", "        t = 0\n", "        \n", "        # Start the simulation and stop if x > xC\n", "        while x < xF:\n", "            \n", "            # Update position and momentum\n", "            x, p = langevin_bbk_step(x, mass * v, mass, gamma, beta, gradVx, dt)\n", "            \n", "            # Calculate velocity\n", "            v    = p / mass\n", "            \n", "            # Update simulation time\n", "            t    = t + 1\n", "        \n", "        # Store simulation time to go from xA to xB\n", "        simTimes[r,g] = t * dt\n", "            \n", "    \n", "# MFPT from experiment\n", "MFPT = np.mean(simTimes, axis=0)\n", "\n", "# Rate from experiment\n", "expRate = 1 / MFPT    "]}, {"cell_type": "markdown", "id": "3deae671-b8da-4363-9c7b-068a4bff253a", "metadata": {}, "source": ["## Pontryagin formula"]}, {"cell_type": "code", "execution_count": 8, "id": "c925d363-cd7b-4533-8d92-e91bc897feec", "metadata": {}, "outputs": [], "source": ["def pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters ):\n", "\n", "    D = 1 / beta / mass / gamma\n", "    \n", "    i0 = np.argmin(np.abs(xcenters - x0)) + 1\n", "    xcenters0 = xcenters[0:i0]\n", "    \n", "    iF = np.argmin(np.abs(xcenters - xF)) + 1\n", "    xcentersF = xcenters[0:iF]\n", "    \n", "    nn = iF - i0\n", "    innerInt = np.zeros(nn+1)\n", "    \n", "    for i in range(nn):\n", "        x = xcenters[0:iA+i+1]\n", "        innerInt[i+1] = np.sum(dx * np.exp( - beta * V(x) ) )\n", "\n", "    x        = xcenters[iA:iA+i+2]\n", "    outerInt = sum( dx * np.exp(beta * V(x)) * innerInt )\n", "\n", "    mfpt = outerInt / D\n", "\n", "    return mfpt\n", "\n", "\n", "def kramersRateModerateFriction(gamma):\n", "     return gamma / omegaB * ( np.sqrt(0.25 + omegaB ** 2 / gamma ** 2 ) - 0.5 ) * omegaA / 2 / np.pi * \\\n", "        np.exp( - beta * ( Eb_plus ))\n", "    \n", "def kramersRateHighFriction(gamma):\n", "     return  omegaA * omegaB / 2 / np.pi / gamma * \\\n", "        np.exp( - beta * ( Eb_plus ))\n", "\n", "\n", "pontryagin   = np.zeros(Ng)\n", "kramers_mod  = np.zeros(Ng)\n", "kramers_high = np.zeros(Ng)\n", "\n", "\n", "for g, gamma in enumerate(gammas):\n", "    pontryagin[g]   = 1 / pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters )\n", "    kramers_mod[g]  = kramersRateModerateFriction( gamma )\n", "    kramers_high[g] = kramersRateHighFriction( gamma )"]}, {"cell_type": "code", "execution_count": 12, "id": "22623f67-8259-477e-9cec-3b096357ef13", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 944.882x314.961 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24*in2cm, 8*in2cm), facecolor='white')  \n", "\n", "ax1.plot(xcenters, V(xcenters), 'k', label = 'Potential') \n", "#ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax1.plot(xA, V(xA), 'go', label = 'Reactant') \n", "\n", "#ax1.plot(xcenters, - 0.5 * kspringB * (xcenters - xB) ** 2 + V(xB), 'k--', linewidth = 0.5)\n", "ax1.plot(xB, V(xB), 'ro', label = 'Barrier') \n", "\n", "#ax1.plot(xcenters,   0.5 * kspringC * (xcenters - xC) ** 2 + V(xC), 'k--', linewidth = 0.5 ) \n", "ax1.plot(xC, V(xC), 'bo' , label = 'Product') \n", "\n", "ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$')\n", "ax1.set_ylabel(r'$V(x)$')\n", "ax1.legend()\n", "ax1.set_ylim((-0, 2.5))\n", "ax1.set_xlim((xmin, xmax))\n", "\n", "\n", "ax2.plot(gammas, expRate, 'ro', label = 'Numerics') \n", "#ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax2.plot(gammas, pontryagin, 'k--', label = 'Pontryagin') \n", "ax2.plot(gammas, kramers_mod, 'b--', label = 'Kramer<PERSON>') \n", "#ax2.plot(gammas, kramers_high, 'g--', label = 'Kramers high') \n", "\n", "ax2.set_title('Rates')\n", "ax2.set_xlabel(r'$\\gamma$ / ps $^{-1}$')\n", "ax2.set_ylabel(r'$k$ / ps$^{-1}$')\n", "ax2.legend()\n", "ax2.set_xlim((0, gammas[-1]))\n", "ax2.set_ylim((0, 0.3))\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.4);\n", "\n", "#fig.savefig('figures/potential.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}