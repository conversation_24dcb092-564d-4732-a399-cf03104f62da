{"cells": [{"cell_type": "code", "execution_count": 1, "id": "be027ab5", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import sympy as sp\n", "from tqdm import tqdm\n", "import matplotlib.pyplot as plt\n", "import matplotlib.cm as cm\n", "from scipy import special\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "id": "d29ff1f7-a167-4034-ac18-112538e5afc7", "metadata": {}, "source": ["## Kramers theory for moderate to high friction regime"]}, {"cell_type": "code", "execution_count": 2, "id": "6282492a-0d72-44ac-bbe4-4c57c73ad108", "metadata": {}, "outputs": [], "source": ["def langevin_bbk_step(Q, P, M, gamma, beta, der_V, dt):\n", "    \n", "    Nreps = Q.shape[0]\n", "    \n", "    # bbk Coefficent\n", "    L = 1 / (1 + 0.5 * gamma*dt)\n", "    \n", "    # Deterministic force\n", "    F  =  - der_V(Q)\n", "    \n", "    # Random force \n", "    R  =  np.random.normal(0, 1, Nreps)\n", "    \n", "    # update p_{n+1/2}\n", "    Phalf = ( 1 - 0.5 * gamma * dt ) * P + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R\n", "    \n", "    # update q_{n+1}\n", "    Q  =  Q + Phalf / M * dt\n", "    \n", "    # Recalculate deterministic force (but not the random force)\n", "    F  =  - der_V(Q)\n", "    \n", "    # update p_{n+1}\n", "    P = ( Phalf + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R ) / ( 1 + 0.5 * gamma * dt ) \n", "\n", "    return Q, P"]}, {"cell_type": "code", "execution_count": 3, "id": "ee06a9f3-8541-4e8e-af25-226c5c191ab5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.4943389\n"]}], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "beta  = 1 / kB / T\n", "\n", "print(kB * T)"]}, {"cell_type": "code", "execution_count": 4, "id": "f441697b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.24943389\n"]}], "source": ["# Initialize the variables x and y for symbolic calculation\n", "x   =  sp.symbols('x')\n", "Eb  =  10\n", "\n", "# Define the potentials Va, Vb and V with sympy\n", "V   =   Eb*(x**2 - 1)**2\n", "\n", "xA = -1\n", "xB =  0\n", "xC =  1 \n", "\n", "# Calculate derivative and second derivative with sympy\n", "gradVx   =  V.diff(x)\n", "grad2Vx2 =  gradVx.diff(x)\n", "\n", "# To display sympy functions:\n", "# display(gradVx)\n", "\n", "# Convert potential and derivatives in numpy functions\n", "V         =  sp.lambdify((x), V, modules=['numpy'])\n", "gradVx    =  sp.lambdify((x), gradVx, modules=['numpy'])\n", "grad2Vx2  =  sp.lambdify((x), grad2Vx2, modules=['numpy'])\n", "\n", "# Approximation at xA and xB\n", "omegaA = np.sqrt( np.abs(grad2Vx2(xA)) / mass )\n", "kspringA     = omegaA ** 2 * mass\n", "\n", "\n", "\n", "omegaB = np.sqrt( np.abs(grad2Vx2(xB)) / mass )\n", "kspringB     = omegaB ** 2 * mass\n", "\n", "\n", "\n", "def a(gamma):\n", "    return ( gamma / ( 2 * omegaB ** 2 ) ) * ( np.sqrt( 1 + 4 * omegaB ** 2 / gamma ** 2 ) - 1 ) \n", "    \n", "def u(x,p,gamma):\n", "    return x - a(gamma) * p/mass\n", "\n", "def xi( x, p, gamma ):\n", "    arg = np.sqrt( mass * omegaB ** 2 / ( 2 * gamma * kB * T * a(gamma) ) )\n", "    return 0.5 * special.erfc( arg * u(x,p,gamma) )\n", "\n", "def xi_u( u, gamma ):\n", "    arg = np.sqrt( mass * omegaB ** 2 / ( 2 * gamma * kB * T * a(gamma) ) )\n", "    return 0.5 * special.erfc( arg * u )\n", "\n", "def Peq_f(x,p):\n", "    return np.exp( - 0.5 * beta * p ** 2 / mass - beta * V(x))\n", "\n", "\n", "def <PERSON><PERSON>(x):\n", "    return V(xB) - 0.5 * mass * omegaB ** 2 * (x - xB) ** 2\n", "\n", "def Peq_lin(x,p):\n", "    return np.exp( - 0.5 * beta * p ** 2 / mass - beta * Vlin(x))\n", "\n", "def Pss_f( x, p, gamma ):\n", "    return Peq_f(x,p) * xi(x, p, gamma)\n", "\n", "Eb = V(xB) - V(xA)\n", "print(kB * T / Eb)"]}, {"cell_type": "code", "execution_count": 106, "id": "390f633c-ce93-412c-aefe-4cd1ce92c9a6", "metadata": {}, "outputs": [], "source": ["# Grid\n", "xbins     = 50   \n", "xmin      = - 2\n", "xmax      = - xmin\n", "\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins     = xbins - 1\n", "\n", "pbins     = 100  \n", "pmin      = - 8\n", "pmax      = - pmin\n", "\n", "pedges    = np.linspace(pmin, pmax, pbins)  # array with x edges\n", "dp        = pedges[1] - pedges[0]\n", "pcenters  = pedges[:-1] + (dp / 2)                # array with x centers\n", "pbins     = pbins - 1\n", "\n", "xp        = np.meshgrid(xcenters, pcenters)\n", "xpf       = np.array([xp[0].flatten('F'), xp[1].flatten('F')]).T"]}, {"cell_type": "code", "execution_count": 107, "id": "cf1762ed-6a4f-4433-91ab-5d5d80465d01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 49999/49999 [00:02<00:00, 18047.40it/s]\n"]}], "source": ["gamma2 = 1*omegaB\n", "print(gamma2 / omegaB)\n", "\n", "Nreps = 1\n", "## Simulation\n", "\n", "# Integrator timestep\n", "dt  =  0.005 # ps\n", "sdt =  np.sqrt(dt)\n", "\n", "# Number of timesteps\n", "Nsteps  =  50000\n", "\n", "# Initial position\n", "x0  =  xA + np.random.normal(0, 1, Nreps) * np.sqrt( 1 / beta / kspringA )\n", "p0  =  np.random.normal(0, 1, Nreps) * np.sqrt( mass / beta )\n", "\n", "# Arrays for x,y values\n", "x2   =   np.zeros((<PERSON><PERSON><PERSON>, Nreps))\n", "p2   =   np.zeros((<PERSON><PERSON><PERSON>, Nreps))\n", "\n", "x2[0,:] = x0\n", "p2[0,:] = p0\n", "\n", "for k in tqdm(range(Nsteps-1)):\n", "    x2[k+1,:], p2[k+1,:]   = langevin_bbk_step(x2[k,:], p2[k,:], mass, gamma2, beta, gradVx, dt)\n", "    p2[k+1, x2[k+1,:]>=xC] = np.random.normal(0, 1, sum(x2[k+1,:]>=xC)) * np.sqrt( mass / beta )\n", "    x2[k+1, x2[k+1,:]>=xC] = xA + np.random.normal(0, 1, sum(x2[k+1,:]>=xC)) * np.sqrt( 1 / beta / kspringA )"]}, {"cell_type": "code", "execution_count": 108, "id": "d502b91b-d71f-4800-971f-950d946b5135", "metadata": {"scrolled": true}, "outputs": [], "source": ["# Boltzmann distribution\n", "Peq = Peq_f(xp[0], xp[1]) \n", "Peq = Peq / np.sum( Peq * dx * dp )\n", "\n", "# Calculate distributions    \n", "Pss2 = np.histogram2d(x2.flatten(), p2.flatten(), bins=(xedges, pedges))[0].T\n", "Pss2 = Pss2 / np.sum( Pss2 * dx * dp )\n", "\n", "# Perturbing distribuitons\n", "Xiss2 = Pss2 / Peq\n", "\n", "Pss2_ex = Pss_f(xp[0], xp[1], gamma2) \n", "\n", "Xiss2_ex = xi(xp[0], xp[1], gamma2)\n", "\n", "# Flux\n", "f    = pcenters * Pss_f(0, pcenters, gamma2)\n", "ff  = np.zeros((pbins, xbins)) \n", "ff[:,24] = f*dp"]}, {"cell_type": "code", "execution_count": 109, "id": "285fc0dc-8468-47fe-bcff-9576f01f0d34", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'p')"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 907.087x226.772 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 2,figsize=(32*in2cm, 8*in2cm), facecolor='white')  \n", "\n", "\n", "ax[0].plot(x2,'k-')\n", "\n", "ax[1].plot(x2, p2, 'k.', markersize = 1)\n", "\n", "ax[0].set_xlabel('time')\n", "ax[1].set_xlabel('x')\n", "\n", "ax[0].set_ylabel('x(t)')\n", "ax[1].set_ylabel('p')\n", "\n"]}, {"cell_type": "code", "execution_count": 110, "id": "7caa77db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.10000000000000002\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 49999/49999 [00:03<00:00, 14032.16it/s]\n"]}], "source": ["gamma2 = 0.1*omegaB\n", "print(gamma2 / omegaB)\n", "\n", "Nreps = 100\n", "## Simulation\n", "\n", "# Integrator timestep\n", "dt  =  0.005 # ps\n", "sdt =  np.sqrt(dt)\n", "\n", "# Number of timesteps\n", "Nsteps  =  50000\n", "\n", "# Initial position\n", "x0  =  xA + np.random.normal(0, 1, Nreps) * np.sqrt( 1 / beta / kspringA )\n", "p0  =  np.random.normal(0, 1, Nreps) * np.sqrt( mass / beta )\n", "\n", "# Arrays for x,y values\n", "x2   =   np.zeros((<PERSON><PERSON><PERSON>, Nreps))\n", "p2   =   np.zeros((<PERSON><PERSON><PERSON>, Nreps))\n", "\n", "x2[0,:] = x0\n", "p2[0,:] = p0\n", "\n", "for k in tqdm(range(Nsteps-1)):\n", "    x2[k+1,:], p2[k+1,:]   = langevin_bbk_step(x2[k,:], p2[k,:], mass, gamma2, beta, gradVx, dt)\n", "    p2[k+1, x2[k+1,:]>=xC] = np.random.normal(0, 1, sum(x2[k+1,:]>=xC)) * np.sqrt( mass / beta )\n", "    x2[k+1, x2[k+1,:]>=xC] = xA + np.random.normal(0, 1, sum(x2[k+1,:]>=xC)) * np.sqrt( 1 / beta / kspringA )"]}, {"cell_type": "code", "execution_count": 111, "id": "9838e7c9", "metadata": {}, "outputs": [], "source": ["# Boltzmann distribution\n", "Peq = Peq_f(xp[0], xp[1]) \n", "Peq = Peq / np.sum( Peq * dx * dp )\n", "\n", "# Calculate distributions    \n", "Pss2 = np.histogram2d(x2.flatten(), p2.flatten(), bins=(xedges, pedges))[0].T\n", "Pss2 = Pss2 / np.sum( Pss2 * dx * dp )\n", "\n", "# Perturbing distribuitons\n", "Xiss2 = Pss2 / Peq\n", "\n", "Pss2_ex = Pss_f(xp[0], xp[1], gamma2) \n", "\n", "Xiss2_ex = xi(xp[0], xp[1], gamma2)\n", "\n", "# Flux\n", "f    = pcenters * Pss_f(0, pcenters, gamma2)\n", "ff  = np.zeros((pbins, xbins)) \n", "ff[:,24] = f*dp"]}, {"cell_type": "code", "execution_count": 112, "id": "0a14f18d-7987-4296-b03c-89aca7244b03", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'p')"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 907.087x226.772 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 2,figsize=(32*in2cm, 8*in2cm), facecolor='white')  \n", "\n", "\n", "ax[0].plot(x2,p2, 'k.', markersize=0.1);\n", "pos = ax[1].pcolormesh(xp[0], xp[1], Pss2, shading='gouraud', vmin= 0, vmax = 0.02, cmap = cm.hot)\n", "ax[0].set_xlim(xmin,xmax)\n", "ax[1].set_xlim(xmin,xmax)\n", "ax[0].set_ylim(pmin,pmax)\n", "ax[1].set_ylim(pmin,pmax)\n", "\n", "ax[0].set_xlabel('x')\n", "ax[0].set_ylabel('p')\n", "ax[1].set_xlabel('x')\n", "ax[1].set_ylabel('p')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}