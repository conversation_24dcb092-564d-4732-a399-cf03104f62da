{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1f845db6", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "# import sympy as sp\n", "# from tqdm import tqdm\n", "\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "2b6a7c43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4009078317304838\n"]}], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "beta  = 1 / kB / T\n", "print(beta)"]}, {"cell_type": "code", "execution_count": 3, "id": "f2dfde40", "metadata": {}, "outputs": [], "source": ["# # Initialize the variables x and y for symbolic calculation\n", "# x   =  sp.symbols('x')\n", "# # Define the potentials Va, Vb and V with sympy\n", "# V   = ( x ** 2 - 1 ) ** 2\n", "# # Calculate derivative and second derivative with sympy\n", "# gradVx   =  V.diff(x)\n", "# grad2Vx2 =  gradVx.diff(x)\n", "# # To display sympy functions:\n", "# # display(gradVx)\n", "# # Convert potential and derivatives in numpy functions\n", "# V         =  sp.lambdify((x), V, modules=['numpy'])\n", "# gradVx    =  sp.lambdify((x), gradVx, modules=['numpy'])\n", "# grad2Vx2  =  sp.lambdify((x), grad2Vx2, modules=['numpy'])\n", "\n", "V = lambda x: (x**2 - 1)**2\n", "gradVx = lambda x: 4*x*(x**2 - 1)\n", "grad2Vx2 = lambda x: 12*x**2 - 4"]}, {"cell_type": "code", "execution_count": 4, "id": "51d0d778", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.9959919839679359\n", "0.9959919839679359\n", "-2.220446049250313e-16\n"]}], "source": ["# Grid\n", "xbins     = 500   \n", "xmin      = - 3.5\n", "xmax      = - xmin\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins     = xbins - 1\n", "\n", "iA        = np.argmin(V(xcenters[0:int(xbins/2)]))\n", "iC        = int(xbins/2) + np.argmin(V(xcenters[int(xbins/2):-1]))\n", "iB        = iA   + np.argmax(V(xcenters[iA:iC]))\n", "\n", "print(xcenters[iA])\n", "print(xcenters[iC])\n", "print(xcenters[iB])\n", "\n", "# Approximation at xA and xB\n", "xA        = xcenters[iA]\n", "omegaA    = np.sqrt( np.abs(grad2Vx2(xA)) / mass )\n", "kspringA  = omegaA ** 2 * mass\n", "\n", "xB        = xcenters[iB]\n", "omegaB    = np.sqrt( np.abs(grad2Vx2(xB)) / mass )\n", "kspringB  = omegaB ** 2 * mass\n", "\n", "xC        = xcenters[iC]\n", "omegaC    = np.sqrt( np.abs(grad2Vx2(xC)) / mass )\n", "kspringC  = omegaC ** 2 * mass\n", "\n", "# Energy barriers\n", "Eb_plus   = V(xB) - V(xA)\n", "Eb_minus  = V(xB) - V(xC)"]}, {"cell_type": "markdown", "id": "d08cf2bb", "metadata": {}, "source": ["The next block defines the BBK integrator for Langevin dynamics."]}, {"cell_type": "code", "execution_count": 5, "id": "0f1e04a7", "metadata": {}, "outputs": [], "source": ["def langevin_bbk_step(Q, P, M, gamma, beta, der_V, dt):\n", "  L = 1 / (1 + 0.5 * gamma*dt)\n", "  \n", "  # Deterministic force\n", "  F  =  - der_V(Q)\n", "  \n", "  # Random force \n", "  R  =  np.random.normal(0, 1, size = np.array(Q).shape)\n", "  \n", "  # update p_{n+1/2}\n", "  Phalf = ( 1 - 0.5 * gamma * dt ) * P + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R\n", "  \n", "  # update q_{n+1}\n", "  Q  =  Q + Phalf / M * dt\n", "  \n", "  # Recalculate deterministic force (but not the random force)\n", "  F  =  - der_V(Q)\n", "  \n", "  # update p_{n+1}\n", "  P = ( Phalf + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R ) / ( 1 + 0.5 * gamma * dt ) \n", "\n", "  return Q, P"]}, {"cell_type": "code", "execution_count": 6, "id": "781ac34d", "metadata": {}, "outputs": [], "source": ["# Number of gamma values being test\n", "Ng     = 30 #25\n", "\n", "# Generate gamma values between 0.05 and 10\n", "# Use logspace to generate more points close to 0.05 than 10\n", "gammas =  np.logspace(np.log(0.01), np.log(25), Ng, base=np.exp(1))"]}, {"cell_type": "markdown", "id": "6f6455ef", "metadata": {}, "source": ["### Numerical experiment"]}, {"cell_type": "code", "execution_count": 7, "id": "68140f57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gamma:  0.010000000000000007\n", "Gamma:  0.013096955295775894\n", "Gamma:  0.017153023801955224\n", "Gamma:  0.02246523859215873\n", "Gamma:  0.029422622555044218\n", "Gamma:  0.03853467722879015\n", "Gamma:  0.05046869450026176\n", "Gamma:  0.06609862357060987\n", "Gamma:  0.08656907180165961\n", "Gamma:  0.11337912633831491\n", "Gamma:  0.14849213491270372\n", "Gamma:  0.1944794852726002\n", "Gamma:  0.254708912456075\n", "Gamma:  0.33359112398729096\n", "Gamma:  0.436902803792918\n", "Gamma:  0.5722096489874995\n", "Gamma:  0.7494204192600892\n", "Gamma:  0.9815125728791008\n", "Gamma:  1.2854826289239565\n", "Gamma:  1.6835908524513516\n", "Gamma:  2.2049914130932584\n", "Gamma:  2.8878673964852104\n", "Gamma:  3.7822270191895493\n", "Gamma:  4.953565818880125\n", "Gamma:  6.4876630084556455\n", "Gamma:  8.496863239580248\n", "Gamma:  11.128303800310405\n", "Gamma:  14.574689739047827\n", "Gamma:  19.08840599621129\n", "Gamma:  24.99999999999999\n"]}], "source": ["# Number of simulations\n", "Nreps = 500\n", "\n", "# Integrator timestep\n", "dt  =  0.005 # ps\n", "sdt =  np.sqrt(dt)\n", "\n", "# Array to store simulation time for each replica and each gamma value\n", "simTimes = np.zeros((<PERSON><PERSON><PERSON>,<PERSON>))\n", "\n", "# For loop over gamma values\n", "for g, gamma in enumerate(gammas):\n", "  print(\"Gamma: \", str(gamma))\n", "  # Recalculate diffusion constant\n", "  D     = kB * T / mass / gamma # nm2 ps-1\n", "  sigma = np.sqrt(2 * D)\n", "  for r in range(Nreps):\n", "    # Initial position\n", "    x0  =  xA \n", "    # Final position\n", "    xF  =  xC\n", "    # Initial momentum drawn from the <PERSON><PERSON><PERSON> distribution\n", "    p0  =  np.random.normal(0, 1) * np.sqrt( mass / beta )\n", "    # Initialize position and velocity\n", "    x   =   x0\n", "    v   =   p0 / mass\n", "    t = 0\n", "    # Start the simulation and stop if x > xC\n", "    while x < xF:\n", "      # Update position and momentum\n", "      x, p = langevin_bbk_step(x, mass * v, mass, gamma, beta, gradVx, dt)\n", "      # Calculate velocity\n", "      v    = p / mass\n", "      # Update simulation time\n", "      t    = t + 1\n", "    # Store simulation time to go from xA to xB\n", "    simTimes[r,g] = t * dt\n", "\n", "# MFPT from experiment\n", "MFPT = np.mean(simTimes, axis=0)\n", "\n", "# Rate from experiment\n", "expRate = 1 / MFPT    "]}, {"cell_type": "markdown", "id": "3deae671-b8da-4363-9c7b-068a4bff253a", "metadata": {}, "source": ["## Pontryagin formula"]}, {"cell_type": "code", "execution_count": 8, "id": "c925d363-cd7b-4533-8d92-e91bc897feec", "metadata": {}, "outputs": [], "source": ["def pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters ):\n", "  D = 1 / beta / mass / gamma\n", "  \n", "  i0 = np.argmin(np.abs(xcenters - x0)) + 1\n", "  xcenters0 = xcenters[0:i0]\n", "  \n", "  iF = np.argmin(np.abs(xcenters - xF)) + 1\n", "  xcentersF = xcenters[0:iF]\n", "  \n", "  nn = iF - i0\n", "  innerInt = np.zeros(nn+1)\n", "  \n", "  for i in range(nn):\n", "    x = xcenters[0:iA+i+1]\n", "    innerInt[i+1] = np.sum(dx * np.exp( - beta * V(x) ) )\n", "\n", "  x        = xcenters[iA:iA+i+2]\n", "  outerInt = sum( dx * np.exp(beta * V(x)) * innerInt )\n", "\n", "  mfpt = outerInt / D\n", "\n", "  return mfpt\n", "\n", "def kramersRateModerateFriction(gamma):\n", "  return gamma / omegaB * ( np.sqrt(0.25 + omegaB ** 2 / gamma ** 2 ) - 0.5 ) * omegaA / 2 / np.pi * \\\n", "    np.exp( - beta * ( Eb_plus ))\n", "\n", "def kramersRateHighFriction(gamma):\n", "  return  omegaA * omegaB / 2 / np.pi / gamma * \\\n", "    np.exp( - beta * ( Eb_plus ))\n", "\n", "pontryagin   = np.zeros(Ng)\n", "kramers_mod  = np.zeros(Ng)\n", "kramers_high = np.zeros(Ng)\n", "\n", "for g, gamma in enumerate(gammas):\n", "  pontryagin[g]   = 1 / pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters )\n", "  kramers_mod[g]  = kramersRateModerateFriction( gamma )\n", "  kramers_high[g] = kramersRateHighFriction( gamma )"]}, {"cell_type": "code", "execution_count": 10, "id": "22623f67-8259-477e-9cec-3b096357ef13", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 944.882x314.961 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24*in2cm, 8*in2cm), facecolor='white')  \n", "\n", "ax1.plot(xcenters, V(xcenters), 'k', label = 'Potential') \n", "ax1.plot(xA, V(xA), 'go', label = 'Reactant') \n", "ax1.plot(xB, V(xB), 'ro', label = 'Barrier') \n", "ax1.plot(xC, V(xC), 'bo', label = 'Product') \n", "ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$')\n", "ax1.set_ylabel(r'$V(x)$')\n", "ax1.legend()\n", "ax1.set_ylim((-0, 2.5))\n", "ax1.set_xlim((xmin, xmax))\n", "\n", "ax2.plot(gammas, expRate, 'ro', label = 'Numerics') \n", "ax2.plot(gammas, pontryagin, 'k--', label = 'Pontryagin') \n", "ax2.plot(gammas, kramers_mod, 'b--', label = 'Kramer<PERSON>') \n", "ax2.set_title('Rates')\n", "ax2.set_xlabel(r'$\\gamma$ / ps $^{-1}$')\n", "ax2.set_ylabel(r'$k$ / ps$^{-1}$')\n", "ax2.legend()\n", "ax2.set_xlim((0, gammas[-1]))\n", "ax2.set_ylim((0, 0.3))\n", "\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.4);\n", "#fig.savefig('figures/potential.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}