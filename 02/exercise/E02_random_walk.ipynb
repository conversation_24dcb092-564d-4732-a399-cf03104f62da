{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf8d327a", "metadata": {}, "outputs": [], "source": ["# Import useful libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.special import factorial\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)"]}, {"cell_type": "markdown", "id": "53428ad2", "metadata": {}, "source": ["## Binomial distribution\n", "\n", "The probability to be at position $m$ after $N$ steps is\n", "\\begin{aligned}\n", "P_d(m,N) = \n", "\\begin{cases}\n", "0 & \\mathrm{if} \\, N \\, \\mathrm{even} \\, \\mathrm{and} \\, m \\, \\mathrm{odd} \\cr\n", "0 & \\mathrm{if} \\, N \\, \\mathrm{odd} \\, \\mathrm{and} \\, m \\, \\mathrm{even} \\cr\n", "\\frac{N!}{\n", "\\frac{N+m}{2}!\n", "\\cdot\n", "\\frac{N-m}{2}!\n", "}\n", "\\left( \n", "\\frac{1}{2}\n", "\\right)^N\n", "&\n", "\\rm else\n", "\\end{cases}\n", "\\end{aligned}\n", "\n", "For very large $N$, the distribution function becomes continuous. To see this, let’s apply Stirling’s approximation, $N!\\approx (N/e)^N \\sqrt{2\\pi N}$, and after a bit of manipulation we find\n", "$$\n", "P_c(m,N) =\\sqrt{\\frac{2}{N \\pi}}\\exp\\left( - \\frac{m^2}{2 N}\\right)\n", "$$\n", "\n", "The expected position is\n", "$$\n", "\\langle m \\rangle = 0\n", "$$\n", "\n", "The variance of the position is\n", "$$\n", "\\langle m^2 \\rangle = N\n", "$$"]}, {"cell_type": "code", "execution_count": 2, "id": "925a634c", "metadata": {}, "outputs": [], "source": ["# Initial position\n", "x0 = 0 # nm\n", "\n", "# Discrete distribution assuming p = 0.5\n", "def Pd(m,N):\n", "  p  = 0.5\n", "\n", "  nL = 0.5*(N - m)\n", "  nR = 0.5*(N + m)\n", "  \n", "  P = factorial(N) / factorial(nL) / factorial(nR) * p**nL * (1 - p)**nR    \n", "  \n", "  # if N even m odd\n", "  P[(N % 2 == 0) & (m % 2 != 0)] = 0\n", "  \n", "  # if N even m odd\n", "  P[(N % 2 != 0) & (m % 2 == 0)] = 0\n", "  \n", "  return  P\n", "\n", "# Continuous distribution\n", "def Pc(m,N):\n", "  P = ( 2 / N / np.pi )**0.5 * np.exp( - m**2 * ( 2 * N )**-1 )\n", "\n", "  return  P"]}, {"cell_type": "code", "execution_count": 3, "id": "d1bc489a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/hr/k68z41tx33x2dg658bq2l7p80000gn/T/ipykernel_13586/2555878731.py:11: RuntimeWarning: divide by zero encountered in divide\n", "  P = factorial(N) / factorial(nL) / factorial(nR) * p**nL * (1 - p)**nR\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["mmin  = -25 #25\n", "mmax  = -mmin\n", "mbins =  1 + mmax-mmin\n", "mcenters = np.linspace(mmin,mmax,mbins)\n", "medges   = np.linspace(mmin-0.5,mmax+0.5,mbins+1)\n", "\n", "# N = 5\n", "Pd5 = Pd(mcenters,5)\n", "Pc5 = Pc(mcenters,5)\n", "\n", "# N = 20\n", "Pd20 = Pd(mcenters,20)\n", "Pc20 = Pc(mcenters,20)\n", "\n", "# N = 50\n", "Pd50 = Pd(mcenters,50)\n", "Pc50 = Pc(mcenters,50)\n", "\n", "plt.figure(figsize=(8, 5)) # , dpi=80)\n", "plt.plot(mcenters, Pc5, 'b-', label = 'N=5', linewidth=2 )\n", "plt.bar(mcenters, Pd5, color='b', label = 'N=5' )\n", "plt.plot(mcenters, Pc20, 'r-', label = 'N=20', linewidth=2 )\n", "plt.bar(mcenters, Pd20, color='r', label = 'N=20' )\n", "plt.plot(mcenters, Pc50, 'g-', label = 'N=50', linewidth=2 )\n", "plt.bar(mcenters, Pd50, color='g', label = 'N=50' )\n", "\n", "plt.xticks(mcenters)\n", "plt.xlim((mmin, mmax))\n", "plt.xlabel('m')\n", "plt.ylabel('p(m,N)')\n", "plt.legend();"]}, {"cell_type": "markdown", "id": "d0033da0", "metadata": {}, "source": ["## Random walk on a grid\n", "\n", "Consider the <PERSON><PERSON><PERSON> process $\\lbrace{Y_i}\\rbrace_{i\\geq 0}$ with entries\n", "\n", "$$\n", "Y_i = \n", "\\begin{cases}\n", "-1 & \\mathrm{with} \\, p=\\frac{1}{2} \\cr\n", "1 & \\mathrm{with} \\, p=\\frac{1}{2} \n", "\\end{cases}\n", "$$\n", "\n", "The random walk is the sequence $\\lbrace{X_n}\\rbrace_{n\\geq 0}$ with\n", "\n", "$$\n", "X_{n+1} = X_n + Y_n \\, ,\n", "$$\n", "\n", "where $Y_n$ is a <PERSON><PERSON><PERSON> trial.\n", "\n", "The random walk can be also defined as\n", "\n", "$$\n", "X_{n} = X_0 + \\sum_{i=1}^n Y_i \\, ,\n", "$$"]}, {"cell_type": "code", "execution_count": 9, "id": "0b37b290", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/hr/k68z41tx33x2dg658bq2l7p80000gn/T/ipykernel_13586/2555878731.py:11: RuntimeWarning: divide by zero encountered in divide\n", "  P = factorial(N) / factorial(nL) / factorial(nR) * p**nL * (1 - p)**nR\n"]}, {"data": {"image/png": "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**********************************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********************************************************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", "text/plain": ["<Figure size 1000x700 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Grid\n", "mmin  = -75\n", "mmax  =  75\n", "mbins =  1 + mmax-mmin\n", "mcenters = np.linspace(mmin,mmax,mbins)\n", "medges   = np.linspace(mmin-0.5,mmax+0.5,mbins+1)\n", "\n", "\n", "# Number of simulations (replicas)\n", "Nreps  = 1000\n", "\n", "# Number of timesteps per each trajectory\n", "Nsteps    = 10\n", "\n", "# Array of t values\n", "tvalues = np.linspace(1, Nsteps, Nsteps)\n", "\n", "# Arrays of <PERSON><PERSON><PERSON> process and random walk\n", "X   = np.zeros((<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>))\n", "Y   = np.zeros((<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>))\n", "\n", "# Variance\n", "vt  = np.zeros(Nsteps)\n", "\n", "for n in range(Nsteps-1):\n", "  # Draw a random number -1 or 1\n", "  r = 2 * np.random.randint(2, size=(1,Nreps)) - 1\n", "  \n", "  # <PERSON><PERSON><PERSON> process\n", "  Y[n+1,:]  = r\n", "  \n", "  # Random walk    \n", "  X[n+1,:]  = X[n,:] + Y[n+1,:]\n", "  \n", "  # Variance at each timestep\n", "  vt[n+1]   = np.var(X[n+1,:])\n", "    \n", "vart = np.linspace(0, Nsteps-1, Nsteps)\n", "\n", "# Histogram\n", "h  = np.histogram(X[-1,:], bins=medges, density=True)\n", "h0 = h[0]\n", "h0[h0== 0] = float('nan')\n", "    \n", "# Figure\n", "xmin =   np.min(X)\n", "xmax =  -xmin\n", "\n", "fig, ((ax0, ax1), (ax2, ax3)) = plt.subplots(2, 2, figsize=(10, 7))\n", "\n", "ax0.plot(tvalues, Y[:,0], 'ks', linewidth=0.5);\n", "ax0.set_xlabel('timestep')\n", "ax0.set_ylabel('y / unitless')\n", "ax0.set_xlim((0,Nsteps+1))\n", "ax0.set_ylim((-1.5, 1.5))\n", "ax0.set_title('<PERSON><PERSON><PERSON> process')\n", "ax0.set_xticks(tvalues)\n", "ax0.set_yticks((-1,0,1))\n", "\n", "ax1.figure\n", "ax1.plot(tvalues, X, 'o-', linewidth=1);\n", "ax1.set_xlabel('timestep')\n", "ax1.set_ylabel('x / unitless')\n", "ax1.set_xlim((0,Nsteps+1))\n", "#ax1.set_ylim((xmin, xmax))\n", "ax1.set_title('Random walk')\n", "ax1.set_xticks(tvalues)\n", "# ax1.set_yticks(np.linspace(-Nsteps,Nsteps,2*Nsteps+1))\n", "\n", "ax2.bar(mcenters, Pd(mcenters,Nsteps-1), color='r', label = 'Pd(m,N)' )\n", "ax2.bar(mcenters, h0, color='b', label = 'Histogram', width  = 0.5 )\n", "ax2.plot(mcenters, Pc(mcenters,Nsteps-1), 'k-', label = 'Pc(m,N)', linewidth = 2)\n", "\n", "ax2.set_xlim((-Nsteps,Nsteps))\n", "ax2.set_xlabel('m')\n", "ax2.set_title('Probability distribution')\n", "ax2.legend();\n", "# ax2.set_xticks(np.linspace(-Nsteps,Nsteps,2*Nsteps+1))\n", "\n", "ax3.plot(tvalues, vart, 'k', label = 'Exact', linewidth = 2)\n", "ax3.plot(tvalues, vt, 'bs', label = 'Numerical experiment', linewidth = 2)\n", "ax3.set_xlim((0,Nsteps+1))\n", "ax3.set_xlabel('timestep')\n", "ax3.set_ylabel(r'$\\sigma^2(t)$')\n", "ax3.set_title('Variance')\n", "ax3.legend()\n", "ax3.set_xticks(tvalues)\n", "\n", "fig.tight_layout();\n"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}