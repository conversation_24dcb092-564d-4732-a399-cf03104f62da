{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Estimate rates from SqRA rate matrix 1D"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T06:11:52.049504Z", "start_time": "2020-11-13T06:11:51.357462Z"}}, "outputs": [], "source": ["import sys\n", "\n", "#%matplotlib ipympl\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "import matplotlib.cm as cm\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adjacency matrix"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def adjancency_matrix_sparse(nbins, nd, periodic=False):\n", "    v = np.zeros(nbins)\n", "    v[1] = 1\n", "    \n", "    if periodic:\n", "        v[-1] = 1\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.circulant(v)) #.toarray()\n", "    else:\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.toeplitz(v)) #.toarray()\n", "    \n", "    A = A0\n", "    I2 = scipy.sparse.eye(nbins)  #np.eye(nbins)\n", "    for _ in range(1, nd):\n", "        I1 = scipy.sparse.eye(*A.shape) #np.eye(*A.shape)\n", "        A =  scipy.sparse.kron(A0, I1) + scipy.sparse.kron(I2, A)\n", "    return A"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1D system"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 472.441x314.961 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# System parameters\n", "kB    = 0.008314463\n", "T     = 300\n", "mass  = 1\n", "gamma = 1\n", "D     = kB * T / mass / gamma\n", "sigma = np.sqrt(2 * D)\n", "beta  = 1 / kB / T\n", "\n", "# Potential energy function\n", "def V(x):\n", "    return 14*(x**3 - 1.5*x)**2 - x**3 + x\n", "\n", "\"\"\"\n", "def V(x):\n", "    a = 0.06\n", "    #return - 1 / beta * np.log( np.exp( - ( x - 1 ) ** 2 / a ) + np.exp( - ( x + 1 ) ** 2 / a ) )\n", "    return - 1 / beta * np.log( np.exp( - ( x - 0.5 ) ** 2 / a ) + np.exp( - ( x + 0.5 ) ** 2 / a ) +\n", "                                np.exp( - ( x - 1.5 ) ** 2 / a ) + np.exp( - ( x + 1.5 ) ** 2 / a ) )\n", "\"\"\"\n", "\n", "\n", "# Grid\n", "nd     = 1  # Number of dimensions\n", "nedges = 121 # State boundaries\n", "xmin   = -2\n", "xmax   =  2\n", "\n", "x      = np.linspace(xmin, xmax, nedges)  # array with x edges\n", "dx     = x[1] - x[0]\n", "x      = x[:-1] + (dx / 2)                # array with x centers\n", "xbins  = nedges - 1\n", "Nbins  = xbins**nd                        # number of bins\n", "\n", "fig, (ax1) = plt.subplots(1, 1, figsize=(12*in2cm, 8*in2cm), facecolor='white')\n", "###############################################\n", "ax1.plot(x, kB*T*np.ones(x.shape), '--', color='gold')\n", "ax1.text(-1.95, kB*T+0.5, r'$k_B T$', fontsize = 10)\n", "ax1.plot(x, V(x), 'k', label = 'Potential') \n", "\n", "#ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$ / nm')\n", "ax1.set_ylabel(r'$V(x)$ / kJ mol$^{-1}$')\n", "#ax1.legend()\n", "ax1.set_ylim((-1, 18))\n", "ax1.set_xlim((-2, 2))\n", "\n", "\n", "ax1.set_title('Potential energy function')\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "fig.savefig('potential3Well.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Build SqRA 1D"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22112\\815605921.py:33: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  Kevals[:,i] = np.exp(Qevals * tau[i])\n"]}], "source": ["A  = adjancency_matrix_sparse(Nbins, nd, periodic=True)\n", "\n", "# Potential energy of states\n", "v = V(x)\n", "\n", "# Flux\n", "flux = D / dx**2\n", "Af   = flux * A\n", "\n", "# Diagonalization\n", "SQRA = np.sqrt(np.exp(- beta * v))\n", "SQRA = SQRA / sum(SQRA)\n", "Di   = scipy.sparse.spdiags(SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) #.toarray()\n", "D1   = scipy.sparse.spdiags(1/SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)       \n", "Q    = D1 * Af * Di\n", "\n", "Q            = Q + scipy.sparse.spdiags(-Q.sum(axis=1).T, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\n", "Qeval<PERSON>, Qevecs = scipy.sparse.linalg.eigs(Q.T, 6, which='LR')\n", "idx    = np.argsort( - np.real(Qevals))\n", "Qevals = Qevals[idx]\n", "Qevecs = Qevecs[:,idx]\n", "\n", "\n", "<PERSON><PERSON><PERSON>, Kevecs = scipy.sparse.linalg.eigs(Q, 6, which='LR')\n", "idx    = np.argsort( - np.real(Kevals))\n", "Kevals = Kevals[idx]\n", "Kevecs = Kevecs[:,idx]\n", "\n", "tau    = np.linspace(0,1,100)\n", "\n", "Kevals = np.zeros((6,100))\n", "for i in range(100):\n", "    Kevals[:,i] = np.exp(Qevals * tau[i])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\emma\\lib\\site-packages\\matplotlib\\cbook\\__init__.py:1369: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return np.asarray(x, float)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22112\\3008827477.py:27: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22112\\3008827477.py:28: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22112\\3008827477.py:29: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22112\\3008827477.py:30: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22112\\3008827477.py:31: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 787.402x275.591 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(1, 3, figsize=(20*in2cm, 7*in2cm), facecolor='white')\n", "\n", "\n", "ax[0].plot(0, Qevals[0], 'ko');\n", "ax[0].plot(1, <PERSON><PERSON><PERSON>[1], 'bo');\n", "ax[0].plot(2, <PERSON><PERSON><PERSON>[2], 'ro');\n", "ax[0].plot(3, <PERSON><PERSON><PERSON>[3], 'go');\n", "ax[0].plot(4, <PERSON><PERSON><PERSON>[4], 'yo');\n", "ax[0].plot(5, <PERSON><PERSON><PERSON>[5], 'co');\n", "ax[0].set_xticks([0,1,2,3,4,5])\n", "ax[0].set_xlabel(r'$i$') \n", "ax[0].set_ylabel(r'$\\kappa_i$') \n", "ax[0].set_title('Eigenvalues $\\mathcal{Q}$')\n", "\n", "ax[1].plot(tau, Kevals[0,:], 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON>s[1,:], 'b', label = r'$\\lambda_1(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[2,:], 'r', label = r'$\\lambda_2(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[3,:], 'g', label = r'$\\lambda_3(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[4,:], 'y', label = r'$\\lambda_4(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[5,:], 'c', label = r'$\\lambda_5(\\tau)$');\n", "ax[1].set_xlabel(r'$\\tau$ / ps') \n", "ax[1].set_ylabel(r'$\\lambda_i(\\tau)$') \n", "ax[1].set_title(r'Eigenvalues $\\mathcal{K}_{\\tau}$')\n", "ax[1].legend(loc='upper right', fontsize=9)\n", "\n", "#ax[2].plot(tau, - tau / np.log(Kevals[0,:]), 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n", "ax[2].set_xlabel(r'$\\tau$ / ps') \n", "ax[2].set_ylabel(r'$t_i(\\tau)$') \n", "ax[2].set_title(r'Timescales $t_i(\\tau)$')\n", "ax[2].legend(loc='upper right',fontsize=9)\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "fig.savefig('evals3Well.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAr4AAAK8CAYAAAAJT7x+AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAAD/YklEQVR4nOzdd3gU1frA8e+mJ5BCC4QekColEHoRUal2VIqKoMi9qFxBxIIdFLhXRbEAIiKIIk3pVfiJiDSll9ClhJCQAul99/z+mOxKSALZZHcn2X0/zzPP7s7OzryzhDdvzpw5x6CUUgghhBBCCOHk3PQOQAghhBBCCEeQwlcIIYQQQrgEKXyFEEIIIYRLkMJXCCGEEEK4BCl8hRBCCCGES5DCVwghhBBCuAQpfIUQQgghhEuQwlcIIYQQQrgEKXyFEEIIIYRLkMJXCCGEEEK4BCl8hRBCCCGES5DCVwhRIvPnz+e3337TOwwhhBCi2AxKKaV3EEKI8uPHH3/E3d2djIwM6tWrx9GjR2natCm9evXSOzQhhBDipqTwFUJYxWg08uWXXzJv3jx8fX15/vnnGTp0qN5hCSGEELckXR3Kifnz52MwGIpczJeczdudP39e13ivt2TJEm6//XZ8fX0xGAwcPHhQt1h27tzJe++9R2JiYoH3yuJ3VxKO+L4NBoPl0fxcCGch+dY2J<PERSON>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", "text/plain": ["<Figure size 629.921x629.921 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(6, 2, figsize=(16*in2cm, 16*in2cm), facecolor='white')\n", "\n", "\n", "####################################################################################################\n", "y1 = np.zeros(x.shape)\n", "y2 = np.real(Qevecs[:,0])\n", "phi0 = y2 / np.sum( y2*dx)\n", "ax[0,0].plot(x, phi0, 'k-', label= r'$\\varphi_0$');\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,0].set_title(r'Eigenfunctions of $\\mathcal{Q}^*, \\mathcal{P}_{\\tau}$')\n", "ax[0,0].set_ylim(0,1.3)\n", "ax[0,0].legend(loc='upper left')\n", "ax[0,0].set_xticks([])\n", "\n", "y2 = np.real(Kevecs[:,0])\n", "psi0 = y2 / y2\n", "ax[0,1].plot(x, psi0 , 'k-', label= r'$\\psi_0$');\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,1].set_ylim(0,1.3)\n", "ax[0,1].set_title(r'Eigenfunctions of $\\mathcal{Q}, \\mathcal{K}_{\\tau}$')\n", "ax[0,1].legend(loc='upper left')\n", "ax[0,1].set_xticks([])\n", "\n", "####################################################################################################\n", "phi1 = np.real(Qevecs[:,1])\n", "ax[1,0].plot(x, phi1 , 'k-', label= r'$\\varphi_1$');\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,0].legend(loc='upper left')\n", "ax[1,0].set_xticks([])\n", "ax[1,0].set_ylim(-0.4,0.4)\n", "\n", "psi1 =  np.real(-Kevecs[:,1])\n", "ax[1,1].plot(x, psi1 , 'k-', label= r'$\\psi_1$');\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,1].legend(loc='upper left')\n", "ax[1,1].set_xticks([])\n", "ax[1,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi2 = -np.real(Qevecs[:,2])\n", "ax[2,0].plot(x, phi2 , 'k-', label= r'$\\varphi_2$');\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,0].legend(loc='upper left')\n", "ax[2,0].set_xticks([])\n", "ax[2,0].set_ylim(-0.4,0.4)\n", "\n", "psi2 = np.real(Kevecs[:,2])\n", "ax[2,1].plot(x, psi2 , 'k-', label= r'$\\psi_2$');\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,1].legend(loc='upper left')\n", "ax[2,1].set_xticks([])\n", "ax[2,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi3 = np.real(Qevecs[:,3])\n", "ax[3,0].plot(x, phi3 , 'k-', label= r'$\\varphi_3$');\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,0].legend(loc='upper left')\n", "ax[3,0].set_xticks([])\n", "ax[3,0].set_ylim(-0.4,0.4)\n", "\n", "psi3 = np.real(Kevecs[:,3])\n", "ax[3,1].plot(x, psi3 , 'k-', label= r'$\\psi_3$');\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,1].legend(loc='upper left')\n", "ax[3,1].set_xticks([])\n", "ax[3,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi4 = np.real(Qevecs[:,4])\n", "ax[4,0].plot(x, phi4 , 'k-', label= r'$\\varphi_4$');\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,0].legend(loc='upper left')\n", "ax[4,0].set_xticks([])\n", "ax[4,0].set_ylim(-0.4,0.4)\n", "\n", "psi4 = - np.real(Kevecs[:,4])\n", "ax[4,1].plot(x, psi4 , 'k-', label= r'$\\psi_4$');\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,1].legend(loc='upper left')\n", "ax[4,1].set_xticks([])\n", "ax[4,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi5 = np.real(Qevecs[:,5])\n", "ax[5,0].plot(x, phi5 , 'k-', label= r'$\\varphi_5$');\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,0].legend(loc='upper left')\n", "ax[5,0].set_xticks([])\n", "ax[5,0].set_ylim(-0.4,0.4)\n", "ax[5,0].set_xlabel(r'$x$ / nm')\n", "\n", "psi5 = - np.real(Kevecs[:,5])\n", "\n", "ax[5,1].plot(x, psi5 , 'k-', label= r'$\\psi_5$');\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,1].legend(loc='upper left')\n", "ax[5,1].set_xticks([])\n", "ax[5,1].set_ylim(-0.4,0.4)\n", "ax[5,1].set_xlabel(r'$x$ / nm')\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.1)\n", "fig.savefig('evecs3Well.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: -232.717318\n", "         Iterations: 184\n", "         Function evaluations: 318\n"]}], "source": ["from pcca import pcca\n", "\n", "chi = pcca(<PERSON><PERSON>to<PERSON>(), 3)[0]\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1102.36x314.961 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = plt.figure(figsize=(28*in2cm, 8*in2cm))\n", "\n", "ax = fig.add_subplot(1,3,1,projection='3d')\n", "ax.plot(psi0, psi1, psi2, 'k.')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$\\psi_0$',labelpad=-10)\n", "ax.set_ylabel(r'$\\psi_1$',labelpad=-10)\n", "ax.set_zlabel(r'$\\psi_2$',labelpad=-10)\n", "ax.set_xticks([0.9,1.1])\n", "ax.set_yticks([-0.1,0.1])\n", "ax.set_zticks([-0.1,0.1])\n", "ax.set_title(r'2-simplex $\\psi_0, \\psi_1, \\psi_2$')\n", "\n", "\n", "ax = fig.add_subplot(1,3,2,projection='3d')\n", "ax.plot(chi[:,0], chi[:,1], chi[:,2], 'k.')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$\\chi_0$',labelpad=-10)\n", "ax.set_ylabel(r'$\\chi_1$',labelpad=-10)\n", "ax.set_zlabel(r'$\\chi_2$',labelpad=-10)\n", "ax.set_xticks([0,1])\n", "ax.set_yticks([0,1])\n", "ax.set_zticks([0,1])\n", "ax.set_title(r'2-simplex $\\chi_0, \\chi_1, \\chi_2$')\n", "\n", "ax = fig.add_subplot(1,3,3)\n", "ax.plot(x, chi[:,0],  'b', label=r'$\\chi_0$')\n", "ax.plot(x, chi[:,1],  'r', label=r'$\\chi_1$')\n", "ax.plot(x, chi[:,2],  'g', label=r'$\\chi_2$')\n", "ax.legend()\n", "ax.set_xlabel(r'$x$')\n", "ax.set_title('Membership functions')\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.08)\n", "fig.savefig('memFuncs3Well.png', format='png', dpi=900, bbox_inches='tight')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}