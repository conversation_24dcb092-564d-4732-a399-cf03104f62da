{"cells": [{"cell_type": "markdown", "id": "a736f389", "metadata": {}, "source": ["# Square Root Approximation of the Fokker-Planck Equation"]}, {"cell_type": "code", "execution_count": null, "id": "8a9c7ecf", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "from scipy.spatial import ConvexHull, convex_hull_plot_2d\n", "\n", "import clippedVoronoi\n", "from sqra_functions import generate_random_voronoi, generate_grid_voronoi, adjacency_random_voronoi, adjacency_matrix_grid, sqra_random_voronoi, sqra_grid_voronoi\n", "from scipy.cluster.vq import vq, kmeans, whiten\n", "from scipy.spatial import Delaunay, delaunay_plot_2d\n", "import matplotlib.cm as cm\n", "\n", "import itertools\n", "from scipy.sparse.linalg import expm, expm_multiply\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches\n", "\n", "adjacency_matrix_grid(10, 20, periodic=False)"]}, {"cell_type": "code", "execution_count": 2, "id": "714aa671", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.colorbar.Colorbar at 0x1eb156c2700>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 629.921x629.921 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Potential energy function\n", "def V(x, y):\n", "    #return (x**2 - 1)**2 + 0.5*y * x + y**2     # kJ mol-1\n", "    return 3 * np.exp(-x ** 2 - (y - 1/3) ** 2) \\\n", "    - 3 * np.exp(-x ** 2 - (y - 5/3) ** 2) \\\n", "    - 5 * np.exp(-(x - 1) ** 2 - y ** 2) \\\n", "    - 5 * np.exp(-(x + 1) ** 2 - y ** 2) \\\n", "    + 0.2 * x ** 4 \\\n", "    + 0.2 * (y - 1/3) ** 4\n", "    \n", "# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "gamma = 1           # ps-1\n", "\n", "D     = kB * T / mass / gamma # nm2 ps-1\n", "sigma = np.sqrt(2 * D)\n", "beta  = 1 / kB / T\n", "\n", "# Grid\n", "nd     =  2  # Number of dimensions\n", "xbins  = 60  # x boundaries\n", "xmin   = -3.4\n", "xmax   =  3.4\n", "\n", "ybins  = 60  # y boundaries\n", "ymin   = -3.4\n", "ymax   =  3.4\n", "\n", "\n", "xedges = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx     = xedges[1] - xedges[0]\n", "x      = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins  = xbins - 1\n", "\n", "yedges = np.linspace(ymin, ymax, ybins)  # array with y edges\n", "dy     = yedges[1] - yedges[0]\n", "y      = yedges[:-1] + (dy / 2)                # array with y centers\n", "ybins  = ybins - 1\n", "\n", "Nbins  = xbins*ybins                      # number of bins\n", "\n", "grid = np.meshgrid(x,y)\n", "\n", "\n", "fig, ax1 = plt.subplots(1, 1,figsize=(16*in2cm, 16*in2cm))  \n", "\n", "pos = ax1.pcolor(grid[0], grid[1], V(grid[0], grid[1]), shading='auto', vmax=5)\n", "ax1.set_title('Potential')\n", "ax1.set_aspect('equal', 'box')\n", "ax1.set_xlabel('x')\n", "ax1.set_ylabel('y')\n", "fig.colorbar(pos, ax=ax1)"]}, {"cell_type": "code", "execution_count": 4, "id": "5d9677c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of cells:  16\n", "Number of cells:  25\n", "Number of cells:  49\n", "Number of cells:  81\n", "Number of cells:  121\n", "Number of cells:  169\n"]}], "source": ["Ncellsarray = np.array([16,25, 49, 81, 121, 169])\n", "\n", "kappa      = np.zeros((len(Ncellsarray), 6))\n", "kappa_grid = np.zeros((len(Ncellsarray), 6))\n", "\n", "\n", "for N, Ncells in enumerate(Ncellsarray):\n", "    print(\"Number of cells: \", Ncells)\n", "\n", "    xcenters, ycenters, vor                        = generate_random_voronoi(<PERSON><PERSON><PERSON>, xmin, xmax, ymin, ymax)\n", "    xcenters_grid, ycenters_grid, dx, dy, vor_grid = generate_grid_voronoi(Ncells, xmin, xmax, ymin, ymax)\n", "\n", "    A, Vol, h, S                           = adjacency_random_voronoi(Ncells, vor)\n", "    A_grid                                 = adjacency_matrix_grid(int(np.sqrt(Ncells)), 2, periodic=False)\n", "\n", "    Q      = sqra_random_voronoi(Ncells, xcenters, ycenters, beta, D, V, A, Vol, h, S)\n", "    Q_grid = sqra_grid_voronoi(Ncells, xcenters_grid, ycenters_grid, dx, dy, beta, D, V, A_grid)\n", "\n", "    # Number of eigenvectors\n", "    Nevecs = 6\n", "\n", "    # Eigenvalue problem\n", "    evals, evecs = scipy.sparse.linalg.eigs(Q.T, Nevecs, which='LR')\n", "    evals = np.real(evals)\n", "    kappa[N,:] = evals\n", "\n", "    # Eigenvalue problem\n", "    evals_grid, evecs_grid = scipy.sparse.linalg.eigs(Q_grid.T, Nevecs, which='LR')\n", "    evals_grid = np.real(evals_grid)\n", "    kappa_grid[N,:] = evals_grid\n"]}, {"cell_type": "code", "execution_count": 5, "id": "42fa209f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(-8.0, 0.1)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(<PERSON><PERSON><PERSON><PERSON>, kappa[:,0], 'k', label=r'$\\kappa_0$')\n", "\n", "plt.plot(<PERSON><PERSON><PERSON><PERSON>, kappa[:,1], 'b', label=r'$\\kappa_1$, Random')\n", "plt.plot(N<PERSON><PERSON>ray, kappa_grid[:,1], 'b--', label=r'$\\kappa_1$, Regular grid')\n", "plt.plot(<PERSON><PERSON><PERSON><PERSON>, kappa[:,2], 'r', label=r'$\\kappa_2$, Random')\n", "plt.plot(<PERSON><PERSON><PERSON>ray, kappa_grid[:,2], 'r--', label=r'$\\kappa_2$, Regular grid')\n", "plt.plot(<PERSON><PERSON><PERSON><PERSON>, kappa[:,3], 'g', label=r'$\\kappa_3$, Random')\n", "plt.plot(<PERSON><PERSON><PERSON>ray, kappa_grid[:,3], 'g--', label=r'$\\kappa_3$, Regular grid')\n", "plt.xlabel(r'$N$ cells')\n", "plt.ylabel(r'$\\kappa_i(N)$')\n", "plt.title('First four eigenvalues')\n", "plt.legend()\n", "plt.ylim((-8,0.1))"]}, {"cell_type": "code", "execution_count": null, "id": "86c63e9d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}