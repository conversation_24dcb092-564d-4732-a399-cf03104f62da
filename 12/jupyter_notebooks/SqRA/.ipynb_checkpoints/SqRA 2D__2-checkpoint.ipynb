{"cells": [{"cell_type": "markdown", "id": "a736f389", "metadata": {}, "source": ["# Square Root Approximation of the Fokker-Planck Equation"]}, {"cell_type": "code", "execution_count": 1, "id": "8a9c7ecf", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "from scipy.spatial import ConvexHull, convex_hull_plot_2d\n", "\n", "import clippedVoronoi\n", "from sqra_functions import adjacency_matrix_grid\n", "from scipy.cluster.vq import vq, kmeans, whiten\n", "from scipy.spatial import Delaunay, delaunay_plot_2d\n", "import matplotlib.cm as cm\n", "\n", "import itertools\n", "from scipy.sparse.linalg import expm, expm_multiply\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "714aa671", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.colorbar.Colorbar at 0x24aefa58e20>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAhsAAAH+CAYAAAA1RswRAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAABEG0lEQVR4nO3df3RU9Z3/8dedSTIJJDMIkV8SfihVUcQqeFpcdYNWCm2teFZWuy4Vi35XBU+VU9dizwK6xbitx2rtMSvdFqzbFnUV9biVBVuB9liKsbJSXDmCP5ISfgklCYHMJHPv9w/K1JT7+UDC3DszmefjnHuOuZ+5n/uZmfsZP7zv530/jud5ngAAAAISyXUDAABA38ZgAwAABIrBBgAACBSDDQAAECgGGwAAIFAMNgAAQKAYbAAAgEAx2AAAAIFisAEAAALFYAMAAASKwQYAAEVk8eLFchyn2zZ06NBAz1kSaO0AACDvnHvuuXr11Vczf0ej0UDPx2ADAIAiU1JSEng0o9v5QjsTAABFqqOjQ6lUKrD6Pc+T4zjd9sViMcViMd/Xv/feexo+fLhisZg+85nP6IEHHtDpp58eWPsclpgHACA4HR0dGjOqUrv2pAM7R2VlpQ4ePNht36JFi7R48eJjXvvKK6/o0KFDOvPMM7V79259+9vf1rvvvqstW7Zo0KBBgbSPwQYAAAFqbW1VIpHQR2+OVrwq+3kZrW2uRk38UE1NTYrH45n9tsjGJ7W3t+uMM87QP//zP2v+/PlZb5/EbRQAAEJRWeWosso5/gt7yNWROuPxeLfBxonq37+/zjvvPL333nvZbloGqa8AABSxZDKp//u//9OwYcMCOweRDQAAQpD2XKUDmLiQ9twevf4b3/iGrrrqKo0cOVJ79uzRt7/9bbW2turGG2/MfuP+jMEGAABF5I9//KO+8pWv6OOPP9app56qz372s9qwYYNGjRoV2DkZbAAAEAJXnlxlP7TR0zpXrFiR9TYcD3M2AABAoIhsAAAQAleueja74sTrzXcMNgAACEHa85QO4NFWQdSZbdxGAQAAgSKyAQBACPJlgmguENkAAACBIrIBAEAIXHlKE9kAAADIPiIbAACEgDkbAAAAASGyAQBACHjOBgAAQECIbAAAEAL3z1sQ9eY7IhsAACBQRDYAAAhBOqDnbARRZ7YR2QAAAIEisgEAQAjS3pEtiHrzHZENAAAQKCIbAACEoJizURhsAAAQAleO0nICqTffcRsFCNHy5cvlOE5mKykp0YgRI3TTTTdpx44dParr8ccf1/Lly4Np6F95/fXXtXjxYh04cOCYstraWtXW1vaq3pM5FkDhILIB5MCyZct09tln6/Dhw1q/fr3q6uq0bt06bd68Wf379z+hOh5//HFVV1dr9uzZwTZWRwYb9913n2bPnq0BAwYc0w4Ax+d6R7Yg6s13DDaAHBg/frwmTZokSZoyZYrS6bT+9V//VS+88IJuuOGGHLeuZ84555xcNwFAnuM2CpAHPvvZz0qSPvroI3V0dGjBggUaM2aMysrKdNppp2nu3LndbmGMHj1aW7Zs0bp16zK3ZEaPHp0pb21t1Te+8Y1uddx5551qb2/vdl7HcTRv3jw99dRTGjdunPr166fzzz9fL7/8cuY1ixcv1t133y1JGjNmTOZ8a9euleR/K+S+++7TZz7zGQ0cOFDxeFwXXnihfvSjH8krgAWjgKCk/zxnI4gt3xHZAPLAtm3bJEmnnnqqZsyYoV/+8pdasGCBLr30Ur399ttatGiRfvvb3+q3v/2tYrGYVq5cqWuvvVaJRCJzGyMWi0mSDh06pL/927/VH//4R917772aMGGCtmzZooULF2rz5s169dVX5Th/+XH67//+b73xxhu6//77VVlZqe985zu65pprtHXrVp1++um6+eabtX//fj322GN6/vnnNWzYMEn2iMaHH36of/qnf9LIkSMlSRs2bNAdd9yhHTt2aOHChYF8hgDyF4MNIAfS6bS6urrU0dGhdevW6dvf/raqqqoUj8f1P//zP/rOd76TiSZceeWVqqmp0XXXXaef/OQnuuWWW3TBBReooqJC8Xg8ExU56vvf/77efvtt/e53v8vcqrniiit02mmn6dprr9WqVas0ffr0zOsPHz6sV199VVVVVZKkCy+8UMOHD9czzzyjb37zmxoxYkRm0HDBBRd0i6CYLFu2LPPfruuqtrZWnufp0Ucf1b/8y790G+wAxSKoKEQhRDa4jQLkwGc/+1mVlpaqqqpKX/rSlzR06FC98sor+v3vfy9Jx0z6nDlzpvr3769f/vKXx6375Zdf1vjx4/XpT39aXV1dme3zn/98t9sfR02ZMiUz0JCkIUOGaPDgwfroo496/f5+9atf6XOf+5wSiYSi0ahKS0u1cOFC7du3T3v27Ol1vQAKE5ENIAd+8pOfaNy4cSopKdGQIUMytyaWLVumkpISnXrqqd1e7ziOhg4dqn379h237t27d2vbtm0qLS31Lf/444+7/T1o0KBjXhOLxXT48OETfTvdbNy4UVOnTlVtba1++MMfasSIESorK9MLL7ygJUuW9LpeoNC5niPXC+A5GwHUmW0MNoAcGDduXOYWxycNGjRIXV1d2rt3b7cBh+d52rVrly666KLj1l1dXa2Kigr9+Mc/NpYHacWKFSotLdXLL7+s8vLyzP4XXngh0PMCyF/cRgHyyBVXXCFJ+s///M9u+5977jm1t7dnyiVz9OFLX/qStm/frkGDBmnSpEnHbCcy5+KvHZ18eiJRiaMPK4tGo5l9hw8f1lNPPdXj8wJ9STFnozDYAPLIlVdeqc9//vO65557dN999+nVV1/Vww8/rJtuukkXXHCBZs2alXnteeedp//93//V008/rTfeeEObN2+WJN15550666yzdNlll+nhhx/Wq6++qtWrV+s//uM/9Pd///f63e9+1+N2nXfeeZKkRx99VL/97W/V0NCgtrY239d+8Ytf1MGDB/UP//APWrNmjVasWKFLL700M2ABUHy4jQLkEcdx9MILL2jx4sVatmyZlixZourqas2aNUsPPPBAt/9h33fffdq5c6duueUWtbW1adSoUfrwww/Vv39//frXv9aDDz6opUuX6oMPPlBFRYVGjhypz33uc72KbNTW1mrBggV68skn9cMf/lCu6+q1117zfdT45Zdfrh//+Mf6t3/7N1111VU67bTTdMstt2jw4MGaM2fOSXw6QGFLK6J0AP/GT2e9xuxzPJ6yAwBAYFpbW5VIJPSrP9Sosir7g42Dba4uH9+klpYWxePxrNefDUQ2AAAIgRdQNopXANkozNkAAACBIrIBAEAIeIIoAABAQIhsAAAQgrQXUdoLIBulANI8GGwAABACV47cAG4ouMr/0UZRDTZc11Vzc7OqqqpYdRIAIM/z1NbWpuHDhysSYWZBUIpqsNHc3KyamppcNwMAkGeampo0YsSIQM9RzBNEi2qwcXQZ7Y9+P1rxSkawAFDsWg+6GnXhh5n/PyAYRTXYOHrrJF4ZUbwqepxXAwCKRRi31oObIJr/czb45z0AAEWqrq5OjuPozjvvDPQ8RRXZAAAgV45ko2Q/gtLbOt944w0tXbpUEyZMyHKLjkVkAwCAInPw4EHdcMMN+uEPf6hTTjkl8PMx2AAAIATun5eYz/Z29Nkdra2t3bZkMmlsy9y5c/XFL35Rn/vc50J57ww2AADoA2pqapRIJDJbXV2d7+tWrFih3//+98byIDBnAwCAEASdjdLU1KR4PJ7ZH4vFjnltU1OTvv71r2v16tUqLy/PeltMGGwAANAHxOPxboMNP2+++ab27NmjiRMnZval02mtX79eP/jBD5RMJhWNZv/REAw2AAAIgfuJ+RXZrffEn7NxxRVXaPPmzd323XTTTTr77LN1zz33BDLQkBhsAABQNKqqqjR+/Phu+/r3769BgwYdsz+bGGwAABCCtOco7QWwNkoAdWYbgw0AAIrY2rVrAz8Hgw0AAEJw9LkY2a83/9dGYbABAEAIXC8iN4DUV5eF2AAAQLEjsgEAQAiK+TYKkQ0AABCoghls1NfXa8KECZknpE2ePFmvvPJKrpsFAMAJcfWX9Ndsbm6u39gJKJjBxogRI/Tggw+qoaFBDQ0Nuvzyy3X11Vdry5YtuW4aAACwKJg5G1dddVW3v5csWaL6+npt2LBB5557bo5aBQDAiQnuceX5HzcomMHGJ6XTaT377LNqb2/X5MmTja9LJpNKJpOZv1tbW8NoHgAA+ISCGmxs3rxZkydPVkdHhyorK7Vy5Uqdc845xtfX1dXpvvvuC7GFAAD4C26J+fyPbOR/Cz/hrLPO0qZNm7RhwwbddtttuvHGG/XOO+8YX79gwQK1tLRktqamphBbCwAApAKLbJSVlWns2LGSpEmTJumNN97Qo48+qieeeML39bFYTLFYLMwmAgDgy5UjV9lfNC2IOrOtoCIbf83zvG5zMgAAQP4pmMjGvffeq+nTp6umpkZtbW1asWKF1q5dq1WrVuW6aQAAHFcxz9komMHG7t27NWvWLO3cuVOJREITJkzQqlWrdOWVV+a6aQAAwKJgBhs/+tGPct0EAAB6Lbi1UfI/spH/LQQAAAWtYCIbAAAUMtdz5HoBZKMEUGe2EdkAAACBIrIBAEAI3IDmbLA2CgAAkCS5XkRuAGmqQdSZbfnfQgAAUNCIbAAAEIK0HKUDeLR4EHVmG5ENAAAQKCIbAACEgDkbAAAAASGyAeRY0us07O8yHtMp11iW9jzf/eYjzP/qiDrme8Glln+rxBz/n5aYU2ppBdC3pRXM/Ip01mvMPiIbAAAgUEQ2AAAIAXM2AAAAAkJkAwCAEKS9iNIBRCGCqDPb8r+FAACgoBHZAAAgBJ4cuQFko3gF8ARRBhvoE1xLYufH6UPGshbDYQfcmPGYVrfcWNZmKGtzK4zHtBvOlXTNaaIdnrnrmiaL2UKtUcf/g4gY9ktSuWNOzY1F/NN5+0eSxmOqIoctZR2+++OG/ZI0wHCuhCWeWx3tZyyLEAgGeo3BBgAAIWDOBgAAQECIbAAAEALXc+R62Z9fEUSd2cZgAwCAEKQVUTqAGwpB1Jlt+d9CAABQ0IhsAAAQAm6jAHlkV/qgsWxHusz/mK648Zi9XUONZfvT/X33t3SZUyAPdJrLDhrad7DTnC7bkfbvhqb9ktSZjhrLurI4M73EkvpaGjWvNVke9U+LNe2XpMpScxprZTTlu39AqTmtOVHiXzYw2m485tSSVmPZUEPZaYa2SdLQaKWxDCgmDDYAAAiBq4jcAGYvBFFntuV/CwEAQEFjsAEAQAjSnhPY1hP19fWaMGGC4vG44vG4Jk+erFdeeSWgd30Egw0AAIrIiBEj9OCDD6qhoUENDQ26/PLLdfXVV2vLli2BnZM5GwAAhCBfslGuuuqqbn8vWbJE9fX12rBhg84999xsNi2DwQYCtc81z/x/r9N/AbKmztOMxzR1DvTdvydlzkbZ3+mfcSJJf0r5Z5a0pswLsR20lB3u9F88LZmyZJZ0+meWuF3mwKOtTNn8MXM8Y1GkxJypYiorLTVnsMTKzJkqFaX+C7tVlpkXdosbyk4pM2ewDCw1X6+Dy/yzUWpK9xuPqSndZyz7VKl/+wZFzNcrYNPa2v0ajcViisXMv1eSlE6n9eyzz6q9vV2TJ08OrG3cRgEAIASeF5EbwOb9Od29pqZGiUQis9XV1RnbsnnzZlVWVioWi+nWW2/VypUrdc455wT23olsAADQBzQ1NSke/0uU1xbVOOuss7Rp0yYdOHBAzz33nG688UatW7cusAEHgw0AAEKQlqO0sj9n42idR7NLTkRZWZnGjh0rSZo0aZLeeOMNPfroo3riiSey3j6J2ygAABQ9z/OUTJrnQJ0sIhsAAITA9YJZx8Q1z+P2de+992r69OmqqalRW1ubVqxYobVr12rVqlVZb9tRDDYAACgiu3fv1qxZs7Rz504lEglNmDBBq1at0pVXXhnYORls4KTtSLcZy95JnWIsey/pv0BaY3KQ8ZidHQnf/fuTloXTkhXGsoNJ/4XTDhv2S1Jnh7nbeCnDAmkp8x1LJ+X/Lx2ny/wvoBJzlqicdPb+5eRFzf9kci2/HukS/+O6ysz1HS4zp9K2lPmnzJaWW9JlY/4LpFXGqozHDIgdNpbtjPlfe3vKLYsAWs7V5u7y3X9O2Z+Mx5wWNdeH/Hc0eySIenviRz/6UdbbcDzM2QAAAIEisgEAQAhcOXIDyEYJos5sY7ABAEAIerNo2onWm++4jQIAAAJFZAMAgBDkywTRXMj/FgIAgIJGZAMnzLSCqy299Z2OEcay9w+f6rv/j4cHmNvQ4Z/i2nK43HjM4Q5zGmvqsP8qreowpLBKcjrMY/SoIY012mG+pxrxX9BUEf/MzSNlttRXUwap7cE/huZ5EXO7bamvbpn/ca7h45akdLllldsy/+8jVW5uRKrc/2SHK8zXw8Fyc1lbhf86Ewe7LKsApy0p1F7Pf37LYy2++1kptjC4CmiJ+QKYIEpkAwAABIrIBgAAIfACSn31iGxkT11dnS666CJVVVVp8ODBmjFjhrZu3ZrrZgEAgOMomMHGunXrNHfuXG3YsEFr1qxRV1eXpk6dqvZ2/3kEAADkE9dzAtvyXcHcRvnr1eiWLVumwYMH680339Rll12Wo1YBAIDjKZjBxl9raTkyK3vgwIHG1ySTSSWTyczfra2tgberL3u/039m/fbUEPMxhowTSWo85J/FsudQpfGYA+3+i6olTVklktKWhdOcw/5ZDrbskehhS1myZ/slKWrIOul1NkraP+3EsWSjmP5h5EV7m43iv9+SnKG05TNPx/zL0obsnyNl/oHbZKc5oNtlWkhPUqrT/w13ps3H9OZfnDFTepKkARH/heIGmRNikEd4zkaB8TxP8+fP1yWXXKLx48cbX1dXV6dEIpHZampqQmwlAACQCnSwMW/ePL399tv6+c9/bn3dggUL1NLSktmamppCaiEAAN0xZ6OA3HHHHXrppZe0fv16jRhhfmCUJMViMcVixBcBAMilghlseJ6nO+64QytXrtTatWs1ZsyYXDcJAIATxhLzBWDu3Ln62c9+phdffFFVVVXatWuXJCmRSKiiwn/SIAAAyL2CGWzU19dLkmpra7vtX7ZsmWbPnh1+gwAA6IGg5lcwZyOLPM+2ihSyZU/6oLGssWuY7/4dSfNCbLs64sayjw/7Lx5lSm+VpI5D/rmT3iHzpRw9bJ4HHTGUlXQYD1HUVmZKfbUe439tm1JiJSnSZe4PxrRYWx9yDAunlZiPcUssaaeGFNeIIYVVsqfzmsoiaXN9TpfhPaXN53Et9XUYftAPmKtTxJJvXBbxb0i8xD+9VZJOLfFP3x9VstN4zOCoOZUc4SrmwUZBZqMAAIDCUTCRDQAAChmRDQAAgIAQ2QAAIARENgAAAAJCZAPd7LYsKrW7M+G7f2+qynjM/qQ5s6T1cLnvftuiau5h/0vWlnESPWQpM2SJRM0JASqxLapmqK+kw5yVEE0ZslEMWSqSPRvFMZXZErpMC7FZMk5s2SimLBFDAsaRJriWzBLX1Ahzfab3m+7lolVpx//aS1oyTlqj/te4JJWX+C+4trfM3J92l/n3wd2le4zHDDZ3aYTMUzAP4CqEXE0iGwAAIFBENgAACAFzNgAAAAJCZAMAgBAQ2QAAAAgIkQ0AAEJQzJENBhvoZp/bz1i2v8t/Qac/pczHtCXNqX8dSf8UVzdpvixNC6eZ9kvmxdEkS6pqL445ci7TomqW1FfDMZFOS+qrpcxxs5j62ovU0t6y/1724sfUdIilKs8xX0em9rlR8/XaUWr+ANvK/PvGn2I974O2flsYiZHo6xhsAAAQgmKObDBnAwAABIrIBgAAIfA8R14AUYgg6sw2IhsAACBQRDYAAAiBKyeQtVGCqDPbGGwAABCCYp4gymCjCB10zbmbB9KDjGV/6vJPr2vtjBmPaU+WGcs6OwyXX4f57l4k6d+poinjIYrY0lgNx0Vs9VnSTqP+C3kqYtgvSU7avz7TfkmSZysznMdyjGfMfbWk2NpWcDV8hZFO849i1HJT14v4t8OL9Lw+26Kv1jZE/c+VLjEf1Fli/oltL/PvG7b+ZOqDB9Lm1NeD7j5jWWXEnJoOZBODDQAAQsAEUQAAgIAQ2QAAIATFPGeDyAYAAEWkrq5OF110kaqqqjR48GDNmDFDW7duDfScDDYAAAjB0TkbQWw9sW7dOs2dO1cbNmzQmjVr1NXVpalTp6q9vT2gd85tlKJ00Osylh1I9zeWtXX5z1xv77RknHRGjWVeyn+sGzXsl6SoKRvFlnFiKTNlndiyRyLmj8+yCFovFsOyZFrYqzNkbvQiF9+W7WFrn7lC2wJyllN1+Z/LllliKjMklfz5RLb6DG0oNR+UtlzLpr5h60+mPmjrtwe93cYy/2Xd0NetWrWq29/Lli3T4MGD9eabb+qyyy4L5JwMNgAACIEX0JyNo5GN1tbWbvtjsZhiMXMq9VEtLS2SpIEDB2a9bUdxGwUAgD6gpqZGiUQis9XV1R33GM/zNH/+fF1yySUaP358YG0jsgEAQAg89e6O6onUK0lNTU2Kx+OZ/ScS1Zg3b57efvtt/eY3v8l+wz6BwQYAAH1APB7vNtg4njvuuEMvvfSS1q9frxEjRgTYMgYbAACEwpUjJw8WYvM8T3fccYdWrlyptWvXasyYMVlv019jsAEAQBGZO3eufvazn+nFF19UVVWVdu3aJUlKJBKqqKgI5JwMNopQu+WeYZtrXpipvcs/Je9wZ6nxmK6UOfXVMaQFmhZbkyypqr1YbM1WFukyf0gR2wJkljITU0qla1pRTZJjTYvN4r+cLFXZTmNNmTWdyvLZRUyLqlm+JzmGNljbbWmD4VK2Xa9umblCU9+w9SdTH7T22wDmCKB38mVtlPr6eklSbW1tt/3Lli3T7Nmzs9Sq7hhsAABQRLwgZqkeB4MNAABC4HqOnCJdG4XBBgAAIfC8gFJfC+BWGQ/1AgAAgSKyAQBACPJlgmguMNgoQodcc4bIIde8CNQhw0z4jpR59rxryUaJmBZi68WiaraME1MGy5Hj/OOP1sXW0paYpanIlBkhyYsYFk6zZXRY6gtNb2K3tnbbEksMn3nUklriGNrnWdoQtS3EZriUPcuvqBszV2jqG7b+ZOqD1n5r6e9AWBhsAAAQgmKObDBnAwAABIrIBgAAISjm1FciGwAAIFBENgAACAHP2QAAAAgIkY0i1GHJ1TuUjpnLuvxT8jo7Lal1SUvqa4f/fUZb6mtJh//+qGH/kWPMw/5op3+ZY0t9dXv+zwhrGquhzJRqebz6sroOmy0d1bWUmRZVs9ZnWXjOcC7PtiieqX3Wr8+WbmyozvIrmjZc45Lklvt/wbb+ZOqDtn5r6+8I15HIRhDZKFmvMusKKrKxfv16XXXVVRo+fLgcx9ELL7yQ6yYBAIDjKKjBRnt7u84//3z94Ac/yHVTAADokaPP2Qhiy3cFFV+bPn26pk+fnutmAACAHiiowUZPJZNJJZN/mQDQ2tqaw9YAAIqZp+NMGTqJevNdQd1G6am6ujolEonMVlNTk+smAQBQdPr0YGPBggVqaWnJbE1NTbluEgCgSDFno4+KxWKKxcwpYcWq3TOvEHnQkkJ32JB212VZ2dVJWVbYNK3geth4iLGs9LAlvbUXqa+9zSUzpaR6vVhN1C2xpE3a0mKzuMinMYVVUiRtbl/EENi1pxTbykzfh+V76vRvn7UNvUiLtX0XUXNXU9rQN2z9ydQHbf3W1t8LIwCPvqBPDzYAAMgbRTxpo6AGGwcPHtS2bdsyf3/wwQfatGmTBg4cqJEjR+awZQAAHEdQtzy4jZJdDQ0NmjJlSubv+fPnS5JuvPFGLV++PEetAgAANgU12KitrZVXCM9lBQDgr7AQGwAAQEAKKrKB7Ojw/Ge0S9LhtKWs07/MtSwcFU1aslEMi6eZFluTzFknpe3mVIZoh7nMSWd3UTXTR2vLRnGdnmc5uKWWNhiOs93WNWVhONZ/jlgWTjNksUQsi61FLN+FY1owrxeL4kUsGVKRtO0N+5d5lqwhS5KIIoa+kbb0J1MftPVbW3+XUpYyZFtQaaqFkPpKZAMAAASKyAYAAGHwnGAyR4hsAACAYkdkAwCAEJCNAgAAEBAiGwAAhIHHlaOYtLuWRZu6zGWpTsPlkjQHyKId5olLphTXkkPmnmNKcS1pN68YFu0wl/UmddIrMb/ftCHv1I32fAJXbxZvkyTX8DX1JvXVFvr0LIu0Gc9jWWwtkrIsmJf0P5nTZanQwJa67KR7voqdG7VcDzHzuboMfSNt6U+mPmjrt7b+TuorwsJgAwCAEPCcDQAAgIAQ2QAAICwFML8iCEQ2AABAoIhsAAAQgmKes8FgAwCAMJD6imLS4VpWiLSsHpk0pN05KUvqqyWzruSw//5SW+rrQf8UyJKDncZjIskucyNMK43aVmmN2VbRNBxjWaXVcf3LTOmokj2N1ZgyazvGcK7epMtK5hTXSFfP01slKWJIX44kzd+7TFmxljTkiCm9W+b369pWfS23rHps6Bu2/mTqg7Z+a+vvQFgYbAAAEApH1lH/SdWb35ggCgAAAkVkAwCAMBTxnA0iGwAAIFAMNgAACIMX4NYD69ev11VXXaXhw4fLcRy98MILJ/nGjo/bKH1Yp+efhZH0bNko5ksi3eU/NnU6LTPuDYutHSnz7yGlh8yLa5W0+7+n6MGk8RjnsCUlxpSGETGPwx1TBoskOWX+1ZWZ64uUGbJRLIt42TJBjD88tjlkhmN6k3EiSZFO/wMjKfNBEUs2SvSQ/3do/W5dw7kcy+da4f/92Y5zLd9tZz9b3zB875b+ZOqDtn5r6++m34hSh/819GXt7e06//zzddNNN+nv/u7vQjknVxQAAGHwHHs++cnU2wPTp0/X9OnTs98OCwYbAAD0Aa2trd3+jsViisViOWpNd8zZAAAgBJ4X3CZJNTU1SiQSma2uri63b/gTiGwAANAHNDU1KR6PZ/7Ol6iGxGADAIBwBPycjXg83m2wkU+4jQIAAAJFZKMP65J/KqFtYaaUGzWWuZ3+Y9NoyrawlbFIJUn/IX70kGVBrnZDCmS7Jcf2sKUsbThXqfkzckzpspIihkW+omXmzzVdbkgpNn8M9jLDqayLqpmyRHvZBtOCa9GkJfW1w3yxOIcMqc2277bTUF/U/F04pnRZSRFDOnQ0Zq6vJGlJeTakuEYs/Slt6IO2fmvr76bfiFL+1xCMPMlGOXjwoLZt25b5+4MPPtCmTZs0cOBAjRw5Mtutk8RgAwCAotLQ0KApU6Zk/p4/f74k6cYbb9Ty5csDOSeDDQAAQuB4x3kg30nU2xO1tbXyLBHaIDBnAwAABIrIBgAAYSjiVV8ZbAAAEIY8mSCaCww2+rC04Z5cp2f+2lNp86x2z/W/6xbxX8vpSJklGyVqykaxLMgV6TAsvNV+yHiMd+iwuRGm+5YllswIc21ySv0/20jM8p5S/p95NGX+50rasl5YJO3fQtstWnM2ivkgW/sihjLbYmtOh+VCMmSdeJbvXV2G+mwLsdmyUQzfbTRp/jKiSXNfM/UNW3/qMvRBW7+19XfTb4T1Igd6gcEGAABhKOLbKEwQBQAAgSKyAQBAGIhsAAAABIPIBgAAYSCyAQAAEAwiG32Yaxjupi15bWnPMv40pUeaswUVsS7k5d8+p8tyUNI/X9BLGlJiJXlJwyJekjzDQmxOmTmd0bEt5BXzPy6SMi+GFe3074bpLvN3ETUs4iVJxq/Q8tWavsOoJXXZtNjakeP8K4ykzHmdjuU7VIf/d2j9blOGRfss3591kTbDtWe7Xm2pw6a+YetPpj5o67e2/m76jUBAivg5G0Q2AABAoIhsAAAQgnxZiC0XiGwAAIBAEdkAACAMZKMAAAAEo+AGG48//rjGjBmj8vJyTZw4Ub/+9a9z3SQAAGDR49sos2fP1te+9jVddtllQbTH6umnn9add96pxx9/XH/zN3+jJ554QtOnT9c777yjkSNHht6efJc2xNZcW3qrjSm9yhbCy/JKo3IN+YKGFFZJcjsty2h6hkZYjvFMq4lKcgxlTpc5n9ExpJDaUkutZYbsTdvXbvouetsG03uyfQ7GVVpl/sw92/dkuCY815Kya7mOTNee7Xq1prGaDrP2p56nONr6u+k3Asi2Hv9fp62tTVOnTtWnPvUpPfDAA9qxY0cQ7fL18MMPa86cObr55ps1btw4PfLII6qpqVF9fb3v65PJpFpbW7ttAADkgqO/ZKRkdcv1GzsBPR5sPPfcc9qxY4fmzZunZ599VqNHj9b06dP1X//1X+rstDwB6CSlUim9+eabmjp1arf9U6dO1euvv+57TF1dnRKJRGarqakJrH0AAFgdfahXEFue61U8fdCgQfr617+ut956Sxs3btTYsWM1a9YsDR8+XHfddZfee++9bLdTH3/8sdLptIYMGdJt/5AhQ7Rr1y7fYxYsWKCWlpbM1tTUlPV2AQAAu5OaILpz506tXr1aq1evVjQa1Re+8AVt2bJF55xzjr73ve9lq43dOE73EZznecfsOyoWiykej3fbAADICS/ALc/1eLDR2dmp5557Tl/60pc0atQoPfvss7rrrru0c+dOPfnkk1q9erWeeuop3X///VltaHV1taLR6DFRjD179hwT7QAAAPmjx9kow4YNk+u6+spXvqKNGzfq05/+9DGv+fznP68BAwZkoXl/UVZWpokTJ2rNmjW65pprMvvXrFmjq6++Oqvn6iuihmlDEesUeQvTM3Fttwt7UeYZIlVHjjGURczjZidiWbTMknzQK6ZMB9eSjZL2L7N9TY6l3aYFvrxeZQb1/JgjxxkKLZ+D8bPLMtv1YLuOTNee/Xq1NaQ3x/T8M7L1d9NvBAJSxA/16vFg43vf+55mzpyp8vJy42tOOeUUffDBByfVMD/z58/XrFmzNGnSJE2ePFlLly5VY2Ojbr311qyfCwAAZEePBxuzZs0Koh0n5LrrrtO+fft0//33a+fOnRo/frx+8YtfaNSoUTlrEwAAJ6KYF2IruLVRbr/9dt1+++25bgYAADhBBTfYAACgIBXxnI2CWxsFAAAUFiIbAACEoYgjGww2+rCIIa0tarkyo7Z8RkMczLbAl2tYFEyS3BJDKmGppcLSUt/dTpn//iNlZeYy08JbUUvDo5b2mcps6ZEmllRQa9ppLzKbjamvtrp6k6pq+xx68bk6peafMGOKq+W7tV1HpmvPdr2arnHJ3Des6yQaymz91tbfTb8RQLYx2AAAIATFnI3CnA0AABAoIhsAAIQhqBVa++qqrwAAACeKyAYAAGEo4mwUIhsAACBQRDb6sFLHP7cuFuk0HlNe0mUsc6L+6XWuJVvQVpaO+d9nTJebL8tohX8aq5MyLwwYsaRoeqbUV8vKoE4sZiwzpkeWmMf1nulcttVJ84FtNV1Dme1zcAyfnXScz9zE8L07trTmCvN15BmuPdv1arrGJXPfsPUZUx+09Vtbfzf9RiAYxZyNwmADAIAwcBsFAAAgGEQ2AAAIQ0C3UYhsAACAokdkAwCAMBTxnA0GG31YzPGf1t4/kjQeUx41z2qPlvpnbrhl5ivdNhu/q9y/rKu/eYZ8tMM/KyHqmheickosi3WlDDP1bZkgloXdTBkLbsycYuCW+b9f+yJ25jLTwwStDxk0lNnOY22f6T1ZPgeny/IdmvZbFmIzLhRnWWzN9P1JUrrS/9qzXa+ma1yS0oYEG1t/MvVBW7+19XfTbwSKw+OPP67vfve72rlzp84991w98sgjuvTSSwM5F7dRAAAIgxfg1kNPP/207rzzTn3rW9/SW2+9pUsvvVTTp09XY2PjybxDIwYbAAAUmYcfflhz5szRzTffrHHjxumRRx5RTU2N6uvrAzkfgw0AAEJw9KFeQWyS1Nra2m1LJv1voaVSKb355puaOnVqt/1Tp07V66+/Hsh7Z7ABAEAfUFNTo0Qikdnq6up8X/fxxx8rnU5ryJAh3fYPGTJEu3btCqRtTBAFAKAPaGpqUjwez/wdO85j/h2n+wRmz/OO2ZctDDYAAOgD4vF4t8GGSXV1taLR6DFRjD179hwT7cgWBhtFqJ8lFa6yxFxWVuafXncoZk5ZTFeY0wI7+/mPoEsqzXf3Ip3m1ETjMWWW1NdOw0JsltG9bTExU2pnusLcBjfmX59bajlPiWWBL8OpPMtNU8f4FdoWErN9Dv770+ne/eREDJ+502W5Hjz/KfpeqfmadMvNqaCdcf9zdVquV9M1LknpCkMbLP2p3NAHbf3W1t8Rsjx5zkZZWZkmTpyoNWvW6JprrsnsX7Nmja6++uosN+4IBhsAABSZ+fPna9asWZo0aZImT56spUuXqrGxUbfeemsg52OwAQBACPJpifnrrrtO+/bt0/3336+dO3dq/Pjx+sUvfqFRo0Zlv4FisAEAQFG6/fbbdfvtt4dyLgYbAACEpQDWMQkCgw0AAMKQJxNEc4HBRhEaEDlkLKsq7TCW9Y+lfPcfqjBkdEjqsmSjdPXz359KmWf3O4ZTeRFzVkK0w3yZRyyLf5l4UUuGQZkhs8SQcSJJXYaytG0RL8v6WcZMFVv6vOHHyotYfsVsGTuO/3uyZcS4peb6oin/68hJ9/xX1rVkE6XLLZkllf5tSFmyUUzXuCR1GbJRZOlPpj5o67e2/g6EhcEGAAAhyKcJomHjceUAACBQRDYAAAhDEc/ZILIBAAACRWQDAIAQMGcDAAAgIEQ2itCgaLu5rNRcFi/3T6/bX9HfeEy6n/kS66wyLK5lzvwzplRa0yaT5mF/pMuU82k+xovYFkHzL3PLep7GmrYc41l6rmvKNu5N6msvFnyztcEtM6dCRyzpwabvyXF7npprW8QuHTOXpfr7l3VWmpvQWWUuS/fzT7uOVvgvtiaZ+6Ct39r6u1RuKUPWMWcDAAAgGEQ2AAAIA5ENAACAYBDZAAAgBGSjAAAABITIBgAAYSjiORsMNorQqdFOY9mQ0hZj2cDYYd/9e/sljce0VlpSXzv9cz4d15LyaYjF2VcMNZeZU1+Nh1hTSF3DirCeOeNTriH11ZZaaqvP9Bl5lnabwrCuZVFcW4qyKdU3bb705KQt35NpdddefE/W1Ffz4sHGVVq7bKmvlZYU6kr/FNf+lv5k6oO2fmvr76S+IiwMNgAACEMRRzaYswEAAAJVMIONJUuW6OKLL1a/fv00YMCAXDcHAIAeOZqNEsSW7wpmsJFKpTRz5kzddtttuW4KAAA95wW45bmCmbNx3333SZKWL1+e24YAAIAeKZjBRm8kk0klk3+Z2d3a2prD1uSPwRHDtHpJw0v/ZCwbWu7/+e3tZ16IrSNpSLWQlOzyD6ylZE61MGV7uDHjIYqkzGVOl3991rCkJavDlPFhyhCRzIuqWTNYLGVZjVdaslEilmwUU6aKY15jTI4t88X4wZqPMR5iW0DOlo1iSNzo6m9uRLrK/CHFKv0vzIH9DhmPMfVBW7+19XeEi4d69VF1dXVKJBKZraamJtdNAgCg6OR0sLF48WI5jmPdGhoael3/ggUL1NLSktmampqy2HoAAHqAORu5MW/ePF1//fXW14wePbrX9cdiMcVilvg6AAAIXE4HG9XV1aqurs5lEwAACEcRP9SrYCaINjY2av/+/WpsbFQ6ndamTZskSWPHjlVlpeV5wQAAIKcKZrCxcOFCPfnkk5m/L7jgAknSa6+9ptra2hy1CgCAE+PImsx2UvXmu4IZbCxfvpxnbGRJqWP+2mssKXQ15ft993/c35z6eqjTnEu437DgWmfEfIxX4p/zmS7v3UJsxsXELGFJW5pZb1JfTdO0remyUcsCX1n85bG9V9vCacY0Vlt6qzX11X+/9b2avgtL2nC6zPyG3Zh/mdvfnN5aakhvlaQBlf6Lqg3rb07RN/VBW78tdSz5vEBICmawAQBAQSviORt9+jkbAAAg94hsAAAQAp4gCgAAEBAiGwAAhKGI52ww2EA3o0s6jWVjYnt89++3LcSWNi/EZtJSYk5LSJX519eVNKcYpFPmAF6vFmLrhV5lTUQsjbDFJLOZB2f7HKyZJT1fOC2sz9wrsWTylJnfVCTmn3VS3s/cZxL9Ooxlp1W1+O4f1c8/40Qy90Fbv5XIRkHuMdgAACAsBRCFCAKDDQAAQsAEUQAAgIAQ2QAAIAxFPEGUyAYAAAgUkQ0AAEJQzHM2GGygm0ERcxrr2WW7ffe3pSuMx3RZVr0qMay8tbu00nhMa6zcd//hpDnFtqvT3Aa3yz+455pSN6XehSwt1TmGXwrHdowtLdZ4HnOZ14v35PXiM/IsOcC9aYP1czV8RlFLanVJqXlRtYqYf3ppvNyc3jqk4qCxbFR//xTXM8t3GY8x9UFbvwXyAbdRAAAIgxfgFpAlS5bo4osvVr9+/TRgwIBe18NgAwAA+EqlUpo5c6Zuu+22k6qH2ygAAISgEOds3HfffZKk5cuXn1Q9DDYAAOgDWltbu/0di8UUi8Vy1JruuI0CAEAYAp6zUVNTo0Qikdnq6urCemfHxWADAIA+oKmpSS0tLZltwYIFvq9bvHixHMexbg0NDVltG7dRcMLOLe3nu7/TazQeEzWkt0pSZdQ/ZTBRdorxmH3l/il+rSlzqLCjy5wWm+ryT4tNe+ZxuGtdwrXnIsbU197diI1kcWq628slZE0prmF9dpL52isrMae3lltWT42XJX33D4q1G48ZUf4nY9mY2F7f/efF/mg8xtQHUSACfoJoPB5XPB4/7svnzZun66+/3vqa0aNHZ6Fhf8FgAwCAIlJdXa3q6upQz8lgAwCAEBRiNkpjY6P279+vxsZGpdNpbdq0SZI0duxYVVaaH8D41xhsAAAAXwsXLtSTTz6Z+fuCCy6QJL322muqra094XqYIAoAQBgK8Amiy5cvl+d5x2w9GWhIDDYAAEDAuI2Ck/bpMv/F0SSpv/OhsWxg1H+RqmGlB4zH7C5P+O7/U5d5ln57lzlT5XDaP1Oly5qNkt1MFVtGhfkYc5ZPWGyfg/mY3mWjmD4j2+dgWuivImrOOOlf4p9xIkmnlBzy3T+ktMV4zPBSczbKmaUf++7/lGUhQhQ2x/Pk9GrFwePXm+8YbAAAEIaAU1/zGbdRAABAoIhsAAAQgkJMfc0WIhsAACBQRDYAAAgDczYAAACCQWQDgbKl8Q2LHvDd/8fSfcZj9qb9F2LbZ9gvSW1uhbGsw/VPfe3wzIu32VI+071cuKynonnwT5mw3qtkfr+21Ndyxz/FtTxiTn2tihw2lg2K+i+4dqphvySNiPov9CdJlRFSXIsNczYAAAACQmQDAIAwMGcDAAAgGEQ2AAAIAXM2AAAAAkJkAwCAMBTxnA0GG8iZyoj/arFnW+JtZxsyUpPeAeMxB709xrJ21z91stOS1tlpWbk0zHTQYmJKfS21xI9LDcf0j5gvsEqnzFgWc0zp0Oa0awBHMNgAACAkhTC/IgjM2QAAAIEisgEAQBg878gWRL15jsEGAAAhIPUVAAAgIEQ20CeYMwXsZYMYbgMISxGnvhbET+2HH36oOXPmaMyYMaqoqNAZZ5yhRYsWKZVK5bppAADgOAoisvHuu+/KdV098cQTGjt2rP7whz/olltuUXt7ux566KFcNw8AgONy3CNbEPXmu4IYbEybNk3Tpk3L/H366adr69atqq+vZ7ABAECeK4jBhp+WlhYNHDjQ+ppkMqlkMpn5u7W1NehmAQDgjzkbhWX79u167LHHdOutt1pfV1dXp0QikdlqampCaiEAADgqp4ONxYsXy3Ec69bQ0NDtmObmZk2bNk0zZ87UzTffbK1/wYIFamlpyWxNTU1Bvh0AAIyOPmcjiC3f5fQ2yrx583T99ddbXzN69OjMfzc3N2vKlCmaPHmyli5detz6Y7GYYrHYyTYTAACchJwONqqrq1VdXX1Cr92xY4emTJmiiRMnatmyZYpYVm4EACDv8Ljy/Nbc3Kza2lqNHDlSDz30kPbu3ZspGzp0aA5bBgAAjqcgBhurV6/Wtm3btG3bNo0YMaJbmVcAIzoAAFgbJc/Nnj1bnuf5bgAAIL8VRGQDAICCx3M2AAAAgkFkAwCAEDBnAwAAICBENgAACAPP2QAAAEHiNgoAAEBAiGwAABAGUl8BAACCQWQDAIAQMGcDAADgEz788EPNmTNHY8aMUUVFhc444wwtWrRIqVSqx3UR2QAAIAyud2QLot4AvPvuu3JdV0888YTGjh2rP/zhD7rlllvU3t6uhx56qEd1MdgAAADHmDZtmqZNm5b5+/TTT9fWrVtVX1/PYAMAgLwUcDZKa2trt92xWEyxWCyrp2ppadHAgQN7fBxzNgAA6ANqamqUSCQyW11dXVbr3759ux577DHdeuutPT6WwQYAACFw9JeMlKxuf66/qalJLS0tmW3BggW+7Vi8eLEcx7FuDQ0N3Y5pbm7WtGnTNHPmTN188809fu/cRgEAoA+Ix+OKx+PHfd28efN0/fXXW18zevTozH83NzdrypQpmjx5spYuXdqrtjHYAAAgDHmyEFt1dbWqq6tP6LU7duzQlClTNHHiRC1btkyRSO9uiDDYAAAAx2hublZtba1Gjhyphx56SHv37s2UDR06tEd1MdgAACAEhfYE0dWrV2vbtm3atm2bRowY0a3M62E0hQmiAADgGLNnz5bneb5bTxHZAAAgDEW86iuDDQAAQuB4npwAJogGUWe2cRsFAAAEisgGAABhcP+8BVFvniOyAQAAAkVkAwCAEDBnAwAAICBENgAACEMRp74S2QAAAIEisgEAQBjyZCG2XCCyAQAAAkVkAwCAEBTaQmzZRGQDAAAEisgGAABhYM4GAABAMIhsAAAQAsc9sgVRb74jsgEAAAJFZAMAgDAwZwMAACAYRDYAAAhDEa+NwmADAIAQsMQ8AABAQIhsAAAQBiaIAgAABIPIBgAAYfAkBfEArvwPbBDZAAAAwSqYwcaXv/xljRw5UuXl5Ro2bJhmzZql5ubmXDcLAIATcjQbJYgt3xXMYGPKlCl65plntHXrVj333HPavn27rr322lw3CwAAHEfBzNm46667Mv89atQoffOb39SMGTPU2dmp0tLSHLYMAIAT4CmgbJTsV5ltBTPY+KT9+/frpz/9qS6++GLrQCOZTCqZTGb+bm1tDaN5AADgEwrmNook3XPPPerfv78GDRqkxsZGvfjii9bX19XVKZFIZLaampqQWgoAwF85+pyNILY8l9PBxuLFi+U4jnVraGjIvP7uu+/WW2+9pdWrVysajeqrX/2qPMuHvGDBArW0tGS2pqamMN4WAAD4hJzeRpk3b56uv/5662tGjx6d+e/q6mpVV1frzDPP1Lhx41RTU6MNGzZo8uTJvsfGYjHFYrFsNhkAgN5xJTkB1ZvncjrYODp46I2jEY1PzskAAAD5pyAmiG7cuFEbN27UJZdcolNOOUXvv/++Fi5cqDPOOMMY1QAAIJ+w6mueq6io0PPPP68rrrhCZ511lr72ta9p/PjxWrduHbdJAADIcwUR2TjvvPP0q1/9KtfNAACg94p41deCGGwAAFDwiniwURC3UQAAQOEisgEAQBiIbAAAAASDyAYAAGEo4od6EdkAAACBYrABAEAIjj7UK4gtKF/+8pc1cuRIlZeXa9iwYZo1a5aam5t7XA+DDQAA4GvKlCl65plntHXrVj333HPavn27rr322h7Xw5wNAADCUIDZKHfddVfmv0eNGqVvfvObmjFjhjo7O1VaWnrC9TDYAACgD2htbe32d7ZXPt+/f79++tOf6uKLL+7RQEPiNgoAAOFwveA2STU1NUokEpmtrq4uK82+55571L9/fw0aNEiNjY168cUXe1wHgw0AAPqApqYmtbS0ZLYFCxb4vm7x4sVyHMe6NTQ0ZF5/991366233tLq1asVjUb11a9+VV4Pb91wGwUAgDAEPGcjHo8rHo8f9+Xz5s3T9ddfb33N6NGjM/9dXV2t6upqnXnmmRo3bpxqamq0YcMGTZ48+YSbyGADAIAicnTw0BtHIxrJZLJHxzHYAAAgFAFFNhRMNsrGjRu1ceNGXXLJJTrllFP0/vvva+HChTrjjDN6FNWQimywcXRE1nqwAJ7tCgAI3NH/H/R0DkIxqKio0PPPP69Fixapvb1dw4YN07Rp07RixYoeZ7kU1WCjra1NkjTqwg9z2xAAQF5pa2tTIpEI9iQF9pyN8847T7/61a+yUldRDTaGDx+upqYmVVVVyXGyuxpOa2urampq1NTUdEITdApRMbxHqTjeJ++x7yiG9xnke/Q8T21tbRo+fHhW60V3RTXYiEQiGjFiRKDnONHZwIWsGN6jVBzvk/fYdxTD+wzqPQYe0TjK9RTI/Ao3/28BFdVgAwCAnPHcI1sQ9eY5HuoFAAACRWQjS2KxmBYtWpTV59Dnm2J4j1JxvE/eY99RDO+zz7zHApsgmk2OR74PAACBaW1tVSKR0OdqblNJJPsDpi43qVeb6tXS0pK383aIbAAAEIYiniDKnA0AABAoIhsAAIShiOdsENkAAACBYrARgC9/+csaOXKkysvLNWzYMM2aNUvNzc25blbWfPjhh5ozZ47GjBmjiooKnXHGGVq0aJFSqVSum5Z1S5Ys0cUXX6x+/fppwIABuW5OVjz++OMaM2aMysvLNXHiRP3617/OdZOybv369brqqqs0fPhwOY6jF154IddNyqq6ujpddNFFqqqq0uDBgzVjxgxt3bo1183Kuvr6ek2YMCHzMK/JkyfrlVdeyXWzes/TX6IbWd1y/caOj8FGAKZMmaJnnnlGW7du1XPPPaft27fr2muvzXWzsubdd9+V67p64okntGXLFn3ve9/Tv//7v+vee+/NddOyLpVKaebMmbrtttty3ZSsePrpp3XnnXfqW9/6lt566y1deumlmj59uhobG3PdtKxqb2/X+eefrx/84Ae5bkog1q1bp7lz52rDhg1as2aNurq6NHXqVLW3t+e6aVk1YsQIPfjgg2poaFBDQ4Muv/xyXX311dqyZUuum4YeIvU1BC+99JJmzJihZDKp0tLSXDcnEN/97ndVX1+v999/P9dNCcTy5ct155136sCBA7luykn5zGc+owsvvFD19fWZfePGjdOMGTNUV1eXw5YFx3EcrVy5UjNmzMh1UwKzd+9eDR48WOvWrdNll12W6+YEauDAgfrud7+rOXPm5LopJyyT+jr0/6kkUpb1+rvclF7dtTSvU1+JbARs//79+ulPf6qLL764zw40JKmlpUUDBw7MdTNgkUql9Oabb2rq1Knd9k+dOlWvv/56jlqFbGhpaZGkPt0H0+m0VqxYofb2dk2ePDnXzUEPMdgIyD333KP+/ftr0KBBamxs1IsvvpjrJgVm+/bteuyxx3Trrbfmuimw+Pjjj5VOpzVkyJBu+4cMGaJdu3blqFU4WZ7naf78+brkkks0fvz4XDcn6zZv3qzKykrFYjHdeuutWrlypc4555xcN6t3XDe4Lc8x2DhBixcvluM41q2hoSHz+rvvvltvvfWWVq9erWg0qq9+9avK9ztWPX2PktTc3Kxp06Zp5syZuvnmm3PU8p7pzfvsSxzH6fa353nH7EPhmDdvnt5++239/Oc/z3VTAnHWWWdp06ZN2rBhg2677TbdeOONeuedd3LdLPQQz9k4QfPmzdP1119vfc3o0aMz/11dXa3q6mqdeeaZGjdunGpqarRhw4a8Dv/19D02NzdrypQpmjx5spYuXRpw67Knp++zr6iurlY0Gj0mirFnz55joh0oDHfccYdeeuklrV+/XiNGjMh1cwJRVlamsWPHSpImTZqkN954Q48++qieeOKJHLesF4r4ORsMNk7Q0cFDbxyNaCSTyWw2Ket68h537NihKVOmaOLEiVq2bJkikcIJkp3Md1nIysrKNHHiRK1Zs0bXXHNNZv+aNWt09dVX57Bl6CnP83THHXdo5cqVWrt2rcaMGZPrJoXG87y8/y3FsRhsZNnGjRu1ceNGXXLJJTrllFP0/vvva+HChTrjjDPyOqrRE83NzaqtrdXIkSP10EMPae/evZmyoUOH5rBl2dfY2Kj9+/ersbFR6XRamzZtkiSNHTtWlZWVuW1cL8yfP1+zZs3SpEmTMhGpxsbGPjff5uDBg9q2bVvm7w8++ECbNm3SwIEDNXLkyBy2LDvmzp2rn/3sZ3rxxRdVVVWViVYlEglVVFTkuHXZc++992r69OmqqalRW1ubVqxYobVr12rVqlW5blrvENlAtlRUVOj555/XokWL1N7ermHDhmnatGlasWJF4S+P/GerV6/Wtm3btG3btmNCt/k+L6WnFi5cqCeffDLz9wUXXCBJeu2111RbW5ujVvXeddddp3379un+++/Xzp07NX78eP3iF7/QqFGjct20rGpoaNCUKVMyf8+fP1+SdOONN2r58uU5alX2HE1d/utrcNmyZZo9e3b4DQrI7t27NWvWLO3cuVOJREITJkzQqlWrdOWVV+a6ab1TxAux8ZwNAAAClHnOxsCbgnvOxv5lef2cDSIbAACEwPNceV7201SDqDPbCmdWHwAAKEhENgAACIPnBTO/ogBmQxDZAAAAgSKyAQBAGLyAslGIbAAAgGJHZAMAgDC4ruQEkDlCNgoAACh2RDYAAAgDczYAAACCQWQDAIAQeK4rL4A5GzxBFEDe2Lt3r4YOHaoHHnggs+93v/udysrKtHr16hy2DEBfR2QDKBKnnnqqfvzjH2vGjBmaOnWqzj77bP3jP/6jbr/9dk2dOjXXzQP6viKes8FgAygiX/jCF3TLLbfohhtu0EUXXaTy8nI9+OCDuW4WgD6OwQZQZB566CGNHz9ezzzzjBoaGlReXp7rJgHFwfUkpzgjG8zZAIrM+++/r+bmZrmuq48++ijXzQFQBIhsAEUklUrphhtu0HXXXaezzz5bc+bM0ebNmzVkyJBcNw3o+zxPUhBPEM3/yAaDDaCIfOtb31JLS4u+//3vq7KyUq+88ormzJmjl19+OddNA/o8z/XkBXAbxSuAwQa3UYAisXbtWj3yyCN66qmnFI/HFYlE9NRTT+k3v/mN6uvrc908AH0YkQ2gSNTW1qqzs7PbvpEjR+rAgQO5aRBQbDxXwdxG4aFeAACgyBHZAAAgBMzZAAAACAiRDQAAwlDEczYYbAAAEIIudQayNEqXOo//ohxjsAEAQIDKyso0dOhQ/WbXLwI7x9ChQ1VWVhZY/SfL8QphZgkAAAWso6NDqVQqsPrLysryep0jBhsAACBQZKMAAIBAMdgAAACBYrABAAACxWADAAAEisEGAAAIFIMNAAAQKAYbAAAgUP8fM+Z6YZocH/MAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 629.921x629.921 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Potential energy function\n", "def V(x, y):\n", "    #return (x**2 - 1)**2 + 0.5*y * x + y**2     # kJ mol-1\n", "    return 3 * np.exp(-x ** 2 - (y - 1/3) ** 2) \\\n", "    - 3 * np.exp(-x ** 2 - (y - 5/3) ** 2) \\\n", "    - 5 * np.exp(-(x - 1) ** 2 - y ** 2) \\\n", "    - 5 * np.exp(-(x + 1) ** 2 - y ** 2) \\\n", "    + 0.2 * x ** 4 \\\n", "    + 0.2 * (y - 1/3) ** 4\n", "\n", "# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "gamma = 1           # ps-1\n", "\n", "D     = kB * T / mass / gamma # nm2 ps-1\n", "sigma = np.sqrt(2 * D)\n", "beta  = 1 / kB / T\n", "\n", "# Grid\n", "nd     =  2  # Number of dimensions\n", "xbins  = 60  # x boundaries\n", "xmin   = -3.4\n", "xmax   =  3.4\n", "\n", "ybins  = 60  # y boundaries\n", "ymin   = -3.4\n", "ymax   =  3.4\n", "\n", "\n", "xedges = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx     = xedges[1] - xedges[0]\n", "x      = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins  = xbins - 1\n", "\n", "yedges = np.linspace(ymin, ymax, ybins)  # array with y edges\n", "dy     = yedges[1] - yedges[0]\n", "y      = yedges[:-1] + (dy / 2)                # array with y centers\n", "ybins  = ybins - 1\n", "\n", "Nbins  = xbins*ybins                      # number of bins\n", "\n", "grid = np.meshgrid(x,y)\n", "\n", "\n", "fig, ax1 = plt.subplots(1, 1,figsize=(16*in2cm, 16*in2cm))  \n", "\n", "pos = ax1.pcolor(grid[0], grid[1], V(grid[0], grid[1]), shading='auto', vmax=5)\n", "ax1.set_title('Potential')\n", "ax1.set_aspect('equal', 'box')\n", "ax1.set_xlabel('x')\n", "ax1.set_ylabel('y')\n", "fig.colorbar(pos, ax=ax1)"]}, {"cell_type": "markdown", "id": "f05317d4", "metadata": {}, "source": ["## Create Voronoi tessellation"]}, {"cell_type": "code", "execution_count": 3, "id": "2c7c9033", "metadata": {}, "outputs": [], "source": ["# Number of initial points uniformly distributed\n", "Npoints  = 10000\n", "xpoints  = np.random.uniform(xmin, xmax, Npoints)\n", "ypoints  = np.random.uniform(ymin, ymax, Npoints)\n", "points   = np.array((xpoints, ypoints))\n", "\n", "# Number of cluster cells\n", "Ncells  = 100\n", "\n", "cells = kmeans(points.T,Ncells,iter=50)\n", "cells = cells[0]\n", "cc_x = cells[:,0]\n", "cc_y = cells[:,1]\n", "\n", "vor = clippedVoronoi.voronoi(cells, (xmin, xmax, ymin, ymax))\n", "\n", "xcenters = vor.filtered_points[:,0]\n", "ycenters = vor.filtered_points[:,1]\n", "\n", "############################# R E G U L A R   G R I D #################\n", "\n", "# Grid\n", "xbins  = int(np.sqrt(Ncells))\n", "ybins  = int(np.sqrt(Ncells))\n", "\n", "xedges = np.linspace(xmin, xmax, xbins + 1)  # array with x edges\n", "dx     = xedges[1] - xedges[0]\n", "x      = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins  = xbins - 1\n", "\n", "yedges = np.linspace(ymin, ymax, ybins + 1)  # array with y edges\n", "dy     = yedges[1] - yedges[0]\n", "y      = yedges[:-1] + (dy / 2)                # array with y centers\n", "ybins  = ybins - 1\n", "\n", "Nbins  = xbins*ybins                      # number of bins\n", "\n", "grid = np.meshgrid(x,y)\n", "\n", "# Grid contains 2 matrices xbins x ybins with the x and y coordinates\n", "# This transforms the two matrices in two vectors (x,y)\n", "\n", "cells_grid = np.array([grid[0].flatten('F'), grid[1].flatten('F')]).T\n", "vor_grid = clippedVoronoi.voronoi(cells_grid, (xmin, xmax, ymin, ymax))\n", "\n", "xcenters_grid = vor_grid.filtered_points[:,0]\n", "ycenters_grid = vor_grid.filtered_points[:,1]"]}, {"cell_type": "code", "execution_count": 4, "id": "2538992e", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 629.921x629.921 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["############################# F I G U R E  #################\n", "\n", "fig, (ax0, ax1) = plt.subplots(1, 2,figsize=(16*in2cm, 16*in2cm))  \n", "\n", "\n", "for i,region in enumerate(vor.filtered_regions):\n", "    vertices = vor.vertices[region + [region[0]], :]\n", "    ax0.plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax0.plot(xcenters[i], ycenters[i], 'ro', markersize=3)\n", "ax0.set_aspect('equal', 'box')\n", "\n", "ax0.set_xlabel('x')\n", "ax0.set_ylabel('y')\n", "\n", "\n", "\n", "for i,region in enumerate(vor_grid.filtered_regions):\n", "    vertices = vor_grid.vertices[region + [region[0]], :]\n", "    ax1.plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax1.plot(xcenters_grid[i], ycenters_grid[i], 'ro', markersize=3)\n", "ax1.set_aspect('equal', 'box')\n", "\n", "ax1.set_xlabel('x')\n", "ax1.set_ylabel('y')\n", "\n", "fig.tight_layout()"]}, {"cell_type": "markdown", "id": "5fc1938b", "metadata": {}, "source": ["## Adjaceny matrix\n", "\n", "Adjacency matrix is generated searching Voronoi cells sharing the same vertices.\n", "The same script is using to estimate the matrix $\\mathcal{S}_{ij}$ with the intersecting surfaces and the matrix $h_{ij}$ distance between adjacent points."]}, {"cell_type": "code", "execution_count": 5, "id": "717026b9", "metadata": {"scrolled": false}, "outputs": [], "source": ["# Volumes    \n", "Vol      = np.zeros(Ncells)\n", "\n", "# Adjacency matrix\n", "A      = scipy.sparse.lil_matrix((Ncells, Ncells))\n", "\n", "# Intersecting areas\n", "S      = scipy.sparse.lil_matrix((Ncells, Ncells))\n", "\n", "# Distances between neighboring points \n", "h      = scipy.sparse.lil_matrix((Ncells, Ncells))\n", "\n", "# Note that len(vor.filtered_regions) = Ncells\n", "# region = Voronoi cell\n", "for i,region in enumerate(vor.filtered_regions):\n", "    \n", "    vertices = vor.vertices[region + [region[0]], :]\n", "    Vol[i] = ConvexHull(vertices).volume\n", "\n", "    for j in range(i+1, Ncells):\n", "        \n", "        #ri and rj contain the indeces of the vertices of the region omega_i and omega_j\n", "        ri = vor.filtered_regions[i]\n", "        rj = vor.filtered_regions[j]\n", "        int_sur = np.intersect1d(ri, rj)\n", "\n", "        # if two cells share vertices, \n", "        if int_sur.size != 0:\n", "            \n", "            # then they are adjacent:\n", "            A[i,j] = 1\n", "            A[j,i] = 1\n", "            \n", "            # Coordinates of the cells centers\n", "            v0_x       = vor.filtered_points[i,0]\n", "            v0_y       = vor.filtered_points[i,1]\n", "            v1_x       = vor.filtered_points[j,0]\n", "            v1_y       = vor.filtered_points[j,1]\n", "            \n", "            # Distance between the centers\n", "            distance = np.sqrt( (v1_x - v0_x)**2 + (v1_y - v0_y)**2 )\n", "            h[i,j]   = distance\n", "            h[j,i]   = distance\n", "            \n", "            # Coordinates of the intersecting vertices\n", "            int_sur0_x = vor.vertices[int_sur[0],0]\n", "            int_sur0_y = vor.vertices[int_sur[0],1]\n", "            int_sur1_x = vor.vertices[int_sur[1],0]\n", "            int_sur1_y = vor.vertices[int_sur[1],1]\n", "                        \n", "            # Intersecting area (length of the common edge)\n", "            area = np.sqrt( (int_sur1_x - int_sur0_x)**2 + (int_sur1_y - int_sur0_y)**2 )\n", "            S[i,j] = area\n", "            S[j,i] = area\n", "            \n", "# Adjancency matrix for grids\n", "A_grid = adjacency_matrix_grid(int(np.sqrt(Ncells)), 2, periodic=False)"]}, {"cell_type": "markdown", "id": "7949ea33", "metadata": {}, "source": ["## SqRA\n", "\n", "$$\n", "\\pi(x,y) = \\exp(-\\beta V(x,y))\n", "$$\n", "\n", "$$\n", "Q_{ij} = D \\frac{\\mathcal{S}_{ij}}{\\mathcal{V}_i h_{ij}} \\sqrt{\\frac{\\pi_j}{\\pi_i}}\n", "$$"]}, {"cell_type": "code", "execution_count": 6, "id": "2ab8f472", "metadata": {}, "outputs": [], "source": ["# Boltzmann distribution\n", "pi   = np.exp(-beta * V(cc_x, cc_y))\n", "sqra = np.sqrt(pi)\n", "\n", "Q = scipy.sparse.lil_matrix((Ncells, Ncells))\n", "\n", "for i in range(Ncells):\n", "    for j in range(Ncells):\n", "        if A[i,j] == 1:\n", "            Q[i,j] = D * S[i,j] / Vol[i] / h[i,j] * sqra[j] / sqra[i]\n", "            \n", "# The diagonal is equal to minus the row-sum\n", "Q    = Q + scipy.sparse.spdiags( - Q.sum(axis=1).T, 0, Ncells, Ncells)\n", " \n", "    \n", "# Boltzmann distribution\n", "pi_grid   = np.exp(-beta * V(cells_grid[:,0], cells_grid[:,1]))\n", "sqra_grid = np.sqrt(pi_grid)\n", "\n", "Q_grid = scipy.sparse.lil_matrix((Ncells, Ncells))\n", "\n", "for i in range(Ncells):\n", "    for j in range(Ncells):\n", "        if A_grid[i,j] == 1:\n", "            Q_grid[i,j] = D * 1/dx**2 * sqra_grid[j] / sqra_grid[i]\n", "            \n", "# The diagonal is equal to minus the row-sum\n", "Q_grid    = Q_grid + scipy.sparse.spdiags( - Q_grid.sum(axis=1).T, 0, Ncells, Ncells)\n", " "]}, {"cell_type": "code", "execution_count": 7, "id": "300af133", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Q with regular grid')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 866.142x866.142 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Q is a sparse matrix\n", "fig, (ax0, ax1) = plt.subplots(1, 2,figsize=(22*in2cm, 22*in2cm))  \n", "\n", "ax0.spy(<PERSON><PERSON>())\n", "ax0.set_title('Q with random Voronoi')\n", "ax1.spy(Q_grid.toarray())\n", "ax1.set_title('Q with regular grid')\n"]}, {"cell_type": "markdown", "id": "84ba9893", "metadata": {}, "source": ["## Eigenvalue problem\n", "Estimate left eigenvectors and eigenvalues:\n", "\n", "$$\n", "Q \\varphi_i(x) = \\lambda_i \\varphi_i(x)\n", "$$"]}, {"cell_type": "code", "execution_count": 8, "id": "b03bf6bd", "metadata": {}, "outputs": [], "source": ["# Number of eigenvectors\n", "Nevecs = 50\n", "\n", "# Eigenvalue problem\n", "evals, evecs = scipy.sparse.linalg.eigs(Q.T, Nevecs, which='LR')\n", "evals = np.real(evals)\n", "evecs = np.real(evecs)\n", "evecs[:,0] = np.abs(evecs[:,0])\n", "\n", "e0 = np.real(evecs[:,0])\n", "e1 = np.real(evecs[:,1])\n", "e2 = np.real(evecs[:,2])\n", "\n", "# Eigenvalue problem\n", "evals_grid, evecs_grid = scipy.sparse.linalg.eigs(Q_grid.T, Nevecs, which='LR')\n", "evals_grid = np.real(evals_grid)\n", "evecs_grid = np.real(evecs_grid)\n", "evecs_grid[:,0] = np.abs(evecs_grid[:,0])\n", "\n", "e0_grid = np.real(evecs_grid[:,0])\n", "e1_grid = np.real(evecs_grid[:,1])\n", "e2_grid = np.real(evecs_grid[:,2])"]}, {"cell_type": "code", "execution_count": 9, "id": "39881aba", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Eigenvalues')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(evals[0:6],'bs', label = 'random Voronoi')\n", "plt.plot(evals_grid[0:6],'ro', label = 'regular grid')\n", "plt.legend()\n", "plt.title('Eigenvalues')"]}, {"cell_type": "code", "execution_count": 11, "id": "28553fa7", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1000x1000 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#\n", "# normalize chosen colormap\n", "norm = mpl.colors.Normalize(vmin=np.min(e0), vmax=np.max(e0), clip=True)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.hot)\n", "\n", "\n", "fig, (ax) = plt.subplots(3, 2, figsize=(10,10))\n", "\n", "\n", "# normalize chosen colormap\n", "norm = mpl.colors.Normalize(vmin=np.min(e0), vmax=np.max(e0), clip=True)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.hot)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.hot)\n", "\n", "for i,region in enumerate(vor.filtered_regions):\n", "    vertices = vor.vertices[region + [region[0]], :]\n", "    polygon = [vor.vertices[j] for j in region]\n", "    ax[0,0].plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax[0,0].fill(*zip(*polygon), color=mapper.to_rgba(e0[i]));\n", "    ax[0,0].set_aspect('equal', 'box')\n", "    ax[0,0].set_xlabel('x')\n", "    ax[0,0].set_ylabel('y')\n", "    ax[0,0].set_title(r'$\\varphi_0(x,y)$')\n", "    \n", "    \n", "    \n", "norm = mpl.colors.Normalize(vmin=np.min(e1), vmax=np.max(e1), clip=True)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "\n", "for i,region in enumerate(vor.filtered_regions):\n", "    vertices = vor.vertices[region + [region[0]], :]\n", "    polygon = [vor.vertices[j] for j in region]\n", "    ax[1,0].plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax[1,0].fill(*zip(*polygon), color=mapper.to_rgba(e1[i]));\n", "    ax[1,0].set_aspect('equal', 'box')\n", "    ax[1,0].set_xlabel('x')\n", "    ax[1,0].set_ylabel('y')\n", "    ax[1,0].set_title(r'$\\varphi_1(x,y)$')\n", "    \n", "    \n", "norm = mpl.colors.Normalize(vmin=np.min(e2), vmax=np.max(e2), clip=True)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "\n", "for i,region in enumerate(vor.filtered_regions):\n", "    vertices = vor.vertices[region + [region[0]], :]\n", "    polygon = [vor.vertices[j] for j in region]\n", "    ax[2,0].plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax[2,0].fill(*zip(*polygon), color=mapper.to_rgba(e2[i]));\n", "    ax[2,0].set_aspect('equal', 'box')\n", "    ax[2,0].set_xlabel('x')\n", "    ax[2,0].set_ylabel('y')\n", "    ax[2,0].set_title(r'$\\varphi_2(x,y)$')\n", "    \n", "    \n", "################################ R E G U L A R   G R I D ########################################\n", "\n", "# normalize chosen colormap\n", "norm = mpl.colors.Normalize(vmin=np.min(e0_grid), vmax=np.max(e0_grid), clip=True)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.hot)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.hot)\n", "\n", "for i,region in enumerate(vor_grid.filtered_regions):\n", "    vertices = vor_grid.vertices[region + [region[0]], :]\n", "    polygon = [vor_grid.vertices[j] for j in region]\n", "    ax[0,1].plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax[0,1].fill(*zip(*polygon), color=mapper.to_rgba(e0_grid[i]));\n", "    ax[0,1].set_aspect('equal', 'box')\n", "    ax[0,1].set_xlabel('x')\n", "    ax[0,1].set_ylabel('y')\n", "    ax[0,1].set_title(r'$\\varphi_0(x,y)$')\n", "    \n", "    \n", "    \n", "norm = mpl.colors.Normalize(vmin=np.min(e1_grid), vmax=np.max(e1_grid), clip=True)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "\n", "for i,region in enumerate(vor_grid.filtered_regions):\n", "    vertices = vor_grid.vertices[region + [region[0]], :]\n", "    polygon = [vor_grid.vertices[j] for j in region]\n", "    ax[1,1].plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax[1,1].fill(*zip(*polygon), color=mapper.to_rgba(-e1_grid[i]));\n", "    ax[1,1].set_aspect('equal', 'box')\n", "    ax[1,1].set_xlabel('x')\n", "    ax[1,1].set_ylabel('y')\n", "    ax[1,1].set_title(r'$\\varphi_1(x,y)$')\n", "    \n", "    \n", "norm = mpl.colors.Normalize(vmin=np.min(e2_grid), vmax=np.max(e2_grid), clip=True)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "mapper = cm.ScalarMappable(norm=norm, cmap=cm.bwr)\n", "\n", "for i,region in enumerate(vor_grid.filtered_regions):\n", "    vertices = vor_grid.vertices[region + [region[0]], :]\n", "    polygon = [vor_grid.vertices[j] for j in region]\n", "    ax[2,1].plot(vertices[:, 0], vertices[:, 1], 'k-')\n", "    ax[2,1].fill(*zip(*polygon), color=mapper.to_rgba(e2_grid[i]));\n", "    ax[2,1].set_aspect('equal', 'box')\n", "    ax[2,1].set_xlabel('x')\n", "    ax[2,1].set_ylabel('y')\n", "    ax[2,1].set_title(r'$\\varphi_2(x,y)$')\n", "    \n", "    \n", "fig.tight_layout();"]}, {"cell_type": "code", "execution_count": 15, "id": "46195070", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: -63.553688\n", "         Iterations: 168\n", "         Function evaluations: 290\n", "[[-2.04220585  1.01868796  1.0235179 ]\n", " [ 0.24222084 -0.94812694  0.7059061 ]\n", " [ 0.23762167  0.70210032 -0.93972199]]\n"]}], "source": ["from pcca import pcca, schurvects\n", "\n", "# Dense matrix\n", "Qd = np.real(Q.toarray())\n", "\n", "K         = scipy.linalg.expm( 100*0.001 * Qd )\n", "\n", "nc  =  3\n", "\n", "chi, e, Spcca, X = pcca(Qd, 3, massmatrix=None)\n", "\n", "Qc = np.linalg.pinv(chi).dot(Qd.dot(chi))\n", "\n", "print(Qc)"]}, {"cell_type": "code", "execution_count": null, "id": "a8a90cca", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}