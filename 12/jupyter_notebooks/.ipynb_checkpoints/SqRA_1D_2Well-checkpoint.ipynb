{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Estimate rates from SqRA rate matrix 1D"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T06:11:52.049504Z", "start_time": "2020-11-13T06:11:51.357462Z"}}, "outputs": [], "source": ["import sys\n", "\n", "%matplotlib ipympl\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "import matplotlib.cm as cm\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adjacency matrix"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def adjancency_matrix_sparse(nbins, nd, periodic=False):\n", "    v = np.zeros(nbins)\n", "    v[1] = 1\n", "    \n", "    if periodic:\n", "        v[-1] = 1\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.circulant(v)) #.toarray()\n", "    else:\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.toeplitz(v)) #.toarray()\n", "    \n", "    A = A0\n", "    I2 = scipy.sparse.eye(nbins)  #np.eye(nbins)\n", "    for _ in range(1, nd):\n", "        I1 = scipy.sparse.eye(*A.shape) #np.eye(*A.shape)\n", "        A =  scipy.sparse.kron(A0, I1) + scipy.sparse.kron(I2, A)\n", "    return A"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1D system"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cad3300a30a44d559ca4d9d928b31ed2", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=472.4409448818897/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# System parameters\n", "kB    = 0.008314463\n", "T     = 300\n", "mass  = 1\n", "gamma = 1\n", "D     = kB * T / mass / gamma\n", "sigma = np.sqrt(2 * D)\n", "beta  = 1 / kB / T\n", "\n", "# Potential energy function\n", "def V(x):\n", "    return 10* (x**2 - 1)**2\n", "\n", "\n", "\n", "\n", "# Grid\n", "nd     = 1  # Number of dimensions\n", "nedges = 121 # State boundaries\n", "xmin   = -2\n", "xmax   =  2\n", "\n", "x      = np.linspace(xmin, xmax, nedges)  # array with x edges\n", "dx     = x[1] - x[0]\n", "x      = x[:-1] + (dx / 2)                # array with x centers\n", "xbins  = nedges - 1\n", "Nbins  = xbins**nd                        # number of bins\n", "\n", "fig, (ax1) = plt.subplots(1, 1, figsize=(12*in2cm, 8*in2cm), facecolor='white')\n", "###############################################\n", "ax1.plot(x, kB*T*np.ones(x.shape), '--', color='gold')\n", "ax1.text(-1.95, kB*T+0.5, r'$k_B T$', fontsize = 10)\n", "ax1.plot(x, V(x), 'k', label = 'Potential') \n", "\n", "#ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$ / nm')\n", "ax1.set_ylabel(r'$V(x)$ / kJ mol$^{-1}$')\n", "#ax1.legend()\n", "ax1.set_ylim((-1, 18))\n", "ax1.set_xlim((-2, 2))\n", "\n", "\n", "ax1.set_title('Potential energy function')\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "#fig.savefig('potential.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Build SqRA 1D"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13528\\3608420724.py:33: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  Kevals[:,i] = np.exp(Qevals * tau[i])\n"]}], "source": ["A  = adjancency_matrix_sparse(Nbins, nd, periodic=False)\n", "\n", "# Potential energy of states\n", "v = V(x)\n", "\n", "# Flux\n", "flux = D / dx**2\n", "Af   = flux * A\n", "\n", "# Diagonalization\n", "SQRA = np.sqrt(np.exp(- beta * v))\n", "SQRA = SQRA / sum(SQRA)\n", "Di   = scipy.sparse.spdiags(SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) #.toarray()\n", "D1   = scipy.sparse.spdiags(1/SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)       \n", "Q    = D1 * Af * Di\n", "\n", "Q            = Q + scipy.sparse.spdiags(-Q.sum(axis=1).T, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\n", "Qeval<PERSON>, Qevecs = scipy.sparse.linalg.eigs(Q.T, 6, which='LR')\n", "idx    = np.argsort( - np.real(Qevals))\n", "Qevals = Qevals[idx]\n", "Qevecs = Qevecs[:,idx]\n", "\n", "\n", "<PERSON><PERSON><PERSON>, Kevecs = scipy.sparse.linalg.eigs(Q, 6, which='LR')\n", "idx    = np.argsort( - np.real(Kevals))\n", "Kevals = Kevals[idx]\n", "Kevecs = Kevecs[:,idx]\n", "\n", "tau    = np.linspace(0,1,100)\n", "\n", "Kevals = np.zeros((6,100))\n", "for i in range(100):\n", "    Kevals[:,i] = np.exp(Qevals * tau[i])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\emma\\lib\\site-packages\\matplotlib\\cbook\\__init__.py:1369: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return np.asarray(x, float)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13528\\2286108464.py:27: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13528\\2286108464.py:28: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13528\\2286108464.py:29: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13528\\2286108464.py:30: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13528\\2286108464.py:31: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3ec2cc948a6a412cac312c286a13dcaa", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=787.4015748031495/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(1, 3, figsize=(20*in2cm, 7*in2cm), facecolor='white')\n", "\n", "\n", "ax[0].plot(0, Qevals[0], 'ko');\n", "ax[0].plot(1, <PERSON><PERSON><PERSON>[1], 'bo');\n", "ax[0].plot(2, <PERSON><PERSON><PERSON>[2], 'ro');\n", "ax[0].plot(3, <PERSON><PERSON><PERSON>[3], 'go');\n", "ax[0].plot(4, <PERSON><PERSON><PERSON>[4], 'yo');\n", "ax[0].plot(5, <PERSON><PERSON><PERSON>[5], 'co');\n", "ax[0].set_xticks([0,1,2,3,4,5])\n", "ax[0].set_xlabel(r'$i$') \n", "ax[0].set_ylabel(r'$\\kappa_i$') \n", "ax[0].set_title('Eigenvalues $\\mathcal{Q}$')\n", "\n", "ax[1].plot(tau, Kevals[0,:], 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON>s[1,:], 'b', label = r'$\\lambda_1(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[2,:], 'r', label = r'$\\lambda_2(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[3,:], 'g', label = r'$\\lambda_3(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[4,:], 'y', label = r'$\\lambda_4(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[5,:], 'c', label = r'$\\lambda_5(\\tau)$');\n", "ax[1].set_xlabel(r'$\\tau$ / ps') \n", "ax[1].set_ylabel(r'$\\lambda_i(\\tau)$') \n", "ax[1].set_title(r'Eigenvalues $\\mathcal{K}_{\\tau}$')\n", "ax[1].legend(loc='upper right', fontsize=9)\n", "\n", "#ax[2].plot(tau, - tau / np.log(Kevals[0,:]), 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n", "ax[2].set_xlabel(r'$\\tau$ / ps') \n", "ax[2].set_ylabel(r'$t_i(\\tau)$') \n", "ax[2].set_title(r'Timescales $t_i(\\tau)$')\n", "ax[2].legend(loc='upper right',fontsize=9)\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "#fig.savefig('evals.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f071c7067a9c475995a219520aa905df", "version_major": 2, "version_minor": 0}, "image/png": "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********************************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********************************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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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********************************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********************************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' width=629.9212598425196/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(6, 2, figsize=(16*in2cm, 16*in2cm), facecolor='white')\n", "\n", "\n", "####################################################################################################\n", "y1 = np.zeros(x.shape)\n", "y2 = np.real(Qevecs[:,0])\n", "phi0 = y2 / np.sum( y2*dx)\n", "ax[0,0].plot(x, phi0, 'k-', label= r'$\\varphi_0$');\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,0].set_title(r'Eigenfunctions of $\\mathcal{Q}^*, \\mathcal{P}_{\\tau}$')\n", "ax[0,0].set_ylim(0,1.3)\n", "ax[0,0].legend(loc='upper left')\n", "ax[0,0].set_xticks([])\n", "\n", "y2 = np.real(Kevecs[:,0])\n", "psi0 = y2 / y2\n", "ax[0,1].plot(x, psi0 , 'k-', label= r'$\\psi_0$');\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,1].set_ylim(0,1.3)\n", "ax[0,1].set_title(r'Eigenfunctions of $\\mathcal{Q}, \\mathcal{K}_{\\tau}$')\n", "ax[0,1].legend(loc='upper left')\n", "ax[0,1].set_xticks([])\n", "\n", "####################################################################################################\n", "phi1 = np.real(Qevecs[:,1])\n", "ax[1,0].plot(x, phi1 , 'k-', label= r'$\\varphi_1$');\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,0].legend(loc='upper left')\n", "ax[1,0].set_xticks([])\n", "ax[1,0].set_ylim(-0.4,0.4)\n", "\n", "psi1 =  np.real(-Kevecs[:,1])\n", "ax[1,1].plot(x, psi1 , 'k-', label= r'$\\psi_1$');\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,1].legend(loc='upper left')\n", "ax[1,1].set_xticks([])\n", "ax[1,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi2 = -np.real(Qevecs[:,2])\n", "ax[2,0].plot(x, phi2 , 'k-', label= r'$\\varphi_2$');\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,0].legend(loc='upper left')\n", "ax[2,0].set_xticks([])\n", "ax[2,0].set_ylim(-0.4,0.4)\n", "\n", "psi2 = np.real(Kevecs[:,2])\n", "ax[2,1].plot(x, psi2 , 'k-', label= r'$\\psi_2$');\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,1].legend(loc='upper left')\n", "ax[2,1].set_xticks([])\n", "ax[2,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi3 = np.real(Qevecs[:,3])\n", "ax[3,0].plot(x, phi3 , 'k-', label= r'$\\varphi_3$');\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,0].legend(loc='upper left')\n", "ax[3,0].set_xticks([])\n", "ax[3,0].set_ylim(-0.4,0.4)\n", "\n", "psi3 = np.real(Kevecs[:,3])\n", "ax[3,1].plot(x, psi3 , 'k-', label= r'$\\psi_3$');\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,1].legend(loc='upper left')\n", "ax[3,1].set_xticks([])\n", "ax[3,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi4 = np.real(Qevecs[:,4])\n", "ax[4,0].plot(x, phi4 , 'k-', label= r'$\\varphi_4$');\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,0].legend(loc='upper left')\n", "ax[4,0].set_xticks([])\n", "ax[4,0].set_ylim(-0.4,0.4)\n", "\n", "psi4 = - np.real(Kevecs[:,4])\n", "ax[4,1].plot(x, psi4 , 'k-', label= r'$\\psi_4$');\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,1].legend(loc='upper left')\n", "ax[4,1].set_xticks([])\n", "ax[4,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi5 = np.real(Qevecs[:,5])\n", "ax[5,0].plot(x, phi5 , 'k-', label= r'$\\varphi_5$');\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,0].legend(loc='upper left')\n", "ax[5,0].set_xticks([])\n", "ax[5,0].set_ylim(-0.4,0.4)\n", "ax[5,0].set_xlabel(r'$x$ / nm')\n", "\n", "psi5 = - np.real(Kevecs[:,5])\n", "\n", "ax[5,1].plot(x, psi5 , 'k-', label= r'$\\psi_5$');\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,1].legend(loc='upper left')\n", "ax[5,1].set_xticks([])\n", "ax[5,1].set_ylim(-0.4,0.4)\n", "ax[5,1].set_xlabel(r'$x$ / nm')\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.1)\n", "#fig.savefig('evecs.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, '$\\\\psi_1$')"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ab12ad911e364ac2a06d86c81d276ae4", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=472.4409448818897/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(1, 1, figsize=(12*in2cm, 8*in2cm), facecolor='white')\n", "ax.plot(psi0, psi1,  'ko')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$\\psi_1$')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, '$x$')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0ed739b0f4f84862b669b9518b9a923a", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=472.4409448818897/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pcca import pcca\n", "\n", "chi = pcca(<PERSON><PERSON>to<PERSON>(), 2)[0]\n", "\n", "fig, (ax) = plt.subplots(1, 1, figsize=(12*in2cm, 8*in2cm), facecolor='white')\n", "ax.plot(x, chi[:,0],  'b')\n", "ax.plot(x, chi[:,1],  'r')\n", "\n", "ax.set_xlabel(r'$x$')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, '$\\\\psi_1$')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "15af67145ef441449ceb511927baacae", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=472.4409448818897/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(1, 1, figsize=(12*in2cm, 8*in2cm), facecolor='white')\n", "ax.plot(chi[:,0],chi[:,1],  'k.')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$\\psi_1$')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}