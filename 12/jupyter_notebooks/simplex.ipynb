{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bfd1e2f0-7a2d-4f08-aa19-74b7788c3f7e", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches\n", "#%matplotlib ipympl\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6444af12-6c64-4a0c-9bd4-331e876e33bb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 629.921x314.961 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["chi0  = np.random.uniform(0,1, 50)\n", "\n", "c    = np.random.uniform(0,1, (100,3))\n", "chi  = np.copy(c)\n", "for i in range(3):\n", "    chi[:,i] = chi[:,i] / np.sum(c, axis= 1)\n", "\n", "fig = plt.figure(figsize=(16*in2cm, 8*in2cm))\n", "\n", "ax = fig.add_subplot(1,3,1)\n", "ax.plot(chi0, np.ones(50),  'ko')\n", "ax.set_xlabel(r'$e_1$')\n", "ax.set_title('0-simplex')\n", "ax.set_xticks([0,1])\n", "ax.set_yticks([])\n", "ax.set_aspect('equal')\n", "\n", "ax = fig.add_subplot(1,3,2)\n", "ax.plot(chi0, 1-chi0,  'ko')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$e_1$')\n", "ax.set_ylabel(r'$e_2$')\n", "ax.set_title('1-simplex')\n", "ax.set_xticks([0,1])\n", "ax.set_yticks([0,1])\n", "\n", "ax = fig.add_subplot(1,3,3, projection='3d')\n", "ax.plot(chi[:,0], chi[:,1], chi[:,2], 'k.')\n", "#ax.plot(0.333, 0.333, 0.333, 'ro')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$e_1$',labelpad=-10)\n", "ax.set_ylabel(r'$e_2$',labelpad=-10)\n", "ax.set_zlabel(r'$e_3$',labelpad=-10)\n", "ax.set_xticks([0,1])\n", "ax.set_yticks([0,1])\n", "ax.set_zticks([0,1])\n", "\n", "ax.set_title('2-simplex')\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "fig.savefig('simplex.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "id": "0db9063d-e7ce-4f42-a19a-00648daf79cc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}