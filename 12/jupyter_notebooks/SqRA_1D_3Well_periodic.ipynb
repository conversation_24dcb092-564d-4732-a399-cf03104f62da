{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Estimate rates from SqRA rate matrix 1D"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T06:11:52.049504Z", "start_time": "2020-11-13T06:11:51.357462Z"}}, "outputs": [], "source": ["import sys\n", "\n", "#%matplotlib ipympl\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "import matplotlib.cm as cm\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adjacency matrix"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def adjancency_matrix_sparse(nbins, nd, periodic=False):\n", "    v = np.zeros(nbins)\n", "    v[1] = 1\n", "    \n", "    if periodic:\n", "        v[-1] = 1\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.circulant(v)) #.toarray()\n", "    else:\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.toeplitz(v)) #.toarray()\n", "    \n", "    A = A0\n", "    I2 = scipy.sparse.eye(nbins)  #np.eye(nbins)\n", "    for _ in range(1, nd):\n", "        I1 = scipy.sparse.eye(*A.shape) #np.eye(*A.shape)\n", "        A =  scipy.sparse.kron(A0, I1) + scipy.sparse.kron(I2, A)\n", "    return A"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1D system"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 472.441x314.961 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# System parameters\n", "kB    = 0.008314463\n", "T     = 300\n", "mass  = 1\n", "gamma = 1\n", "D     = kB * T / mass / gamma\n", "sigma = np.sqrt(2 * D)\n", "beta  = 1 / kB / T\n", "\n", "# Potential energy function\n", "def V(x):\n", "    return 14*(x**3 - 1.5*x)**2 - x**3 + x\n", "\n", "def V(x):\n", "    return 5 * np.cos(3*x) + 5\n", "    \n", "\"\"\"\n", "def V(x):\n", "    a = 0.06\n", "    #return - 1 / beta * np.log( np.exp( - ( x - 1 ) ** 2 / a ) + np.exp( - ( x + 1 ) ** 2 / a ) )\n", "    return - 1 / beta * np.log( np.exp( - ( x - 0.5 ) ** 2 / a ) + np.exp( - ( x + 0.5 ) ** 2 / a ) +\n", "                                np.exp( - ( x - 1.5 ) ** 2 / a ) + np.exp( - ( x + 1.5 ) ** 2 / a ) )\n", "\"\"\"\n", "\n", "\n", "# Grid\n", "nd     = 1  # Number of dimensions\n", "nedges = 121 # State boundaries\n", "xmin   = -np.pi #2\n", "xmax   =  np.pi #2\n", "\n", "x      = np.linspace(xmin, xmax, nedges)  # array with x edges\n", "dx     = x[1] - x[0]\n", "x      = x[:-1] + (dx / 2)                # array with x centers\n", "xbins  = nedges - 1\n", "Nbins  = xbins**nd                        # number of bins\n", "\n", "fig, (ax1) = plt.subplots(1, 1, figsize=(12*in2cm, 8*in2cm), facecolor='white')\n", "###############################################\n", "ax1.plot(x, kB*T*np.ones(x.shape), '--', color='gold')\n", "ax1.text(-1.95, kB*T+0.5, r'$k_B T$', fontsize = 10)\n", "ax1.plot(x, V(x), 'k', label = 'Potential') \n", "\n", "#ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$ / nm')\n", "ax1.set_ylabel(r'$V(x)$ / kJ mol$^{-1}$')\n", "#ax1.legend()\n", "ax1.set_ylim((-15, 18))\n", "#ax1.set_xlim((-2, 2))\n", "\n", "\n", "ax1.set_title('Potential energy function')\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "fig.savefig('potential3Wellper.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Build SqRA 1D"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5672\\815605921.py:33: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  Kevals[:,i] = np.exp(Qevals * tau[i])\n"]}], "source": ["A  = adjancency_matrix_sparse(Nbins, nd, periodic=True)\n", "\n", "# Potential energy of states\n", "v = V(x)\n", "\n", "# Flux\n", "flux = D / dx**2\n", "Af   = flux * A\n", "\n", "# Diagonalization\n", "SQRA = np.sqrt(np.exp(- beta * v))\n", "SQRA = SQRA / sum(SQRA)\n", "Di   = scipy.sparse.spdiags(SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) #.toarray()\n", "D1   = scipy.sparse.spdiags(1/SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)       \n", "Q    = D1 * Af * Di\n", "\n", "Q            = Q + scipy.sparse.spdiags(-Q.sum(axis=1).T, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\n", "Qeval<PERSON>, Qevecs = scipy.sparse.linalg.eigs(Q.T, 6, which='LR')\n", "idx    = np.argsort( - np.real(Qevals))\n", "Qevals = Qevals[idx]\n", "Qevecs = Qevecs[:,idx]\n", "\n", "\n", "<PERSON><PERSON><PERSON>, Kevecs = scipy.sparse.linalg.eigs(Q, 6, which='LR')\n", "idx    = np.argsort( - np.real(Kevals))\n", "Kevals = Kevals[idx]\n", "Kevecs = Kevecs[:,idx]\n", "\n", "tau    = np.linspace(0,1,100)\n", "\n", "Kevals = np.zeros((6,100))\n", "for i in range(100):\n", "    Kevals[:,i] = np.exp(Qevals * tau[i])"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\emma\\lib\\site-packages\\matplotlib\\cbook\\__init__.py:1369: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  return np.asarray(x, float)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5672\\1380550366.py:27: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5672\\1380550366.py:28: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5672\\1380550366.py:29: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5672\\1380550366.py:30: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5672\\1380550366.py:31: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 787.402x275.591 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(1, 3, figsize=(20*in2cm, 7*in2cm), facecolor='white')\n", "\n", "\n", "ax[0].plot(0, Qevals[0], 'ko');\n", "ax[0].plot(1, <PERSON><PERSON><PERSON>[1], 'bo');\n", "ax[0].plot(2, <PERSON><PERSON><PERSON>[2], 'ro');\n", "ax[0].plot(3, <PERSON><PERSON><PERSON>[3], 'go');\n", "ax[0].plot(4, <PERSON><PERSON><PERSON>[4], 'yo');\n", "ax[0].plot(5, <PERSON><PERSON><PERSON>[5], 'co');\n", "ax[0].set_xticks([0,1,2,3,4,5])\n", "ax[0].set_xlabel(r'$i$') \n", "ax[0].set_ylabel(r'$\\kappa_i$') \n", "ax[0].set_title('Eigenvalues $\\mathcal{Q}$')\n", "\n", "ax[1].plot(tau, Kevals[0,:], 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON>s[1,:], 'b', label = r'$\\lambda_1(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[2,:], 'r', label = r'$\\lambda_2(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[3,:], 'g', label = r'$\\lambda_3(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[4,:], 'y', label = r'$\\lambda_4(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[5,:], 'c', label = r'$\\lambda_5(\\tau)$');\n", "ax[1].set_xlabel(r'$\\tau$ / ps') \n", "ax[1].set_ylabel(r'$\\lambda_i(\\tau)$') \n", "ax[1].set_title(r'Eigenvalues $\\mathcal{K}_{\\tau}$')\n", "ax[1].legend(loc='upper right', fontsize=9)\n", "\n", "#ax[2].plot(tau, - tau / np.log(Kevals[0,:]), 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n", "ax[2].set_xlabel(r'$\\tau$ / ps') \n", "ax[2].set_ylabel(r'$t_i(\\tau)$') \n", "ax[2].set_title(r'Timescales $t_i(\\tau)$')\n", "ax[2].legend(loc='upper right',fontsize=9)\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "fig.savefig('evals3Wellper.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 629.921x629.921 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(6, 2, figsize=(16*in2cm, 16*in2cm), facecolor='white')\n", "\n", "\n", "####################################################################################################\n", "y1 = np.zeros(x.shape)\n", "y2 = np.real(Qevecs[:,0])\n", "phi0 = y2 / np.sum( y2*dx)\n", "ax[0,0].plot(x, phi0, 'k-', label= r'$\\varphi_0$');\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,0].set_title(r'Eigenfunctions of $\\mathcal{Q}^*, \\mathcal{P}_{\\tau}$')\n", "ax[0,0].set_ylim(0,1.3)\n", "ax[0,0].legend(loc='upper left')\n", "ax[0,0].set_xticks([])\n", "\n", "y2 = np.real(Kevecs[:,0])\n", "psi0 = y2 / y2\n", "ax[0,1].plot(x, psi0 , 'k-', label= r'$\\psi_0$');\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,1].set_ylim(0,1.3)\n", "ax[0,1].set_title(r'Eigenfunctions of $\\mathcal{Q}, \\mathcal{K}_{\\tau}$')\n", "ax[0,1].legend(loc='upper left')\n", "ax[0,1].set_xticks([])\n", "\n", "####################################################################################################\n", "phi1 = np.real(Qevecs[:,1])\n", "ax[1,0].plot(x, phi1 , 'k-', label= r'$\\varphi_1$');\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,0].legend(loc='upper left')\n", "ax[1,0].set_xticks([])\n", "ax[1,0].set_ylim(-0.4,0.4)\n", "\n", "psi1 =  np.real(-Kevecs[:,1])\n", "ax[1,1].plot(x, psi1 , 'k-', label= r'$\\psi_1$');\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,1].legend(loc='upper left')\n", "ax[1,1].set_xticks([])\n", "ax[1,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi2 = -np.real(Qevecs[:,2])\n", "ax[2,0].plot(x, phi2 , 'k-', label= r'$\\varphi_2$');\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,0].legend(loc='upper left')\n", "ax[2,0].set_xticks([])\n", "ax[2,0].set_ylim(-0.4,0.4)\n", "\n", "psi2 = np.real(Kevecs[:,2])\n", "ax[2,1].plot(x, psi2 , 'k-', label= r'$\\psi_2$');\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,1].legend(loc='upper left')\n", "ax[2,1].set_xticks([])\n", "ax[2,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi3 = np.real(Qevecs[:,3])\n", "ax[3,0].plot(x, phi3 , 'k-', label= r'$\\varphi_3$');\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,0].legend(loc='upper left')\n", "ax[3,0].set_xticks([])\n", "ax[3,0].set_ylim(-0.4,0.4)\n", "\n", "psi3 = np.real(Kevecs[:,3])\n", "ax[3,1].plot(x, psi3 , 'k-', label= r'$\\psi_3$');\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,1].legend(loc='upper left')\n", "ax[3,1].set_xticks([])\n", "ax[3,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi4 = np.real(Qevecs[:,4])\n", "ax[4,0].plot(x, phi4 , 'k-', label= r'$\\varphi_4$');\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,0].legend(loc='upper left')\n", "ax[4,0].set_xticks([])\n", "ax[4,0].set_ylim(-0.4,0.4)\n", "\n", "psi4 = - np.real(Kevecs[:,4])\n", "ax[4,1].plot(x, psi4 , 'k-', label= r'$\\psi_4$');\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,1].legend(loc='upper left')\n", "ax[4,1].set_xticks([])\n", "ax[4,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi5 = np.real(Qevecs[:,5])\n", "ax[5,0].plot(x, phi5 , 'k-', label= r'$\\varphi_5$');\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,0].legend(loc='upper left')\n", "ax[5,0].set_xticks([])\n", "ax[5,0].set_ylim(-0.4,0.4)\n", "ax[5,0].set_xlabel(r'$x$ / nm')\n", "\n", "psi5 = - np.real(Kevecs[:,5])\n", "\n", "ax[5,1].plot(x, psi5 , 'k-', label= r'$\\psi_5$');\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,1].legend(loc='upper left')\n", "ax[5,1].set_xticks([])\n", "ax[5,1].set_ylim(-0.4,0.4)\n", "ax[5,1].set_xlabel(r'$x$ / nm')\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.1)\n", "fig.savefig('evecs3Wellper.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pcca import pcca\n", "\n", "chi = pcca(<PERSON><PERSON>to<PERSON>(), 3)[0]"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1102.36x314.961 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = plt.figure(figsize=(28*in2cm, 8*in2cm))\n", "\n", "ax = fig.add_subplot(1,3,1,projection='3d')\n", "ax.plot(psi0, psi1, psi2, 'k.')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$\\psi_0$',labelpad=-10)\n", "ax.set_ylabel(r'$\\psi_1$',labelpad=-10)\n", "ax.set_zlabel(r'$\\psi_2$',labelpad=-10)\n", "ax.set_xticks([0.9,1.1])\n", "ax.set_yticks([-0.1,0.1])\n", "ax.set_zticks([-0.1,0.1])\n", "ax.set_title(r'2-simplex $\\psi_0, \\psi_1, \\psi_2$')\n", "\n", "\n", "ax = fig.add_subplot(1,3,2,projection='3d')\n", "ax.plot(chi[:,0], chi[:,1], chi[:,2], 'k.')\n", "ax.set_aspect('equal')\n", "ax.set_xlabel(r'$\\chi_0$',labelpad=-10)\n", "ax.set_ylabel(r'$\\chi_1$',labelpad=-10)\n", "ax.set_zlabel(r'$\\chi_2$',labelpad=-10)\n", "ax.set_xticks([0,1])\n", "ax.set_yticks([0,1])\n", "ax.set_zticks([0,1])\n", "ax.set_title(r'2-simplex $\\chi_0, \\chi_1, \\chi_2$')\n", "\n", "ax = fig.add_subplot(1,3,3)\n", "ax.plot(x, chi[:,0],  'b', label=r'$\\chi_0$')\n", "ax.plot(x, chi[:,1],  'r', label=r'$\\chi_1$')\n", "ax.plot(x, chi[:,2],  'g', label=r'$\\chi_2$')\n", "ax.legend()\n", "ax.set_xlabel(r'$x$')\n", "ax.set_title('Membership functions')\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.08)\n", "fig.savefig('memFuncs3Wellper.png', format='png', dpi=900, bbox_inches='tight')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}