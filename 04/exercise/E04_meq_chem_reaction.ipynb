{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f287a3a5", "metadata": {}, "outputs": [], "source": ["# Import useful libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "# from scipy import special\n", "from scipy.integrate import solve_ivp"]}, {"cell_type": "markdown", "id": "34e8dd31", "metadata": {}, "source": ["# Chemical reaction\n", "Consider the chemical reaction \n", "\n", "$$\n", "A + B \\rightleftharpoons C \\, ,\n", "$$\n", "\n", "where $A$ and $B$ are two molecules that react to form the molecule $C$.\n", "The forward rate at which an individual atom $A$ combines with an atom $B$ to form an atom $C$ is denoted by $r_F$.\n", "While the backward reaction occurs at rate $r_B$.\n", "\n", "However, in a system with $n_A(t)$, $n_B(t)$ and $n_C(t)$ atoms at time $t$, the probability of the forward reaction depends on the probability that an atom $A$ meets an atom $B$.\n", "Then, the forward rate must take into account how many atoms $A$ and $B$ are present in the system with respect to the system size $\\Omega$.\n", "The reason for this is that $A$ and $B$ tend to react more readily when there is a significant concentration of $A$ and $B$.\n", "\n", "Conversely, the breaking up of a $C$-molecule into its constituent atoms involves only the molecule $C$.\n", "Then, the time evolution of $n_C(t)$ depends on the absolute number $n_C(t)$, independently on the system size.\n", "\n", "Here, the system size is an extensive parameter that quantifies the relative magnitude of the reactants and of the products.\n", "What precisely that parameter is will depend upon the problem at hand.\n", "It could be either a capacitance (in electrical applications) or a volume or the number of individuals in a population. \n", "\n", "We conclude that, given $n_A(t)$ atoms at time $t$, the system evolves to\n", "\n", "$$\n", "n_A(t+\\Delta t)  =  n_A(t) + r_B \\, \\Delta t n_C(t) - r_F \\frac{n_B(t)}{\\Omega} \\Delta t \\, n_A(t) \\, .\n", "$$\n", "\n", "In other words, $n_A$ increases proportionally to $n_C$ multiplied by the rate $r_B$, and decreases proportionally to  $n_A$  multiplied by the rate $r_F \\frac{n_B}{\\Omega}$.   \n", "\n", "Moving $n_A(t)$ to the LHS, dividing by $\\Delta t$ and taking the limit $\\Delta t \\rightarrow 0$ yields the chemical equation\n", "\n", "$$\n", "\\dot{n}_A = r_B\\, n_{C} - r_F \\frac{n_B}{\\Omega}\\, n_A n_B \\, .\n", "$$\n", "\n", "This equation is called reaction rate as it expresses how the rate of $A$ atoms evolves with time.\n", "The rate of $n_A$ is given as the difference between the forward rate $r_F \\frac{n_B}{\\Omega}\\, n_A n_B$ and the backward rate $r_B\\, n_{C}$.\n", "\n", "Likewise, one gets the equations for $n_B$ and $n_C$:\n", "\n", "\\begin{equation}\n", "\\begin{aligned}\n", "\\dot{n}_A   &= r_B\\, n_{C} - r_F\\frac{n_B}{\\Omega}\\, n_A \\\\\n", "\\dot{n}_B   &= r_B\\, n_{C} - r_F\\frac{n_A}{\\Omega}\\, n_B \\\\\n", "\\dot{n}_{C} &= r_F\\, \\frac{n_A n_B}{\\Omega} - r_B\\, n_{C}  .\n", "\\end{aligned}\n", "\\end{equation}"]}, {"cell_type": "markdown", "id": "27131300", "metadata": {}, "source": ["To reduce the dimensionality, we consider the case where $n_A(t)=n_B(t)=n(t)$, i.e. the number of atoms $A$ is equal to the number of atoms $B$.\n", "It follows, that the number $N = n(t) + n_C(t)$ is constant over the time, and the number of $C$ atoms is $n_C(t) = N - n(t)$. \n", "\n", "Here, we can use the parameter $N$ as system size: $\\Omega = N$.\n", "However, it should not be confused with the total number of atoms $N+n(t)$ which changes with time.\n", "\n", "The system of equations is replaced by:\n", "\n", "\\begin{equation}\n", "\\begin{aligned}\n", "\\dot{n}     &= r_B\\, (N - n) - r_F\\, \\frac{n^2}{N} \\cr\n", "\\dot{n}     &= r_B\\, (N - n) - r_F\\,\\, \\frac{n^2}{N} \\cr\n", "N - \\dot{n} &= r_F\\, \\frac{n^2}{N} - r_B\\, (N - n)  \\, .\n", "\\end{aligned}\n", "\\end{equation}\n", "\n", "In the last equation, we used $\\dot{n_c} = \\frac{\\mathrm{d}n_c}{\\mathrm{d} t} = \\frac{\\mathrm{d}(N - n)}{\\mathrm{d} t}  = N -\\frac{\\mathrm{d} n}{\\mathrm{d} t}$.\n", "\n", "Rearranging the last equation, we obtain\n", "\n", "\\begin{equation}\n", "\\begin{aligned}\n", "\\dot{n} &= N - r_B\\, (N - n) - r_F\\, \\frac{n^2}{N} \\, .\n", "\\end{aligned}\n", "\\end{equation}\n", "\n", "In conclusion, the system is fully determined by the variable $n(t)$ and the dynamics is governed by the equation\n", "\n", "$$\n", "\\dot{n} = r_B\\, (N - n) - r_F\\, \\frac{n^2}{N} \\, , \n", "$$\n", "\n", "where $\\dot{n}$ is the rate of $n$, $r_B\\, (N - n)$ is the backward rate, and $r_F\\, \\frac{n^2}{N}$ is the forward rate.\n", "\n", "Dividing by $N$, we obtain the equivalent equation for the macroscopic part derived by Linear Noise Approximation:\n", "\n", "$$\n", "\\dot{\\varphi} = r_B\\, (1 - \\varphi) - r_F\\, \\varphi^2 \\, .\n", "$$"]}, {"cell_type": "markdown", "id": "7df410d5", "metadata": {}, "source": ["### Gillespie algorithm\n", "1. We estimate the total rate at time $t$:\n", "\n", "$$\n", "r(t) = r_B(N - n(t)) + r_F \\frac{n(t)^2}{N} \\, . \n", "$$\n", "\n", "2. At time $t$, we draw the length of the next step $\\tau$ from the distribution\n", "\n", "$$\n", "r(t)  \\exp\\left(-r(t)\\tau\\right) \\mathrm{d}\\tau \\, ,\n", "$$\n", "\n", "that describes the probability to change the system state in the time interval $[t + \\tau, t + \\tau +\\mathrm{d}\\tau]$, with $\\mathrm{d}\\tau$ infinitesimal time step.\n", "\n", "Calculating the CDF and by exploiting the probability integral transform theorem, the time interval $\\tau$ can be calculated as\n", "\n", "$$\n", "\\tau = - \\frac{\\log(u_1)}{r(t)} \\, ,\n", "$$\n", "with $u_1\\in \\mathcal{U}(0,1)$ random number from a uniform distribution.\n", "\n", "3. The probability that the forward reaction occurs is $P_F(t)= r_F \\frac{n(t)^2}{N} \\frac{1}{r(t)}$, while the probability of the backward reaction is $P_B(t)= r_B(N - n(t)) \\frac{1}{r(t)}$\n", "4. We draw a second random number $u_2$ from a uniform distribution $\\mathcal{U}(0,1)$\n", "5. If $u_2<P_f$ the forward reaction occurs, otherwise the bacward reaction occurs."]}, {"cell_type": "code", "execution_count": 7, "id": "3d4346e4", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Nreps  = 1000 # Number of simulations\n", "Gsteps = 60   # Number of Gillispie time-steps (iterations)\n", "real_time = np.zeros((<PERSON><PERSON><PERSON>, Nreps))\n", "N  = 50 # System size\n", "n0 = 10 # Initial populations: n_a = n_b = n, then n_c = N - n\n", "n      = np.zeros((<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>))\n", "n[0,:] = n0 * np.ones(Nreps)\n", "\n", "# Rates of individual reactions (constants)\n", "rF = 0.5\n", "rB = 0.1\n", "\n", "for t in range(Gsteps-1):\n", "  r  = rF * n[t,:] * n[t,:] / N + rB * (N - n[t,:]) # total rate\n", "  Pf  = rF * n[t,:] * n[t,:] / N / r # probability of the forward reaction\n", "\n", "  # sample the time interval\n", "  u1 = np.random.uniform(0, 1, (Nreps))\n", "  tau = - np.log(u1) / r\n", "  real_time[t+1,:] = real_time[t,:] + tau\n", "\n", "  # sample the reaction\n", "  u2 = np.random.uniform(0, 1, (Nreps))\n", "  for rep in range(Nreps):\n", "    # Forward reaction\n", "    if  u2[rep] <= Pf[rep]:\n", "      n[t+1,rep]  = n[t,rep] - 1\n", "    # Backward reaction\n", "    elif u2[rep] > Pf[rep]:\n", "      n[t+1,rep]  = n[t,rep] + 1\n", "\n", "plt.figure(figsize=(5, 4))\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "plt.plot(real_time[:,0], n[:,0]/N,  color='b', label = r'$n(t) / N$')\n", "plt.plot(real_time[:,0], 1-n[:,0]/N,  color='r', label = r'$1 - n(t) / N$')\n", "plt.plot(real_time[:,0:-1:10], n[:,0:-1:10]/N,  color='b')\n", "plt.plot(real_time[:,0:-1:10], 1-n[:,0:-1:10]/N,  color='r')\n", "plt.legend()\n", "plt.xlabel('time / ps')\n", "plt.ylabel(r'$N_A(t)$');"]}, {"cell_type": "markdown", "id": "398a9cc4", "metadata": {}, "source": ["The trajectories stored in the variable **n** have different time lengths stored in the variable **real_time**. Addittionally, they are discretized with different timesteps **tau**.\n", "To estimate the average trajectory and the variance of the fluctuations, we create an array **tvalues** of time values equally spaced.\n", "timestep\n", "\n", "The average value of **n** at time **tvalues[i+1]** is obtained from all the points (i.e. from all simulations) that fall in the interval **[tvalues[i], tvalues[i+1]]**.\n", "\n", "<img src=\"avg.png\" alt=\"Drawing\" style=\"width: 500px;\"/>"]}, {"cell_type": "code", "execution_count": 8, "id": "ef28c410", "metadata": {}, "outputs": [], "source": ["# Find minimum max path length\n", "maxstep = np.max(real_time)\n", "\n", "# Number of timesteps in the new time-discretization\n", "Nsteps = 30\n", "\n", "# Array of t values, use the maximum time as limit\n", "tvalues = np.linspace(0, maxstep, Nsteps)\n", "\n", "# Grid for histogram\n", "nmin     = 0\n", "nmax     = N\n", "nvalues  = 1 + nmax - nmin\n", "nedges   = np.linspace(nmin, nmax, nvalues, dtype = int) \n", "ncenters = nedges + 0.5\n", "ncenters = np.delete(ncenters,-1)\n", "nvalues  = len(ncenters)\n", "\n", "# Array for histogram\n", "H = np.zeros((Nsteps, nvalues))\n", "\n", "# Histogram of the first step\n", "h = np.histogram(n[0,:].T, bins=nedges, density=True)\n", "H[0,:]   = h[0]\n", "\n", "# Array for mean trajectory (from Gillespie simulations)\n", "mean_n = np.zeros(Nsteps)\n", "mean_n[0] = n0\n", "\n", "# Array for average fluctuations\n", "var_f  = np.zeros(Nsteps)\n", "\n", "for i in range(Nsteps-1):\n", "  ti   =  tvalues[i]      # t_i\n", "  ti1  =  tvalues[i+1]    # t_{i+1}\n", "  tcount = 0\n", "\n", "  #values that fall in [t_i, t_{i+1}]\n", "  el_n = []\n", "  for g in range(Gsteps):\n", "    # Check if the considered real_time[g,:] falls in the interval [t_i, t_{i+1}]\n", "    idx    =  ((ti <= real_time[g,:]) & (real_time[g,:] < ti1))    \n", "    tcount =  tcount + np.sum(idx)\n", "    mean_n[i+1] =  mean_n[i+1] + np.sum(n[g,idx])\n", "    if n[g,idx].size > 0:\n", "      el_n.append(np.array(n[g,idx]))\n", "\n", "  mean_n[i+1] = mean_n[i+1] / tcount   \n", "  var_f[i+1]  = np.var(np.concatenate(el_n))    \n", "  h           = np.histogram(np.concatenate(el_n), bins=nedges, density=True)\n", "  H[i+1,:]    = h[0]"]}, {"cell_type": "markdown", "id": "b0d4c463", "metadata": {}, "source": ["Macroscopic part derived by Linear Noise Approximation:\n", "\n", "$$\n", "\\dot{\\varphi} = r_B\\, (1 - \\varphi) - r_F\\, \\varphi^2 \\, .\n", "$$\n", "\n", "Mean of the fluctuations:\n", "$$\n", "\\frac{\\partial \\langle x\\rangle}{\\partial t} \n", "=\n", "( - 2 r_F \\varphi - b) \\langle x\\rangle\n", "$$\n", "\n", "Variance of the fluctuations:\n", "$$\n", "\\frac{\\partial \\langle x^2\\rangle}{\\partial t} \n", "=\n", "2 ( - 2 r_F \\varphi - b) \\langle x^2\\rangle + \\varphi^2 r_F + r_B - \\varphi r_B\n", "$$"]}, {"cell_type": "code", "execution_count": 9, "id": "a551e7d1", "metadata": {}, "outputs": [], "source": ["def ODEs(t, z, rF, rB):\n", "  phi, x1, x2  =  z\n", "  dotphi  = - phi ** 2 * rF + rB * ( 1 - phi )\n", "  dotx1   = ( - 2 * rF * phi - rB ) * x1\n", "  dotx2   = 2 * ( - 2 * rF * phi - rB ) * x2 + phi ** 2 * rF + rB - phi * rB\n", "  return [dotphi, dotx1, dotx2]"]}, {"cell_type": "code", "execution_count": 12, "id": "d5dc2ae6", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sol = solve_ivp(ODEs, [0, maxstep], [n0/N, 0, 0], args=(rF, rB), method='RK45', dense_output=True)\n", "phi = sol.sol(tvalues)\n", "\n", "avg_phi = phi[0].T\n", "avg_x = phi[1].T\n", "var_x = phi[2].T\n", "\n", "fig, (ax1, ax2) = plt.subplots(1, 2,figsize=(10, 4))\n", "ax1.plot(real_time[:,0:-1:10], n[:,0:-1:10]/N,  '-', color='grey', alpha=0.5, linewidth  =2)\n", "ax1.plot(tvalues, mean_n/N, '--', color = 'red', label=r'$\\langle \\varphi(t) \\rangle$, Gillespie', linewidth  =2)\n", "ax1.plot(tvalues, avg_phi, 'k-', label=r'$\\langle \\varphi(t) \\rangle$, ODE', linewidth  =3)\n", "ax1.legend()\n", "ax1.set_title('Trajectories')\n", "ax1.set_xlabel('time / ps')\n", "ax1.set_ylabel(r'$\\varphi(t)$')\n", "ax1.set_title('Trajectory')\n", "\n", "ax2.plot(tvalues, mean_n/N - avg_phi, 'b--', label=r'$\\langle x(t) \\rangle$, Gillespie', linewidth  =2)\n", "ax2.plot(tvalues, avg_x, 'k-',   label=r'$\\langle x(t) \\rangle$, ODE', linewidth  =2)\n", "ax2.plot(tvalues, var_f/N,  'r--', label=r'$\\langle x(t)^2 \\rangle$, Gillespie', linewidth  =2)\n", "ax2.plot(tvalues, var_x,  'k-', label=r'$\\langle x(t)^2 \\rangle$, ODE', linewidth  =2)\n", "ax2.legend()\n", "ax2.set_title('Fluctuations');"]}, {"cell_type": "markdown", "id": "8be2f368", "metadata": {}, "source": ["$$\n", "\\Pi(x,t) = \\frac{1}{\\sqrt{2\\pi\\, \\langle x(t)^2 \\rangle}} \n", "\\exp\\left(\n", "-\\frac{1}{2}\n", "\\frac{\n", "\\left(\n", "x - \\langle x(t) \\rangle\n", "\\right)^2\n", "}\n", "{\n", "\\langle x(t)^2 \\rangle\n", "}\n", "\\right)\n", "$$\n", "\n", "$$\n", "P(n,t) = \\frac{1}{\\sqrt{N}}\\Pi\\left(\\frac{n - N \\varphi(t)}{\\sqrt{N}},t\\right)\n", "$$"]}, {"cell_type": "code", "execution_count": 16, "id": "3f5778cc", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def solutionFP(x,t):\n", "  return ( 2 * np.pi * var_x[t] ) ** (-0.5) * np.exp(-0.5 * (x - avg_x[t]) ** 2 * var_x[t] ** - 1 )\n", "\n", "# Grid for histogram\n", "xmin     = -2\n", "xmax     =  2\n", "xvalues  = 100\n", "xedges   = np.linspace(xmin, xmax, xvalues) \n", "dx       = xedges[1] - xedges[0]\n", "xcenters = xedges + 0.5 * dx\n", "xcenters = np.delete(xcenters,-1)\n", "xvalues  = len(xcenters)\n", "\n", "PI  = np.zeros((Nsteps, xvalues))\n", "h   = np.histogram(0, bins=xedges, density=True)\n", "PI[0,:]   = h[0]\n", "\n", "P = np.zeros((Nsteps, nvalues))\n", "h   = np.histogram(n0, bins=nedges, density=True)\n", "P[0,:]   = h[0]\n", "\n", "for t in range(1,Nsteps):\n", "  PI[t,:]   = solutionFP(xcenters, t)\n", "  P[t,:]    = solutionFP( (ncenters - N * avg_phi[t]) / np.sqrt(N), t) /  np.sqrt(N)\n", "\n", "fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(14, 4))\n", "\n", "extent = 0, maxstep, xmin, xmax\n", "ax1.imshow(PI.T, interpolation='bilinear', extent = extent, aspect='auto', origin='lower',vmin=0, vmax=2);\n", "ax1.set_xlabel('time / ps')\n", "ax1.set_ylabel('x')\n", "ax1.set_title(r'$\\Pi(x,t)$')\n", "\n", "extent = 0, maxstep, nmin/N, nmax/N\n", "ax2.imshow(P.T, interpolation='bilinear', extent = extent, aspect='auto', origin='lower',vmin=0, vmax=0.5);\n", "ax2.set_xlabel('time / ps')\n", "ax2.set_ylabel(r'$\\varphi(t)$')\n", "ax2.set_title(r'$P(n,t)$')\n", "\n", "extent = 0, maxstep, nmin/N, nmax/N\n", "ax3.imshow(H.T, interpolation='bilinear', extent = extent, aspect='auto', origin='lower',vmin=0, vmax=0.5);\n", "ax3.set_xlabel('time / ps')\n", "ax3.set_ylabel(r'$\\varphi(t)$')\n", "ax3.set_title('Histogram')\n", "\n", "fig.tight_layout();"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}