{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e3e231a3", "metadata": {}, "outputs": [], "source": ["# Import useful libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.special import factorial\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "\n", "# For animation\n", "# %matplotlib inline\n", "# import time\n", "# import pylab as pl\n", "# from IPython import display\n", "# from IPython.display import clear_output"]}, {"cell_type": "markdown", "id": "b246768e", "metadata": {}, "source": ["### Potential energy function\n", "$$\n", "V(x) = (x^2 - 1)^2 \\, .\n", "$$\n", "\n", "### Probability density function\n", "$$\n", "p(x) = \\frac{ e ^{-\\beta V(x)}}{\\int_\\mathbb{R} e ^{-\\beta V(x)}\\, \\mathrm{d}x'} \\, .\n", "$$\n", "\n", "### Cumulative density function\n", "$$\n", "F_X(x) = P(X\\leq x) \n", "= \\int_{-\\infty}^x p(x')\\, \\mathrm{d}x' \\, .\n", "$$\n", "\n", "### Inverse of cumulative density function (strictly increasing)\n", "$$\n", "F^{-1}_X(y)\\,=\n", "\\left\\{ x\\in \\mathbb{R} : F_X(x) = y \\right\\}, \\, \\mathrm{with}\\, y \\in [0,1] \\,.\n", "$$\n", "\n", "### Quantile function (generalized inverse function):\n", "$$\n", "F^{-1}_X(y)\\,=\\,\\inf\\left\\{ x\\in \\mathbb{R} : F_X(x) \\ge y \\right\\}, \\, \\mathrm{with}\\, y \\in [0,1] \\,.\n", "$$"]}, {"cell_type": "code", "execution_count": 2, "id": "01f93ea9", "metadata": {}, "outputs": [], "source": ["# x-Grid\n", "xmin  = -3\n", "xmax  = - xmin\n", "xbins = 400\n", "xedges = np.linspace(xmin,xmax,xbins)\n", "dx = xedges[1] - xedges[0]\n", "xcenters = xedges + 0.5* dx\n", "xcenters = np.delete(xcenters,-1)\n", "xbins = len(xcenters)\n", "\n", "# y-Grid\n", "ymin  = 0\n", "ymax  = 1\n", "ybins = 500\n", "yedges = np.linspace(ymin, ymax, ybins)\n", "dy = yedges[1] - yedges[0]\n", "ycenters = yedges + 0.5* dy\n", "ycenters = np.delete(ycenters, -1)\n", "yb0ins = len(ycenters)\n", "\n", "\n", "beta = 2.9\n", "def V(x):\n", "  return ( x**2 - 1 )**2\n", "\n", "def pdf(x):\n", "  return np.exp( - beta * V(x) ) / np.sum( np.exp( - beta * V(x) ) * dx )\n", "\n", "# Cumulative sum to approximate the CDF\n", "def cdf(x):\n", "  y = np.cumsum([ dx * pdf(xcenters) ])\n", "  return y\n", "\n", "# Quantile function or generalized inverse of cumulative distribution function\n", "def qf(y):\n", "  #Estimate the cdf\n", "  FX = cdf(xcenters)\n", "  \n", "  #Array where to store the values of x\n", "  x = np.zeros(len(y))\n", "  \n", "  # For each value in y, estimate the quantile:\n", "  # Search the values of the CDF greater than y\n", "  # The first value will be x\n", "  for i in range(len(y)):\n", "    # It is necessary to set a tolerance because FX[-1] is not exactly 1\n", "    tol = 1e-10\n", "    idx = FX >= (y[i] - tol)\n", "    x[i] = xcenters[idx][0]\n", "\n", "  return x"]}, {"cell_type": "code", "execution_count": 3, "id": "2070df9c", "metadata": {}, "outputs": [{"data": {"image/png": "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********************************************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", "text/plain": ["<Figure size 800x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax0,ax1) = plt.subplots(1,2, figsize=(8, 3))\n", "ax0.plot(xcenters,pdf(xcenters))\n", "ax0.set_xlabel('x')\n", "ax0.set_title('Probability density function')\n", "\n", "ax1.plot(xcenters,cdf(xedges),'-')\n", "ax1.set_xlabel('x')\n", "ax1.set_ylabel('y')\n", "\n", "ax1.set_title(r'Cumulative density function $y = F_X(x)$')\n", "\n", "plt.tight_layout();"]}, {"cell_type": "code", "execution_count": 10, "id": "c5abeaa3", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x300 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y = 0.2\n", "x = qf(np.array([y]))\n", "\n", "fig, (ax0,ax1,ax2) = plt.subplots(1,3, figsize=(10, 3))\n", "ax0.plot(xcenters,pdf(xcenters))\n", "ax0.set_xlabel('x')\n", "ax0.set_title('Probability density function')\n", "\n", "ax1.plot(xcenters,cdf(xedges),'-')\n", "ax1.plot(x,y,'ro')\n", "ax1.plot(xcenters[xcenters>x],cdf(xedges)[xcenters>x],'r--')\n", "\n", "ax1.set_xlabel('x')\n", "ax1.set_ylabel('y')\n", "\n", "ax1.set_title(r'Cumulative density function $y = F_X(x)$')\n", "\n", "\n", "ax2.plot(ycenters, qf(ycenters), '-')\n", "ax2.plot(y,x,'ro')\n", "ax2.plot(ycenters[ycenters>y], qf(ycenters)[ycenters>y], 'r--')\n", "\n", "ax2.set_xlabel('y')\n", "ax2.set_ylabel('x')\n", "\n", "ax2.set_title(r'Quantile function $x = F_X^{-1}(y)$')\n", "ax2.set_xlim((0,1))\n", "\n", "plt.tight_layout();"]}, {"cell_type": "code", "execution_count": 11, "id": "24e6d2cb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1,ax2) = plt.subplots(1,2, figsize=(8, 3))\n", "\n", "N = 100#10 100 1000\n", "y = np.random.uniform(0,1,N)\n", "tol = 1e-2\n", "#y = np.linspace(0+tol, 1-tol, N)\n", "x = qf(y)\n", "h  = np.histogram(x, bins=xedges, density=True)\n", "h0 = h[0]\n", "\n", "ax1.plot(xcenters, cdf(xedges),'r-', linewidth =4)\n", "ax1.plot(x, y, 'ko')\n", "\n", "ax2.bar(xcenters, h0, width=0.1)\n", "ax2.plot(xcenters, pdf(xcenters), 'r')\n", "ax2.set_xlabel('x')\n", "ax2.set_title('Probability density function')\n", "ax2.set_xlim((xmin, xmax))\n", "\n", "for n in range(N):\n", "  ax1.plot([xmin, x[n]], [y[n], y[n]], 'k-')\n", "  ax1.plot([x[n], x[n]], [0, y[n]], 'k-')\n", "  ax1.set_xlabel('x')\n", "  ax1.set_ylabel('y')\n", "  ax1.set_ylim((0,1))\n", "  ax1.set_xlim((xmin, xmax))\n", "\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}