{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8e2d00a8", "metadata": {}, "outputs": [], "source": ["# Import useful libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "# from IPython.display import display, Math\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "id": "1ed0d580", "metadata": {}, "source": ["# Stochastic calculus"]}, {"cell_type": "markdown", "id": "6e29deb8", "metadata": {}, "source": ["## <PERSON>iemann integral\n", "\n", "Let $f$ be a real-valued function on the interval $[a=0,b=\\tau]$, and let divide the $[a=0,b=\\tau]$ in $N$ intervals, then the <PERSON><PERSON><PERSON> integral is approximated as\n", "\n", "$$\n", "\\int_0^\\tau f(t) \\mathrm{d}t = \\lim_{N \\rightarrow +\\infty} \\sum_{k=0}^{N-1} f(\\hat{t}_k) \\left[t_{k+1} - t_k \\right] \\, ,\n", "$$\n", "\n", "with $\\hat{t}_k \\in [t_k, t_{k+1}]$."]}, {"cell_type": "code", "execution_count": 4, "id": "4c2d748f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def f(t):\n", "  return 3*t**2\n", "\n", "t0 = 0\n", "tau  = 1\n", "N = 5\n", "Delta_t = (tau - t0) / N\n", "\n", "tvaluesc = np.linspace(0,tau, 1000)\n", "plt.figure(figsize=(5, 3))\n", "plt.plot(tvaluesc, f(tvaluesc),'k')\n", "\n", "hat_t   = 1.0*Delta_t\n", "tvalues  = np.linspace(0,tau, N+1)\n", "\n", "# to create legend:\n", "plt.plot([tvalues[0], tvalues[0]], [0, 0], 'b', label = r'$\\hat{t}_k = t_{k+1}$')\n", "plt.plot([tvalues[0], tvalues[0]], [0, 0], 'r', label = r'$\\hat{t}_k = \\frac{1}{2}(t_{k+1} - t_k)$')\n", "plt.plot([tvalues[0], tvalues[0]], [0, 0], 'g', label = r'$\\hat{t}_k = t_k$')\n", "\n", "for k in range(N):\n", "  plt.plot([tvalues[k], tvalues[k+1]], [0, 0], 'b')\n", "  plt.plot([tvalues[k], tvalues[k]], [0, f(tvalues[k] + hat_t)], 'b')\n", "  plt.plot([tvalues[k] , tvalues[k+1]], [f(tvalues[k] + hat_t), f(tvalues[k] + hat_t)], 'b')\n", "  plt.plot([tvalues[k+1], tvalues[k+1]], [0, f(tvalues[k] + hat_t)], 'b')\n", "\n", "hat_t   = 0.5*Delta_t\n", "tvalues  = np.linspace(0,tau, N+1)\n", "\n", "for k in range(N):\n", "  plt.plot([tvalues[k], tvalues[k+1]], [0, 0], 'r')\n", "  plt.plot([tvalues[k], tvalues[k]], [0, f(tvalues[k] + hat_t)], 'r')\n", "  plt.plot([tvalues[k] , tvalues[k+1]], [f(tvalues[k] + hat_t), f(tvalues[k] + hat_t)], 'r')\n", "  plt.plot([tvalues[k+1], tvalues[k+1]], [0, f(tvalues[k] + hat_t)], 'r')\n", "\n", "hat_t   = 0.0*Delta_t\n", "tvalues  = np.linspace(0,tau, N+1)\n", "\n", "for k in range(N):\n", "  plt.plot([tvalues[k], tvalues[k+1]], [0, 0], 'g')\n", "  plt.plot([tvalues[k], tvalues[k]], [0, f(tvalues[k] + hat_t)], 'g')\n", "  plt.plot([tvalues[k] , tvalues[k+1]], [f(tvalues[k] + hat_t), f(tvalues[k] + hat_t)], 'g')\n", "  plt.plot([tvalues[k+1], tvalues[k+1]], [0, f(tvalues[k] + hat_t)], 'g')\n", "\n", "plt.xlabel(r'$t$')\n", "plt.ylabel(r'$f(t)$')\n", "plt.legend();"]}, {"cell_type": "code", "execution_count": 5, "id": "65908f81", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Nvalues = np.linspace(2,100,98,dtype=int)\n", "\n", "hat_t1   = 1.0 * Delta_t\n", "hat_t2   = 0.5 * Delta_t\n", "hat_t3   = 0.0 * Delta_t\n", "\n", "I1 = np.zeros(len(Nvalues))\n", "I2 = np.zeros(len(Nvalues))\n", "I3 = np.zeros(len(Nvalues))\n", "\n", "for n,N in enumerate(Nvalues):\n", "  Delta_t = (tau - t0) / N\n", "  hat_t1   = 1.0 * Delta_t\n", "  hat_t2   = 0.5 * Delta_t\n", "  hat_t3   = 0.0 * Delta_t\n", "  tvalues  = np.linspace(0,tau, N+1)\n", "\n", "  for k in range(N):\n", "    I1[n] = I1[n] + f(tvalues[k] + hat_t1) * Delta_t\n", "    I2[n] = I2[n] + f(tvalues[k] + hat_t2) * Delta_t\n", "    I3[n] = I3[n] + f(tvalues[k] + hat_t3) * Delta_t\n", "\n", "plt.figure(figsize=(5, 3))\n", "plt.plot(Nvalues, I1, 'b', label = r'$\\hat{t}_k = t_{k+1}$')\n", "plt.plot(Nvalues, I2, 'r', label = r'$\\hat{t}_k = \\frac{1}{2}(t_{k+1} - t_k)$')\n", "plt.plot(Nvalues, I3, 'g', label = r'$\\hat{t}_k = t_k$')\n", "plt.ylabel(r'$I(N)$')\n", "plt.xlabel(r'$N$')\n", "plt.legend();\n", "# plt.ylim((0,2));"]}, {"cell_type": "markdown", "id": "7f797455", "metadata": {}, "source": ["## Stochastic integral\n", "\n", "Instead of integrating over time values $[0,\\tau]$, we integrate the function $f$ over random variables $W_t$.\n", "\n", "$$\n", "\\int_0^T f(W_t) \\, \\mathrm{d}W_t = \\lim_{N \\rightarrow +\\infty} \\sum_{k=0}^{N-1} f(\\hat{W}_k) \\left[ W_{k+1} - W_k \\right] \\, ,\n", "$$\n", "\n", "with $\\hat{W}_k \\in [W_k, W_{k+1}]$.\n", "\n", "### Example\n", "\n", "$$\n", "\\int_0^{\\tau} W(t) \\, dW(t) = \\lim_{N\\rightarrow \\infty} \\sum_{k=0}^{N-1} W(\\hat{t}_k) \\Delta W_k\n", "$$\n", "\n", "#### Ito integral\n", "$$\n", "\\int_0^{\\tau} W(t) \\, dW(t) = \\lim_{N\\rightarrow \\infty} \\sum_{k=0}^{N-1} W(t_k) \\Delta W_k = \\frac{1}{2}\\left(W(\\tau) - \\tau\\right)\n", "$$\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> integral\n", "$$\n", "\\int_0^{\\tau} W(t) \\, dW(t) = \\lim_{N\\rightarrow \\infty} \\sum_{k=0}^{N-1}\\frac{1}{2} \\left[W(t_k) + W(t_{k+1}) \\right] \\Delta W_k = \\frac{1}{2} W(\\tau)\n", "$$"]}, {"cell_type": "code", "execution_count": 6, "id": "c27d173d", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["t0 = 0\n", "tau  = 40.5\n", "\n", "Nvalues = np.linspace(2,2000,20,dtype=int)\n", "\n", "# Store numerical result\n", "Ii = np.zeros(len(Nvalues))\n", "Is = np.zeros(len(Nvalues))\n", "\n", "# Store exact result\n", "Ito    = np.zeros(len(Nvalues))\n", "Strato = np.zeros(len(Nvalues))\n", "\n", "for n,N in enumerate(Nvalues):\n", "  # Calculate timestep\n", "  Delta_t = (tau - t0) / N\n", "  \n", "  # Draw increments from a Gaussian distribution\n", "  Delta_W  = np.random.normal(0, 1, N) * np.sqrt(Delta_t)\n", "  \n", "  # Build the Wiener process\n", "  W        = np.cumsum(Delta_W) \n", "  \n", "  # Add W_0 = 0\n", "  W        = np.insert(W, 0, 0., axis=0)\n", "\n", "  for k in range(N):\n", "    Ii[n] = Ii[n] +       (W[k])          * Delta_W[k]\n", "    Is[n] = Is[n] + 0.5 * (W[k] + W[k+1]) * Delta_W[k] # Delta_W[k] = Delta_W[k+1] - Delta_W[k]\n", "\n", "  Ito[n]         = 0.5 * (W[N]**2 - tau)\n", "  Strato[n]      = 0.5 * W[N]**2\n", "\n", "fig, (ax0) = plt.subplots(1, figsize=(5, 4))\n", "ax0.plot(Nvalues, Ii, 'g', label = r'Ito numerics')\n", "ax0.plot(Nvalues, Is, 'r', label = r'<PERSON><PERSON><PERSON>ich numerics')\n", "ax0.plot(<PERSON><PERSON><PERSON>, Ito, 'gs', label = r\"Ito exact\")\n", "ax0.plot(<PERSON><PERSON><PERSON>, <PERSON>rato, 'rs', label = r\"<PERSON><PERSON><PERSON><PERSON> exact\")\n", "ax0.set_ylabel('I(N)')\n", "ax0.set_xlabel('N')\n", "ax0.legend();\n", "#plt.ylim((0,2));"]}, {"cell_type": "markdown", "id": "ea04964e", "metadata": {}, "source": ["Convergence of \n", "$$\\lim_{N\\rightarrow +\\infty} \\sum_{k=0}^{N-1} (W_{k+1} - W_k)^2 = \\tau$$"]}, {"cell_type": "code", "execution_count": 7, "id": "751ddb79", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["t0 = 0\n", "tau  = 4.5\n", "\n", "Nvalues = np.linspace(2,10000,5,dtype=int)\n", "taun   = np.zeros(len(Nvalues))\n", "\n", "fig, (ax0,ax1) = plt.subplots(1, 2, figsize=(10, 4))\n", "\n", "for n,N in enumerate(Nvalues):\n", "  # Calculate timestep\n", "  Delta_t = (tau - t0) / N\n", "  \n", "  # Draw increments from a Gaussian distribution\n", "  Delta_W  = np.random.normal(0, 1, N)\n", "  \n", "  # Build the Wiener process\n", "  W        = np.cumsum(Delta_W) * np.sqrt(Delta_t)\n", "  \n", "  # Add W_0 = 0\n", "  W        = np.insert(W, 0, 0., axis=0)\n", "  \n", "  tvalues  = np.linspace(0,tau, N+1)\n", "  \n", "  ax0.plot(tvalues, W, label=r\"$N=$ %d\" %N)\n", "  \n", "  for k in range(N):\n", "    taun[n] = taun[n] + (W[k+1] - W[k])**2 # Delta_W[k]**2*Delta_t\n", "\n", "ax0.set_ylabel(r'$W_t$')\n", "ax0.set_xlabel(r'$t$')\n", "ax0.set_title(r\"Wiener process for several $N$\")\n", "ax0.set_xlim((0, tau))\n", "ax0.set_ylim((-8, 8))\n", "\n", "x_ticks       = np.append(ax0.get_xticks(), tau)\n", "x_tickslabels = np.append(ax0.get_xticklabels(), r\"$\\tau$\")\n", "\n", "# Set xtick locations to the values of the array `x_ticks`\n", "ax0.set_xticks(x_ticks)\n", "ax0.set_xticklabels(x_tickslabels)\n", "ax0.legend(loc=\"upper left\")\n", "\n", "ax1.plot(Nvalues, tau*np.ones(len(Nvalues)),'k',label=r'$\\tau$')\n", "ax1.plot(Nvalues, taun,'ks-',label='lim')\n", "ax1.set_xlabel(r'$N$')\n", "ax1.set_ylabel(r'$\\tau$')\n", "ax1.set_ylim((0, 2*tau))\n", "ax1.legend()\n", "\n", "fig.tight_layout();"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}