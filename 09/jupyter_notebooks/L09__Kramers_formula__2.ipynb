{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1f845db6", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "# import sympy as sp\n", "# from tqdm import tqdm\n", "\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "2b6a7c43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4009078317304838\n"]}], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "beta  = 1 / kB / T\n", "print(beta)"]}, {"cell_type": "code", "execution_count": 3, "id": "f2dfde40", "metadata": {}, "outputs": [], "source": ["# # Initialize the variables x and y for symbolic calculation\n", "# x   =  sp.symbols('x')\n", "# # Define the potentials Va, Vb and V with sympy\n", "# V   = 10*( x ** 2 - 1 ) ** 2\n", "# # Calculate derivative and second derivative with sympy\n", "# gradVx   =  V.diff(x)\n", "# grad2Vx2 =  gradVx.diff(x)\n", "# # To display sympy functions:\n", "# # display(gradVx)\n", "# # Convert potential and derivatives in numpy functions\n", "# V         =  sp.lambdify((x), V, modules=['numpy'])\n", "# gradVx    =  sp.lambdify((x), gradVx, modules=['numpy'])\n", "# grad2Vx2  =  sp.lambdify((x), grad2Vx2, modules=['numpy'])\n", "\n", "V = lambda x: 10*(x**2 - 1)**2\n", "gradVx = lambda x: 40*x*(x**2 - 1)\n", "grad2Vx2 = lambda x: 120*x**2 - 40"]}, {"cell_type": "code", "execution_count": 4, "id": "51d0d778", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.9959919839679359\n", "0.9959919839679359\n", "-2.220446049250313e-16\n"]}], "source": ["# Grid\n", "xbins     = 500   \n", "xmin      = - 3.5\n", "xmax      = - xmin\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins     = xbins - 1\n", "\n", "iA        = np.argmin(V(xcenters[0:int(xbins/2)]))\n", "iC        = int(xbins/2) + np.argmin(V(xcenters[int(xbins/2):-1]))\n", "iB        = iA   + np.argmax(V(xcenters[iA:iC]))\n", "\n", "print(xcenters[iA])\n", "print(xcenters[iC])\n", "print(xcenters[iB])\n", "\n", "# Approximation at xA and xB\n", "xA        = xcenters[iA]\n", "omegaA    = np.sqrt( np.abs(grad2Vx2(xA)) / mass )\n", "kspringA  = omegaA ** 2 * mass\n", "\n", "xB        = xcenters[iB]\n", "omegaB    = np.sqrt( np.abs(grad2Vx2(xB)) / mass )\n", "kspringB  = omegaB ** 2 * mass\n", "\n", "xC        = xcenters[iC]\n", "omegaC    = np.sqrt( np.abs(grad2Vx2(xC)) / mass )\n", "kspringC  = omegaC ** 2 * mass\n", "\n", "# Energy barriers\n", "Eb_plus   = V(xB) - V(xA)\n", "Eb_minus  = V(xB) - V(xC)"]}, {"cell_type": "markdown", "id": "d08cf2bb", "metadata": {}, "source": ["The next block defines the BBK integrator for Langevin dynamics."]}, {"cell_type": "code", "execution_count": 5, "id": "0f1e04a7", "metadata": {}, "outputs": [], "source": ["def langevin_bbk_step(Q, P, M, gamma, beta, der_V, dt):\n", "  # Deterministic force\n", "  F  =  - der_V(Q)\n", "  \n", "  # Random force \n", "  R  =  np.random.normal(0, 1, size = np.array(Q).shape)\n", "  \n", "  # update p_{n+1/2}\n", "  Phalf = ( 1 - 0.5 * gamma * dt ) * P + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R\n", "  \n", "  # update q_{n+1}\n", "  Q  =  Q + Phalf / M * dt\n", "  \n", "  # Recalculate deterministic force (but not the random force)\n", "  F  =  - der_V(Q)\n", "  \n", "  # update p_{n+1}\n", "  P = ( Phalf + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R ) / ( 1 + 0.5 * gamma * dt ) \n", "\n", "  return Q, P"]}, {"cell_type": "code", "execution_count": 6, "id": "781ac34d", "metadata": {}, "outputs": [], "source": ["# Number of gamma values being test\n", "Ng     = 20 #25\n", "\n", "# Generate gamma values between 0.05 and 10\n", "# Use logspace to generate more points close to 0.05 than 10\n", "gammas =  np.logspace(np.log(0.1), np.log(30), Ng, base=np.exp(1))"]}, {"cell_type": "markdown", "id": "6f6455ef", "metadata": {}, "source": ["### Numerical experiment"]}, {"cell_type": "code", "execution_count": 7, "id": "68140f57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gamma:  0.10000000000000003\n", "Gamma:  0.13501275609964636\n", "Gamma:  0.18228444309622588\n", "Gamma:  0.24610725056510607\n", "Gamma:  0.3322761819490121\n", "Gamma:  0.44861523111203666\n", "Gamma:  0.6056877878071588\n", "Gamma:  0.8177557756774226\n", "Gamma:  1.1040746109061295\n", "Gamma:  1.4906415615808115\n", "Gamma:  2.01255625585706\n", "Gamma:  2.7172076690884666\n", "Gamma:  3.6685769629872973\n", "Gamma:  4.953046867365852\n", "Gamma:  6.687245086537829\n", "Gamma:  9.028633898472897\n", "Gamma:  12.189807464475207\n", "Gamma:  16.4577950210284\n", "Gamma:  22.220122651120796\n", "Gamma:  29.999999999999996\n"]}], "source": ["# Number of simulations\n", "Nreps = 500\n", "\n", "# Integrator timestep\n", "dt  =  0.005 # ps\n", "sdt =  np.sqrt(dt)\n", "\n", "# Array to store simulation time for each replica and each gamma value\n", "simTimes = np.zeros((<PERSON><PERSON><PERSON>,<PERSON>))\n", "\n", "# For loop over gamma values\n", "for g, gamma in enumerate(gammas):\n", "  print(\"Gamma: \", str(gamma))\n", "  # Recalculate diffusion constant\n", "  D     = kB * T / mass / gamma # nm2 ps-1\n", "  sigma = np.sqrt(2 * D)\n", "  for r in range(Nreps):\n", "    # Initial position\n", "    x0  =  xA \n", "    # Final position\n", "    xF  =  xC\n", "    # Initial momentum drawn from the <PERSON><PERSON><PERSON> distribution\n", "    p0  =  np.random.normal(0, 1) * np.sqrt( mass / beta )\n", "    # Initialize position and velocity\n", "    x   =   x0\n", "    v   =   p0 / mass\n", "    t = 0\n", "    # Start the simulation and stop if x > xC\n", "    while x < 0.25:\n", "      # Update position and momentum\n", "      x, p = langevin_bbk_step(x, mass * v, mass, gamma, beta, gradVx, dt)\n", "      # Calculate velocity\n", "      v    = p / mass\n", "      # Update simulation time\n", "      t    = t + 1\n", "    # Store simulation time to go from xA to xB\n", "    simTimes[r,g] = t * dt\n", "\n", "# MFPT from experiment\n", "MFPT = np.mean(simTimes, axis=0)\n", "\n", "# Rate from experiment\n", "expRate = 1 / MFPT    "]}, {"cell_type": "markdown", "id": "3deae671-b8da-4363-9c7b-068a4bff253a", "metadata": {}, "source": ["## Pontryagin formula"]}, {"cell_type": "code", "execution_count": 8, "id": "c925d363-cd7b-4533-8d92-e91bc897feec", "metadata": {}, "outputs": [], "source": ["def pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters ):\n", "  D = 1 / beta / mass / gamma\n", "  \n", "  i0 = np.argmin(np.abs(xcenters - x0)) + 1\n", "  xcenters0 = xcenters[0:i0]\n", "  \n", "  iF = np.argmin(np.abs(xcenters - xF)) + 1\n", "  xcentersF = xcenters[0:iF]\n", "  \n", "  nn = iF - i0\n", "  innerInt = np.zeros(nn+1)\n", "  \n", "  for i in range(nn):\n", "    x = xcenters[0:iA+i+1]\n", "    innerInt[i+1] = np.sum(dx * np.exp( - beta * V(x) ) )\n", "\n", "  x        = xcenters[iA:iA+i+2]\n", "  outerInt = sum( dx * np.exp(beta * V(x)) * innerInt )\n", "\n", "  mfpt = outerInt / D\n", "\n", "  return mfpt\n", "\n", "def kramersRateModerateFriction(gamma):\n", "  return gamma / omegaB * ( np.sqrt(0.25 + omegaB ** 2 / gamma ** 2 ) - 0.5 ) * omegaA / 2 / np.pi * \\\n", "    np.exp( - beta * ( Eb_plus ))\n", "\n", "def kramersRateHighFriction(gamma):\n", "  return  omegaA * omegaB / 2 / np.pi / gamma * \\\n", "    np.exp( - beta * ( Eb_plus ))\n", "\n", "def kramersRateLowFriction(gamma):\n", "  I_minus = 2 * np.pi * Eb_minus / omegaC\n", "  I_plus =  2 * np.pi * Eb_plus / omegaA\n", "  \n", "  p        = I_minus / ( I_plus + I_minus )\n", "  k_weak   = p * gamma * beta * I_plus * omegaA / 2 / np.pi * np.exp( - beta * Eb_plus )\n", "  \n", "  return k_weak\n", "\n", "def TST(gamma):\n", "  return omegaA / 2 / np.pi * np.exp( - beta * Eb_plus )\n", "\n", "Ng1 = 50\n", "gammas1 =  np.logspace(np.log(0.001), np.log(30), Ng1, base=np.exp(1))\n", "pontryagin   = np.zeros(Ng1)\n", "kramers_mod  = np.zeros(Ng1)\n", "kramers_high = np.zeros(Ng1)\n", "kramers_low  = np.zeros(Ng1)\n", "tst          = np.zeros(Ng1)\n", "\n", "for g, gamma in enumerate(gammas1):\n", "  pontryagin[g]   = 1 / pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters )\n", "  kramers_mod[g]  = kramersRateModerateFriction( gamma )\n", "  kramers_high[g] = kramersRateHighFriction( gamma )\n", "  kramers_low[g]  = kramersRateLowFriction( gamma )\n", "  tst[g]          = TST( gamma )"]}, {"cell_type": "code", "execution_count": 16, "id": "22623f67-8259-477e-9cec-3b096357ef13", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 944.882x393.701 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24*in2cm, 10*in2cm), facecolor='white')  \n", "\n", "ax1.plot(xcenters, kB*T*np.ones(xcenters.shape), '--', color='gold')\n", "ax1.text(-1.95, kB*T+0.5, r'$k_B T$', fontsize = 10)\n", "ax1.plot(xcenters, V(xcenters), 'k', label = 'Potential') \n", "ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax1.plot(xA, V(xA), 'go', label = 'Reactant well') \n", "\n", "ax1.plot(xcenters, - 0.5 * kspringB * (xcenters - xB) ** 2 + V(xB), 'k--', linewidth = 0.5)\n", "ax1.plot(xB, V(xB), 'ro', label = 'Barrier') \n", "\n", "#ax1.plot(xcenters,   0.5 * kspringC * (xcenters - xC) ** 2 + V(xC), 'k--', linewidth = 0.5 ) \n", "ax1.plot(xC, V(xC), 'go' ) \n", "\n", "#ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$ / nm')\n", "ax1.set_ylabel(r'$V(x)$ / kJ mol$^{-1}$')\n", "#ax1.legend()\n", "ax1.set_ylim((-1, 14))\n", "ax1.set_xlim((-2, 2))\n", "\n", "ax1.text(-1.06,  0.5, 'A', fontsize = 10)\n", "ax1.text(-0.06,  10.6, 'B', fontsize = 10)\n", "ax1.text(0.9,  0.5, 'C', fontsize = 10)\n", "\n", "ax2.plot(gammas1, tst, 'y--', label = 'TST') \n", "ax2.plot(gammas, expRate, 'ks', label = 'Numerics') \n", "#ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax2.plot(gammas1, pontryagin, 'b--', label = 'Pontryagin') \n", "ax2.plot(gammas1, kramers_mod, 'r--', label = 'Kramer<PERSON>') \n", "ax2.plot(gammas1, kramers_low, 'g--', label = 'Kramer<PERSON>') \n", "#ax2.plot(gammas, kramers_high, 'g--', label = 'Kramers high') \n", "\n", "ax2.set_title('Rates')\n", "ax2.set_xlabel(r'$\\gamma$ / ps $^{-1}$')\n", "ax2.set_ylabel(r'$k$ / ps$^{-1}$')\n", "ax2.legend()\n", "ax2.set_xlim((0, gammas[-1]))\n", "ax2.set_ylim((0, 0.05))\n", "\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.4);\n", "#fig.savefig('figures/potential.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}