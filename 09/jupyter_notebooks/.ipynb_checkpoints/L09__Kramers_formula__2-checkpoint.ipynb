{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1f845db6", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sympy as sp\n", "from tqdm import tqdm\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "2b6a7c43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4009078317304838\n"]}], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "beta  = 1 / kB / T\n", "print(beta)"]}, {"cell_type": "code", "execution_count": 3, "id": "f2dfde40", "metadata": {}, "outputs": [], "source": ["# Initialize the variables x and y for symbolic calculation\n", "x   =  sp.symbols('x')\n", "\n", "# Define the potentials Va, Vb and V with sympy\n", "V   = 10*( x ** 2 - 1 ) ** 2\n", "\n", "# Calculate derivative and second derivative with sympy\n", "gradVx   =  V.diff(x)\n", "grad2Vx2 =  gradVx.diff(x)\n", "\n", "# To display sympy functions:\n", "# display(gradVx)\n", "\n", "# Convert potential and derivatives in numpy functions\n", "V         =  sp.lambdify((x), V, modules=['numpy'])\n", "gradVx    =  sp.lambdify((x), gradVx, modules=['numpy'])\n", "grad2Vx2  =  sp.lambdify((x), grad2Vx2, modules=['numpy'])"]}, {"cell_type": "code", "execution_count": 4, "id": "51d0d778", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.9959919839679359\n", "0.9959919839679359\n", "-2.220446049250313e-16\n"]}], "source": ["# Grid\n", "xbins     = 500   \n", "xmin      = - 3.5\n", "xmax      = - xmin\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins     = xbins - 1\n", "\n", "iA        = np.argmin(V(xcenters[0:int(xbins/2)]))\n", "iC        = int(xbins/2) + np.argmin(V(xcenters[int(xbins/2):-1]))\n", "iB        = iA   + np.argmax(V(xcenters[iA:iC]))\n", "\n", "print(xcenters[iA])\n", "print(xcenters[iC])\n", "print(xcenters[iB])\n", "\n", "# Approximation at xA and xB\n", "xA        = xcenters[iA]\n", "omegaA    = np.sqrt( np.abs(grad2Vx2(xA)) / mass )\n", "kspringA  = omegaA ** 2 * mass\n", "\n", "xB        = xcenters[iB]\n", "omegaB    = np.sqrt( np.abs(grad2Vx2(xB)) / mass )\n", "kspringB  = omegaB ** 2 * mass\n", "\n", "xC        = xcenters[iC]\n", "omegaC    = np.sqrt( np.abs(grad2Vx2(xC)) / mass )\n", "kspringC  = omegaC ** 2 * mass\n", "\n", "# Energy barriers\n", "Eb_plus   = V(xB) - V(xA)\n", "Eb_minus  = V(xB) - V(xC)"]}, {"cell_type": "markdown", "id": "d08cf2bb", "metadata": {}, "source": ["The next block defines the BBK integrator for Langevin dynamics."]}, {"cell_type": "code", "execution_count": 5, "id": "0f1e04a7", "metadata": {}, "outputs": [], "source": ["def langevin_bbk_step(Q, P, M, gamma, beta, der_V, dt):\n", "    \n", "    # Deterministic force\n", "    F  =  - der_V(Q)\n", "    \n", "    # Random force \n", "    R  =  np.random.normal(0, 1, size = np.array(Q).shape)\n", "    \n", "    # update p_{n+1/2}\n", "    Phalf = ( 1 - 0.5 * gamma * dt ) * P + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R\n", "    \n", "    # update q_{n+1}\n", "    Q  =  Q + Phalf / M * dt\n", "    \n", "    # Recalculate deterministic force (but not the random force)\n", "    F  =  - der_V(Q)\n", "    \n", "    # update p_{n+1}\n", "    P = ( Phalf + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R ) / ( 1 + 0.5 * gamma * dt ) \n", "\n", "    return Q, P"]}, {"cell_type": "code", "execution_count": 6, "id": "781ac34d", "metadata": {}, "outputs": [], "source": ["# Number of gamma values being test\n", "Ng     = 20 #25\n", "\n", "# Generate gamma values between 0.05 and 10\n", "# Use logspace to generate more points close to 0.05 than 10\n", "gammas =  np.logspace(np.log(0.1), np.log(30), Ng, base=np.exp(1))"]}, {"cell_type": "markdown", "id": "6f6455ef", "metadata": {}, "source": ["### Numerical experiment"]}, {"cell_type": "code", "execution_count": 7, "id": "68140f57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gamma:  0.10000000000000003\n", "Gamma:  0.13501275609964636\n", "Gamma:  0.18228444309622588\n", "Gamma:  0.24610725056510607\n", "Gamma:  0.3322761819490121\n", "Gamma:  0.44861523111203666\n", "Gamma:  0.6056877878071588\n", "Gamma:  0.8177557756774226\n", "Gamma:  1.1040746109061295\n", "Gamma:  1.4906415615808115\n", "Gamma:  2.01255625585706\n", "Gamma:  2.7172076690884666\n", "Gamma:  3.6685769629872973\n", "Gamma:  4.953046867365852\n", "Gamma:  6.687245086537829\n", "Gamma:  9.028633898472897\n", "Gamma:  12.189807464475207\n", "Gamma:  16.4577950210284\n", "Gamma:  22.220122651120796\n", "Gamma:  29.999999999999996\n"]}], "source": ["# Number of simulations\n", "Nreps = 500\n", "\n", "# Integrator timestep\n", "dt  =  0.005 # ps\n", "sdt =  np.sqrt(dt)\n", "\n", "# Array to store simulation time for each replica and each gamma value\n", "simTimes = np.zeros((<PERSON><PERSON><PERSON>,<PERSON>))\n", "\n", "# For loop over gamma values\n", "for g, gamma in enumerate(gammas):\n", "    \n", "    print(\"Gamma: \", str(gamma))\n", "          \n", "    # Recalculate diffusion constant\n", "    D     = kB * T / mass / gamma # nm2 ps-1\n", "    sigma = np.sqrt(2 * D)\n", "\n", "    for r in range(Nreps):\n", "        \n", "        #print(\"Gamma: \", str(gamma), \"; Replica: \", str(r))\n", "        \n", "        # Initial position\n", "        x0  =  xA \n", "\n", "        # Final position\n", "        xF  =  xC\n", "        \n", "        # Initial momentum drawn from the <PERSON><PERSON><PERSON> distribution\n", "        p0  =  np.random.normal(0, 1) * np.sqrt( mass / beta )\n", "\n", "        # Initialize position and velocity\n", "        x   =   x0\n", "        v   =   p0 / mass\n", "        \n", "        t = 0\n", "        \n", "        # Start the simulation and stop if x > xC\n", "        while x < 0.25:\n", "            \n", "            # Update position and momentum\n", "            x, p = langevin_bbk_step(x, mass * v, mass, gamma, beta, gradVx, dt)\n", "            \n", "            # Calculate velocity\n", "            v    = p / mass\n", "            \n", "            # Update simulation time\n", "            t    = t + 1\n", "        \n", "        # Store simulation time to go from xA to xB\n", "        simTimes[r,g] = t * dt\n", "            \n", "    \n", "# MFPT from experiment\n", "MFPT = np.mean(simTimes, axis=0)\n", "\n", "# Rate from experiment\n", "expRate = 1 / MFPT    "]}, {"cell_type": "markdown", "id": "3deae671-b8da-4363-9c7b-068a4bff253a", "metadata": {}, "source": ["## Pontryagin formula"]}, {"cell_type": "code", "execution_count": 8, "id": "c925d363-cd7b-4533-8d92-e91bc897feec", "metadata": {}, "outputs": [], "source": ["def pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters ):\n", "\n", "    D = 1 / beta / mass / gamma\n", "    \n", "    i0 = np.argmin(np.abs(xcenters - x0)) + 1\n", "    xcenters0 = xcenters[0:i0]\n", "    \n", "    iF = np.argmin(np.abs(xcenters - xF)) + 1\n", "    xcentersF = xcenters[0:iF]\n", "    \n", "    nn = iF - i0\n", "    innerInt = np.zeros(nn+1)\n", "    \n", "    for i in range(nn):\n", "        x = xcenters[0:iA+i+1]\n", "        innerInt[i+1] = np.sum(dx * np.exp( - beta * V(x) ) )\n", "\n", "    x        = xcenters[iA:iA+i+2]\n", "    outerInt = sum( dx * np.exp(beta * V(x)) * innerInt )\n", "\n", "    mfpt = outerInt / D\n", "\n", "    return mfpt\n", "\n", "\n", "def kramersRateModerateFriction(gamma):\n", "     return gamma / omegaB * ( np.sqrt(0.25 + omegaB ** 2 / gamma ** 2 ) - 0.5 ) * omegaA / 2 / np.pi * \\\n", "        np.exp( - beta * ( Eb_plus ))\n", "    \n", "def kramersRateHighFriction(gamma):\n", "     return  omegaA * omegaB / 2 / np.pi / gamma * \\\n", "        np.exp( - beta * ( Eb_plus ))\n", "\n", "def kramersRateLowFriction(gamma):\n", "    \n", "    I_minus = 2 * np.pi * Eb_minus / omegaC\n", "    I_plus =  2 * np.pi * Eb_plus / omegaA\n", "    \n", "    p        = I_minus / ( I_plus + I_minus )\n", "    k_weak   = p * gamma * beta * I_plus * omegaA / 2 / np.pi * np.exp( - beta * Eb_plus )\n", "    \n", "    return k_weak\n", "\n", "def TST(gamma):\n", "    return omegaA / 2 / np.pi * np.exp( - beta * Eb_plus )\n", "\n", "Ng1 = 50\n", "gammas1 =  np.logspace(np.log(0.001), np.log(30), Ng1, base=np.exp(1))\n", "pontryagin   = np.zeros(Ng1)\n", "kramers_mod  = np.zeros(Ng1)\n", "kramers_high = np.zeros(Ng1)\n", "kramers_low  = np.zeros(Ng1)\n", "tst          = np.zeros(Ng1)\n", "\n", "for g, gamma in enumerate(gammas1):\n", "    pontryagin[g]   = 1 / pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters )\n", "    kramers_mod[g]  = kramersRateModerateFriction( gamma )\n", "    kramers_high[g] = kramersRateHighFriction( gamma )\n", "    kramers_low[g]  = kramersRateLowFriction( gamma )\n", "    tst[g]          = TST( gamma )"]}, {"cell_type": "code", "execution_count": 9, "id": "22623f67-8259-477e-9cec-3b096357ef13", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 944.882x314.961 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24*in2cm, 8*in2cm), facecolor='white')  \n", "\n", "ax1.plot(xcenters, kB*T*np.ones(xcenters.shape), '--', color='gold')\n", "ax1.text(-1.95, kB*T+0.5, r'$k_B T$', fontsize = 10)\n", "ax1.plot(xcenters, V(xcenters), 'k', label = 'Potential') \n", "ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax1.plot(xA, V(xA), 'go', label = 'Reactant well') \n", "\n", "ax1.plot(xcenters, - 0.5 * kspringB * (xcenters - xB) ** 2 + V(xB), 'k--', linewidth = 0.5)\n", "ax1.plot(xB, V(xB), 'ro', label = 'Barrier') \n", "\n", "#ax1.plot(xcenters,   0.5 * kspringC * (xcenters - xC) ** 2 + V(xC), 'k--', linewidth = 0.5 ) \n", "ax1.plot(xC, V(xC), 'go' ) \n", "\n", "#ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$ / nm')\n", "ax1.set_ylabel(r'$V(x)$ / kJ mol$^{-1}$')\n", "#ax1.legend()\n", "ax1.set_ylim((-1, 14))\n", "ax1.set_xlim((-2, 2))\n", "\n", "ax1.text(-1.06,  0.5, 'A', fontsize = 10)\n", "ax1.text(-0.06,  10.6, 'B', fontsize = 10)\n", "ax1.text(0.9,  0.5, 'C', fontsize = 10)\n", "\n", "ax2.plot(gammas1, tst, 'y--', label = 'TST') \n", "ax2.plot(gammas, expRate, 'ks', label = 'Numerics') \n", "#ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax2.plot(gammas1, pontryagin, 'b--', label = 'Pontryagin') \n", "ax2.plot(gammas1, kramers_mod, 'r--', label = 'Kramer<PERSON>') \n", "ax2.plot(gammas1, kramers_low, 'g--', label = 'Kramer<PERSON>') \n", "#ax2.plot(gammas, kramers_high, 'g--', label = 'Kramers high') \n", "\n", "ax2.set_title('Rates')\n", "ax2.set_xlabel(r'$\\gamma$ / ps $^{-1}$')\n", "ax2.set_ylabel(r'$k$ / ps$^{-1}$')\n", "ax2.legend()\n", "ax2.set_xlim((0, gammas[-1]))\n", "ax2.set_ylim((0, 0.05))\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.4);\n", "\n", "#fig.savefig('figures/potential.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}