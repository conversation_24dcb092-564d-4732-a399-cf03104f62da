{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Estimate rates from SqRA rate matrix 1D"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T06:11:52.049504Z", "start_time": "2020-11-13T06:11:51.357462Z"}}, "outputs": [], "source": ["import sys\n", "\n", "\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "import matplotlib.cm as cm\n", "\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adjacency matrix"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def adjancency_matrix_sparse(nbins, nd, periodic=False):\n", "    v = np.zeros(nbins)\n", "    v[1] = 1\n", "    \n", "    if periodic:\n", "        v[-1] = 1\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.circulant(v)) #.toarray()\n", "    else:\n", "        A0 = scipy.sparse.csc_matrix(scipy.linalg.toeplitz(v)) #.toarray()\n", "    \n", "    A = A0\n", "    I2 = scipy.sparse.eye(nbins)  #np.eye(nbins)\n", "    for _ in range(1, nd):\n", "        I1 = scipy.sparse.eye(*A.shape) #np.eye(*A.shape)\n", "        A =  scipy.sparse.kron(A0, I1) + scipy.sparse.kron(I2, A)\n", "    return A"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1D system"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 472.441x314.961 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Potential energy function\n", "def V(x):\n", "    return 14*(x**3 - 1.5*x)**2 - x**3 + x\n", "\n", "# System parameters\n", "kB    = 0.008314463\n", "T     = 300\n", "mass  = 1\n", "gamma = 1\n", "D     = kB * T / mass / gamma\n", "sigma = np.sqrt(2 * D)\n", "beta  = 1 / kB / T\n", "\n", "# Grid\n", "nd     = 1  # Number of dimensions\n", "nedges = 121 # State boundaries\n", "xmin   = -2\n", "xmax   =  2\n", "\n", "x      = np.linspace(xmin, xmax, nedges)  # array with x edges\n", "dx     = x[1] - x[0]\n", "x      = x[:-1] + (dx / 2)                # array with x centers\n", "xbins  = nedges - 1\n", "Nbins  = xbins**nd                        # number of bins\n", "\n", "fig, (ax1) = plt.subplots(1, 1, figsize=(12*in2cm, 8*in2cm), facecolor='white')\n", "###############################################\n", "ax1.plot(x, kB*T*np.ones(x.shape), '--', color='gold')\n", "ax1.text(-1.95, kB*T+0.5, r'$k_B T$', fontsize = 10)\n", "ax1.plot(x, V(x), 'k', label = 'Potential') \n", "\n", "#ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$ / nm')\n", "ax1.set_ylabel(r'$V(x)$ / kJ mol$^{-1}$')\n", "#ax1.legend()\n", "ax1.set_ylim((-1, 18))\n", "ax1.set_xlim((-2, 2))\n", "\n", "\n", "ax1.set_title('Potential energy function')\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "#fig.savefig('potential.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Build SqRA 1D"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-11-13T09:36:18.859829Z", "start_time": "2020-11-13T09:36:18.694310Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7352\\3608420724.py:33: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  Kevals[:,i] = np.exp(Qevals * tau[i])\n"]}], "source": ["A  = adjancency_matrix_sparse(Nbins, nd, periodic=False)\n", "\n", "# Potential energy of states\n", "v = V(x)\n", "\n", "# Flux\n", "flux = D / dx**2\n", "Af   = flux * A\n", "\n", "# Diagonalization\n", "SQRA = np.sqrt(np.exp(- beta * v))\n", "SQRA = SQRA / sum(SQRA)\n", "Di   = scipy.sparse.spdiags(SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) #.toarray()\n", "D1   = scipy.sparse.spdiags(1/SQRA, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)       \n", "Q    = D1 * Af * Di\n", "\n", "Q            = Q + scipy.sparse.spdiags(-Q.sum(axis=1).T, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\n", "Qeval<PERSON>, Qevecs = scipy.sparse.linalg.eigs(Q.T, 6, which='LR')\n", "idx    = np.argsort( - np.real(Qevals))\n", "Qevals = Qevals[idx]\n", "Qevecs = Qevecs[:,idx]\n", "\n", "\n", "<PERSON><PERSON><PERSON>, Kevecs = scipy.sparse.linalg.eigs(Q, 6, which='LR')\n", "idx    = np.argsort( - np.real(Kevals))\n", "Kevals = Kevals[idx]\n", "Kevecs = Kevecs[:,idx]\n", "\n", "tau    = np.linspace(0,1,100)\n", "\n", "Kevals = np.zeros((6,100))\n", "for i in range(100):\n", "    Kevals[:,i] = np.exp(Qevals * tau[i])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7352\\2286108464.py:27: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7352\\2286108464.py:28: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7352\\2286108464.py:29: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7352\\2286108464.py:30: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7352\\2286108464.py:31: RuntimeWarning: invalid value encountered in divide\n", "  ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA20AAAFsCAYAAABfMVFGAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAACSX0lEQVR4nOzdd3yTVRfA8V+SLkrpgEIpGwWBQgtYQAHLko0IomwBFVBAZCm+LFGRoYJYRAFlI6ggoCJUadlTkVpW2UPLKLultNCVPu8fD4kNbSEtaZI25/v5BJInN09OCrnNyb33XI2iKApCCCGEEEIIIeyS1tYBCCGEEEIIIYTImSRtQgghhBBCCGHHJGkTQgghhBBCCDsmSZsQQgghhBBC2DFJ2oQQQgghhBDCjknSJoQQQgghhBB2TJI2IYQQQgghhLBjkrQJIYQQQgghhB2TpE0IIYQQQggh7JgkbUIIIYQQQghhxyRpE0IIIYQQQgg7JklbIbVkyRI0Gk2Ol23btpm0++eff2wab34p7K9PCHsnfZGqsL8+IQqKB/VH9/dNjvS+tdVrVRSFSZMmsX379oe2nTRpEgEBAWRkZOTqORYuXEjZsmVJSkrKa5h2wcnWAYj8tXjxYqpXr57leEBAAAAdOnRg7969+Pv7Wzs0cZ+LFy/y+eefc+LECeLj44mJiaFdu3Z88skneHl52To8IR6J9EUFw6ZNmzh27Bh79+5lyZIluLi4cO7cOaZMmcK5c+eIiYlh6NChDB8+3NahCpEne/fuNbn90UcfsXXrVrZs2WJyPCAggJo1a0q/lM9OnjzJ+++/z/fff//AdpcuXeLTTz9lyZIlaLW5G3Pq168fn3zyCZ9++ikffvjho4RrU5K0FXK1atWiXr16Od5fsmRJSpYsacWIRHZ++OEHZs+ezezZs3nyyScBtSNr1qwZhw8fZufOnbnupISwJ9IX2T+9Xk9CQgLDhg2jefPmuLi48N1337FmzRqmTZvGE088wfz58xk2bBjDhg1Do9HYOmQhcu3pp582uV2yZEm0Wm2W45nvF/knMjISgODg4Ae2mzVrFt7e3nTp0iXXz+Hk5MQbb7zBRx99xP/+9z/c3d3zFKutyadAB5fTcPgvv/xCUFAQrq6uPPbYY8yaNYsPPvggyy/pU6dO0atXL0qVKoWrqys1atTgq6++MmljeFx0dDQ9e/bEy8sLPz8/XnvtNW7dumVs9/PPP6PRaNi8eXOWOOfOnYtGo+HQoUMAnD59mldffZWqVavi7u5O2bJl6dixI4cPH37oa37llVeoVKlSluN5fX0A165d4/XXX6d8+fK4urpSsmRJGjduzKZNmx4az5IlS/jwww/ZsGGDMWEDeOKJJxg0aBB79uzh999/f+h5hCjIpC/KGmduXx88Wl+k0+moXLkyAG3btmXixIkcP36c1atX88QTT3D16lWGDBmCv7+/JGzCIdzfLxnem4cOHaJr1654eXlRvHhxRo0aRXp6OidOnKBt27YUK1aMSpUq8emnn2Z7XnPez+a8l48fP07Pnj3x8/PD1dWVChUq0LdvX1JSUoxtHqWPyu9+Jzg4mN69ewPqZx6NRkOxYsVQFMWkXWpqKgsXLqRXr14mX2B/+eWXD5zi6u7uTmpqKgC9e/cmISGBH3744aGv217JSFshp9frSU9PNzmm0WjQ6XQ5Pub333+nS5cuNGnShJUrV5Kens6MGTO4cuWKSbujR4/SqFEjKlSowGeffUbp0qXZuHEjw4YN4/r167z//vsm7V988UW6d+9O//79OXz4MGPHjgVg0aJFADz33HOUKlWKxYsX8+yzz5o8dsmSJTz55JMEBQUB6jB5iRIl+PjjjylZsiQ3b95k6dKlPPXUU0RFRVGtWrW8/cDy+Pr69OnD33//zZQpU3jiiSeIj4/n77//5saNGw98jmPHjjFkyBB+//13vL29s9xvGJn4448/aN++/SO/JiFsRfqivLNGX2Tw66+/ArBlyxbatGnDyJEjjfeVKlWK0NBQQkJCHvk1CVGQdevWjZdffpk33niDiIgIPv30U9LS0ti0aRNDhgzhnXfe4bvvvuN///sfVapUMRkdMvf9/LD38sGDB3nmmWfw9fVl0qRJVK1aldjYWNatW0dqaiqurq5A3vsoa/Q733zzDYMGDSIjI8OYDBYpUiTLl0J//vknN27coHnz5ibHO3bsaPycFBUVxZAhQ5g5cyYNGzYEwN3dHRcXFwBKly5N9erV2bBhA6+99toD47JbiiiUFi9erADZXnQ6XZZ2586dMx6rX7++Ur58eSUlJcV47Pbt20qJEiWUzP9l2rRpo5QrV065deuWyXMPHTpUcXNzU27evKkoiqK8//77CqB8+umnJu2GDBmiuLm5KRkZGcZjo0aNUooUKaLEx8cbjx09elQBlNmzZ+f4etPT05XU1FSlatWqysiRIx/4+vr166dUrFgxyzkMceb29SmKonh4eCgjRozIMb6c9O7dWwkKCsrx/l27dimA8r///S/X5xbCHkhflPPrs6e+yKBBgwYKoHTv3j3P5xCiIOnXr59StGjRbO+7/31reG9+9tlnJu3q1KmjAMratWuNx9LS0pSSJUsqXbp0MWlr7vv5Ye/lFi1aKN7e3srVq1fNfq2KYn4fZa1+p1SpUsqwYcNMjiUnJyt+fn7G/veTTz5RAOXy5cs5nuerr75SACUmJibHNr1791b8/PzyFKc9kOmRhdyyZcv466+/TC5//vlnju2TkpLYv38/nTt3Nn47AeDh4UHHjh2Nt5OTk9m8eTMvvPAC7u7upKenGy/t27cnOTmZP/74w+Tczz//vMntoKAgkpOTuXr1qvHYa6+9xt27d1m5cqXx2OLFi3F1daVXr17GY+np6UydOpWAgABcXFxwcnLCxcWFU6dOcezYsdz/oO6T29fXoEEDlixZwuTJk/njjz9IS0t76HPcvXuXVatW8cwzzzywDWCcsiREQSV9Ud5Yoy8yuHr1Kn/99ReAcfqnECKr5557zuR2jRo10Gg0tGvXznjMycmJKlWq8O+//xqP5eb9/KD38p07d9i+fTvdunV76Jq7vPRR1up3zp8/z9WrV7OsZ3N1deXy5cvGImyXLl1Co9Hg6+ub47kOHDhA8eLFKV++fI5tSpUqxdWrV7PM+igoJGkr5GrUqEG9evVMLg9a7BkXF4eiKPj5+WW5L/OxGzdukJ6ezuzZs3F2dja5GKbxXb9+3eTxJUqUMLltGLo3JCYANWvWpH79+ixevBhQp1QtX76cTp06Ubx4cWO7UaNG8d5779G5c2d+/fVX/vzzT/766y9q165tcr68yu3rW7lyJf369WPBggU0bNiQ4sWL07dvXy5fvpzjc5w5c4a0tDST13W/Q4cOodFoaNOmzSO/JiFsSfqivLFGX2SwYcMGFEWhWLFiHDt2jKioqAe2r1y5Mh4eHjg7O+Pq6oqHhwelS5fOdTluIQqa+39vu7i44O7ujpubW5bjycnJxtu5eT8/6L0cFxeHXq+nXLlyD401L32UtfqdnIqQzJo1iwEDBhhv3717F2dn5wdOpz9w4AB16tR54PO5ubmhKIrJv0lBImvahAkfHx80Gk2WNSOAyZvPx8cHnU5Hnz59ePPNN7M9V15Hh1599VWGDBnCsWPHOHv2LLGxsbz66qsmbZYvX07fvn2ZOnWqyfHr169nuzYsMzc3N5NFupkfa5Db1+fr60toaCihoaHExMSwbt06xowZw9WrV3MsImL4YHPw4MEcY12xYgVdu3bNtliBEIWZ9EUqa/RFBhs2bABg9uzZvPLKK4wcOZKtW7fmWHTk3LlzgFq0pE+fPsaCAkKI7OXm/fyg9/JPP/2ETqfjwoULD33OvPRR1up3IiMjcXd3z7IdzKFDh4zrhg3nT01NJSkpiaJFi2Y5j6IoHDlyhMGDB+f4XAA3b940fsFUEEnSJkwULVqUevXq8fPPPzNjxgzjtKTExETWr19vbOfu7k7z5s2JiooiKCjIZPrSo+rZsyejRo1iyZIlnD17lrJly9K6dWuTNhqNxvjtuMGGDRu4ePEiVapUeeD5K1WqxNWrV7ly5YrxG/vU1FQ2btxobPMor69ChQoMHTqUzZs3s3v37hzbBQYGUrduXX799Vc+/vhjXn/9ddLS0tiwYQNdunThxx9/5NatW8yZM8fs5xaisJC+SGWNvgjUb7J///13goKC6NevH8uXL2fTpk3MmjWLESNGmLRNTk42GVGIjo6mVq1aZsclhKPK6/v5/vdykSJFaNq0KT/++CNTpkx54LTBvPRR1up3Dh06RPXq1bOMoB06dIiXX37ZeNuQ1J05c8YkmTO4efMmd+/efegXdGfPnjXuDVoQSdJWyB05ciTbubuPP/54jvOgJ02aRIcOHWjTpg3Dhw9Hr9czffp0PDw8uHnzprHdrFmzeOaZZwgJCWHw4MFUqlSJ27dvc/r0aX799dcsG1Way9vbmxdeeIElS5YQHx/PO++8k2WPsueee44lS5ZQvXp1goKCiIyMZPr06WZNFejevTsTJ06kR48ejB49muTkZL744gv0er1JO3Nf361bt2jevDm9evWievXqFCtWjL/++stY+S4nGo2G3377jQ8//JDVq1ezfv166tevz+DBg9m4cSPz5s1jw4YNWaZyCVEQSV+Ulb30RQCrVq0iKSnJuF5vxYoV1K9fn1GjRnHhwgVGjhyJh4cHy5Yto3Tp0nTt2tX4nJcvX85243QhRFbmvJ/NeS/PnDmTZ555hqeeeooxY8ZQpUoVrly5wrp16/j6668pVqwYkPc+yhr9jre3N9u3b+eXX37Bz88Pf39/ypcvz9GjR02Ss2bNmgFqJe3skjZXV1ecnZ2JiIggKCiI2rVrG9fDGWRkZLBv3z769+//4H8ge2bTMigi3zyoYhugzJ8/36Rd5opmiqIoP/30kxIYGKi4uLgoFSpUUD7++GNl2LBhio+Pj0m7c+fOKa+99ppStmxZxdnZWSlZsqTSqFEjZfLkycY2hmpL165dyzbG+59bURQlPDzcGOvJkyez3B8XF6f0799fKVWqlOLu7q4888wzys6dO5WmTZsqTZs2fehzhIWFKXXq1FGKFCmiPPbYY8qXX36ZpWKbua8vOTlZGTRokBIUFKR4enoqRYoUUapVq6a8//77SlJSUpbYs/Pvv/8qv/zyizJt2jSlU6dOyoQJE5Tk5GSzHiuEPZO+6MHPYQ99UWRkpNKiRQtl0KBByt27d43HL168qLz44ouKk5OTAihBQUHKzz//bPLYffv2KU888USO5xbC3uWleuT9fUhO52jatKlSs2bNLMcf9n4297189OhRpWvXrkqJEiWMfeQrr7xi8vnhUfqo/P4MdObMGaVp06ZK0aJFjVU5T5w4ofj7+2dpGxISorRv3z7Hc82cOVMpU6aMAignTpzIcv/mzZsVQImMjHxgTPZMoyj37WAnRDbS0tKoU6cOZcuWJTw83NbhFBoLFixg2rRpnD17FoBWrVrxxRdfmHxrnZqaysqVK+nTp4+twhTCbkhfZH2pqano9XqKFCmS5b5t27YxdOhQDh06lGUUUgghcmv16tUsWLAgy1q4NWvW0L17d/7991/Kli2b6/P26dOHs2fPPnTKpj2TpE1kq3///rRq1Qp/f38uX77MvHnz2L59O+Hh4bRs2dLW4RUqiYmJ7N69mx9//JGffvqJmzdv0qBBA5o1a4aPjw8HDhzg888/x9/f39ahCmF10hfZt9u3b/Pss89y7Ngxbty4YdE1hUIIx/P+++9z9+5dPv30U5PjiqLQqFEjgoOD+fLLL3N1zjNnzlCjRg22bNnywG2W7J0kbSJb3bp1Y8+ePVy7dg1nZ2eefPJJxo0bR9u2bW0dWqGm1+v5888/OXz4MGlpaVSrVo2mTZvKByHhsKQvEkIIAeraaENlytyM7G/dupVTp07x+uuv52N0+U+SNiGEEEIIIYSwYzIBXQghhBBCCCHsmCRtQgghhBBCCGHHJGkTQgghhBBCCDsmm2vnQUZGBpcuXaJYsWJoNBpbhyOEzSmKwu3btylTpoyU/bYh6ZuEMCV9k32QvkkIU3npmyRpy4NLly5Rvnx5W4chhN05f/485cqVs3UYDkv6JiGyJ32TbUnfJET2ctM3SdKWB8WKFQPUH7Snp6eNoxHC9hISEihfvrzxvSFsQ/omIUxJ32QfpG8SwlRe+iZJ2vLAMLTv6ekpnY8Qmci0F9uSvkmI7EnfZFvSNwmRvdz0TTLBWwghhBBCCCHsmCRtQgghhBBCCGHHZHqkEEIIYYcyMjJITU21dRh2z9nZGZ1OZ+swhHBYer2etLQ0W4dhl1xcXCxWuVaSNiGEEMLOpKamcu7cOTIyMmwdSoHg7e1N6dKlZe2aEFakKAqXL18mPj7e1qHYLa1WS+XKlXFxcXnkc0nSJoQQQtgRRVGIjY1Fp9NRvnx52V/sARRF4c6dO1y9ehUAf39/G0ckhOMwJGylSpXC3d1dvjS5j2F/wtjYWCpUqPDIPx+HTdrmzJnD9OnTiY2NpWbNmoSGhhISEmKRc+v1enbu3ElsbCz+/v6EhITI1A0hHMyOHTuYPn06kZGRxMbG8tNPP9G5c+cHPmb79u2MGjWK6OhoypQpw7vvvsugQYOsE7CwG+np6dy5c4cyZcrg7u5u63DsXpEiRQC4evUqpUqVkt+3QliBXq83JmwlSpSwdTh2q2TJkly6dIn09HScnZ0f6VwO+fXdypUrGTFiBOPHjycqKoqQkBDatWtHTEzMI5977dq1VKpUiebNm9OrVy+aN29OpUqVWLt2rQUiF0IUFElJSdSuXZsvv/zSrPbnzp2jffv2hISEEBUVxbhx4xg2bBhr1qzJ50iFvdHr9QAWmU7jKAzJrayrEcI6DO81+WLpwQz9uKFffxQOOdI2c+ZM+vfvz4ABAwAIDQ1l48aNzJ07l2nTpuX5vGvXruWll15CURST4xcvXuSll15i9erVdOnS5ZFizwu9HnbuhNhY8PeHkBCQLyKFyF/t2rWjXbt2ZrefN28eFSpUIDQ0FIAaNWqwf/9+ZsyYwYsvvphPUQp7JlONzCc/KyFsQ957D2bJn4/DJW2pqalERkYyZswYk+OtW7dmz549eT6vXq9n+PDhWRI2UOfcazQaRowYQadOnaw6dWPtWhg+HC5c+O9YuXIwaxbYIH8UQuRg7969tG7d2uRYmzZtWLhwIWlpaY88rQJASU9n0Npx3Iq7xf/av8ETxas98jmF5aWkpJCRkYFer7fIt7OFnaz5K/gUBe7csXUUIjdSUiAjQx0YkG4qe5bumhwuabt+/Tp6vR4/Pz+T435+fly+fDnbx6SkpJCSkmK8nZCQkKXNzp07uZA5M7qPoiicP3+enTt30qxZs7wFn0tr18JLL6mdYWYXL6rHV6+WxE0Ie3H58uVs+6X09HSuX7+ebYEFc/qmzM6eSmR1yfrcLFWSc8P/x76fNlkmeGFRFStWZN68edy9e9fWoRQIdevWtXUI4hHduQMeHraOQuRGxYowbx5IN5UzS3dNDvv11P3DlYbRsOxMmzYNLy8v46V8+fJZ2sTGxpr1vOa2e1R6vTrCls3An/HYiBHy7YgQ9iS7fim74wbm9E2Zla7gjObeOR9zqgQUf+SYhXj99ddp2bIle/fuNfsx4eHhjB071uz2b775Jn/++WdewhNCCLOFh//A2LHdzW7/5put+PNP63wB6nAjbb6+vuh0uiyjalevXs3yLbfB2LFjGTVqlPF2QkJClg9H5pYZtlY54p07TadE3k9R4Px5tZ2VBv5UssBOiGyVLl06237Jyckpx8pc5vRNmbm7u6NF3ferovNFXnzxCkuXpuTYXthGSkoKsbGxVKpUCTc3N1uH81D79u2jX79+7Nq1iyFDhjy0fUZGBt26dWPt2rUEBgaa9RzTpk3j7bffzraaqkyPLPjc3SEx0dZRiNxISVE/ylWqBAWgm6J+/SeZMGEinTp1zrGN2jeNY+3aXzCza2LatAm8/fYIBg2KynKfTI98RC4uLgQHBxMREcELL7xgPB4REUGnTp2yfYyrqyuurq4PPG9ISAjlypXj4sWL2a5r02g0lCtXzmLbCjyMuQN6Vhr4U8kCOyFy1LBhQ3799VeTY+Hh4dSrVy/H9Wzm9E2ZaTQaY9Lm73SB0O/SWd+1KN3N/1JRWIFOp0Or1aLT6QpM+fpWrVoxY8YMs+L97bffKF68OHXq1DH7/M2bNyc+Pp4//viDxo0bP0Kkwh5pNFC0qK2jELmh06lJiU5n/9+9p6WlER0dTZ06QQ+M9bffwu71TWZmbEDz5k3u9U27871vcsivp0aNGsWCBQtYtGgRx44dY+TIkcTExDzSfkg6nY5Zs2YBWacyGW6HhoZa7RewuQN6VtuH1LDA7v7hP8MCO9kSQRQyiYmJHDhwgAMHDgBqSf8DBw4YtxYZO3Ysffv2NbYfNGgQ//77L6NGjeLYsWMsWrSIhQsX8s4771g0Ls29pA0nhYbsZcgQK395IwqlgwcPcvLkSbNK7q9bt44WLVqYHGvQoAEeHh64ubmh0+nw8PDAw8ODzz//HFB/j7Zo0YJ169blS/xCiMLp4sWLeHp6kpaWRlBQEB4eHhw8eDDbtvbeNzncSBtA9+7duXHjBpMmTSI2NpZatWoRFhZGxYoVH+m8Xbp0YfXq1QwfPtykKEm5cuUIDQ21arn/kBB1EOvixezXtWk06v1WGfh72AI7jUZdYNepk/1/XSOEmfbv30/z5s2Ntw3TGPv168eSJUuIjY012RuycuXKhIWFMXLkSL766ivKlCnDF198YfFy/1rU92G6s5Y+fhH0v9KcAQNg/Xr1rSjsj6Io3LFiaT13d/dclak+cOAAixcvJj09nZMnT1KzZs2Htr//S9J9+/YBMHXqVCIjI7PdnzAgIIDw8HCz4xJCWI+1K4C6u5v3O6ts2bIsWLCAuXPnsmvXrge2tfe+ySGTNoAhQ4aYNfc+t7p06UKnTp3YuXMnsbGx+Pv7ExISYvUpLjqdOuvwpZfU/9SZ8yXDf/LQUCvlSHa7wE6I/NOsWbNsp0obLFmyJMuxpk2b8vfff+djVP+NtKU5aelRchND4qcSFgbz58Prr+frU4s8unPnDh5WLK2XmJhIUTPnqimKwuDBg5k4cSJffvklR44cMSZt8+fPZ/Hixbi4uLBo0SIee+wxAOLi4vD09Mz2fIcOHaJ27drZ3ufp6UlcXFweXpEQIr9ZuwJoYqL5U2qjoqKMVWbT0tJo1qwZ0dHRLFiwgJdeesnYzt77JoecHpnfdDodzZo1o2fPnjRr1sxmaxK6dFHL+pcta3q8XDkrl/u3ywV2QjgmbaakzT16P5+NvwnAqFFw+rQtIxMF0fz580lOTmb48OEEBgZy5MgRAG7evMmCBQvYsWMH06dPN9kb1cfHJ8ftKQ4ePJjjB6OEhAR8fHws/yKEEIVaVFSUcQ2tk5MTq1evZsSIEVna2Xvf5LAjbY6iSxd11qFNCzba3QI7IRyXcXpkcW9QFAY/sZnVzbqybRv07Qs7doCT/GawK+7u7iRasbSeu7u7We2uXbvG+PHjCQsLQ6fTERgYSHR0NAB//vknzZo1w8nJifr163Py5Enj4+rUqcPx48eznC89PZ1Tp07lWFHy6NGjuSpeIoSwHmtXADWzmwLUhGv69OmAugYtp0ru9t43ya9mB6DT2XjWoV0tsBPCsRlG2tL91S1OtJvCWbKkK0FBsHcvfPwxTJhgywjF/TQajdnTFa1p9OjR9OjRg/r16wMQGBjIqlWrAHWaUeZvnjMyMozXO3bsyKRJk7KcLzExEb1en+PslK1bt7J06VJLvgQhhIXYawXQtLQ0s6cu2nvfJNMjRf4zLLCDrKtGrb7ATgjHprk30qYvVVI9EB5OxQoKX32l3vzwQ/jrLxsFJwqMHTt2sGnTJqZMmWI8FhgYyJkzZ0hOTsbHx4f4+HjjfZn3Umvfvj3Xr183TqU08Pb2pk+fPgQEBLBixQqT+3bu3EmxYsWstm2OEKJwcHZ2ZuTIkbRo0QIPDw8uXbqUY1t775skaRPWYTcL7IRwbIZCJOnFvcHFBWJi4MQJeveGbt0gPR1efhmSkmwbp7BvTZo04cKFCyaL9gMCAtDr9bi5ufHUU0+xfft29Ho9f//9N1WrVjW20+l0TJ06lY8++ijLeZctW0ZSUhK9e/c2OT558mRmzJiRfy9ICFFozZgxg/j4eBITEylTpkyO7ey9b5LpkcJ67GKBnRCOzbimTaNR33+bN0N4OJrq1Zk7F3bvhpMn1cIkX39t42BFgVW8eHH69etHSEgIzs7OLFy40OT+nj170rNnT7PPt3HjRkuHKIRwUN26dWP//v14eHiwb98+Pv30U+N99tw3SdImrMvmC+yEcGyGpE2v6KFNGzVp27gRhg2jeHFYtgxatoRvvoH27dXvWYTIi0GDBmXZ80gIIWzNsPa2oJHpkUII4UCMI21KBrRurR7ctg2SkwFo0QLefls93L8/PGD6vxBCCCGsRJI2IYRwIMZCJEoGBAWp05Tv3IFdu4xtpkyBunXhxg3o1w8yFf4TQgghhA1I0iaEEA7EUPJfr2So1VvbtlXv+P13YxsXF/juOyhSBDZtgs8+s0WkQgghhDCQpE0IIRyIodPX3xtxyy5pA6he/b+dOsaNg/37rROfEEIIIbKSpE0IIRyIYaTNuNlxq1ag1UJ0NJw/b9J2wAB48UV1G4CePeH2bWtHK4QQQgiQpE0IIRyK9t5+9saRNh8fePpp9fp9o20ajVpFsnx5OH0a3nzTioEKIYQQwkiSNiGEcCDG6ZGK8t9BwxTJ337L0r54cVixQh2M+/Zb9SKEEEII65KkTQghHIih5H9G5qStfXv174gISE3N8piQEHj/ffX64MHq5ttCCCGEsB5J2oQQwoFoNdkkbXXrgp8fJCaalP7PbPx4aNYMkpKge3fjtm5CCCGEsAJJ2oQQwoFkqR4J6tzHdu3U6xs2ZPs4nU6dJunrCwcOwOjR+RqmKECaNm2Kr68vGzduNPsxP/zwA927dze7fatWrdi0aVNewhNCCLPZc98kSZsQQjgQLWolkiz7ZRumSIaF5fjYMmVg2TL1+pdfwpo1lo9PFDzbtm2jbdu2LF682Kz2GRkZjBs3jgkTJpj9HBMmTGC0fFMghMijJ598kp9//vmBbey9b5KkTQghHIjOMD3y/jtat1aH044fh7Nnc3x8u3bw7rvq9f79H9hUOAiNRkPLli05cuSIWe3DwsIoXrw4gYGBZj9HkyZNiI+PZ/fu3XkNUwjhoNLS0oiOjiYoKOiB7ey9b5KkTQghHIih089Q7rvDywueeUa9nsMUSYPJk6FRI7h1C7p1g5QUi4cpCpiDBw9y8uRJ0tLSHtp23bp1tGjRwuRYgwYN8PDwwM3NDZ1Oh4eHBx4eHnz++eeAmhi2aNGCdevW5Uv8QojC6eLFi3h6epKWlkZQUBAeHh4cPHgw27b23jc55fszCCGEsBs6DaBkM9IG0KEDbN+uJm1vvZXjOZyd4YcfoE4diIyEd96B2bPzKWCBosCdO9Z7Pnd3dY8+cx04cIDFixeTnp7OyZMnqVmz5kPbDxo0yOTYvn37AJg6dSqRkZGsyWbubUBAAOHh4eYHJoSwHjvtqMqWLcuCBQuYO3cuu3IotGVg732TjLQJIYQDMa5pu3+kDeC559S/t25VK0k+QPnypuvbVq2yYJDCxJ074OFhvUtuPncpisLgwYOZOHEilSpVMk6RTEtLo3Hjxnh7e7N69WqTx8TFxeHp6Znt+Q4dOkTt2rWzvc/T05O4uDjzgxNCWI8dd1RRUVHUrVsXgL1799KwYUOaNm1Khw4diI+PN7az975JkjYhhHAgunvfTCrZfUFZvTo8/ri6V1tExEPP1aED/O9/6vUBA2T/Nkc0f/58kpOTGT58OIGBgcakzcnJidWrVzNixIgsj/Hx8SEhISHb8x08eDDHD0YJCQn4+PhYLHYhhGOIioqiTp06AFSsWJHNmzezfft2OnbsyFdffWVsZ+99k0yPFEIIB6K7l6xlZJe1aTTqaNusWbB+PbzwwkPPN3ky7N0LO3bASy/BH3+os1aE5bi7P3Tg0+LPZ45r164xfvx4wsLC0Ol0BAYGEh0dDajrPPz9/bN9XJ06dTh+/HiW4+np6Zw6dSrHIgBHjx41fvASQtgZe+2oUBOu6dOnA1CmTBnjcScnJ5yc/kuF7L1vkqRNCCEciO7eBIvsZkcC/yVtGzZARoa6h9sDODmp69vq1oXDh2HIEFi8OHdrosSDaTRQtKito8hq9OjR9OjRg/r16wMQGBjIKjPmyXbs2JFJkyZlOZ6YmIher0en02X7uK1bt7J06dJHC1oIkT/stKNKS0vLdurijRs3mDNnjslaNHvvm2R6pBBCOBDD9MgMcsiqmjRR1wtcuQL795t1Tn9/NXHTamHpUliwwFLRCnu1Y8cONm3axJQpU4zHAgMDOXPmDMnJyQ98bPv27bl+/XqWLQK8vb3p06cPAQEBrFixwuS+nTt3UqxYMUJCQiz3IoQQhZ6zszMjR46kRYsWeHh4cOnSJe7cuUPXrl358ssv8fX1Nba1975JkjYhhHAg/yVtOXBxgbZt1eu5KGHcrBkYPr8PHWp2vicKqCZNmnDhwgWTRfsBAQHo9Xrc3Nwe+FidTsfUqVP56KOPsty3bNkykpKS6N27t8nxyZMnM2PGDMsEL4RwKDNmzCA+Pp7ExERKlSpFjx49eOutt2jUqJFJO3vvm2R6pBBCOBBjIZKcRtoAOnWC1avVpG3yZLPP/b//qWvafvkFXnwR/v4bSpR41IhFQdWtWzf279+Ph4cH+/bt49NPPzXe17NnT3r27Gn2uTZu3JgfIQohHMz333/Pjh07SEhIYNasWXTo0IHRo0cb77fnvklG2oQQwoE4mZO0tW8POp26SO3cObPPrdGo0yOrVIGYGOjVC/T6R41YFFSrVq3i7NmzHDp0yCRhE/Zhzpw5VK5cGTc3N4KDg9m5c+cD269YsYLatWvj7u6Ov78/r776Kjdu3LBStEJYRp8+fYiPj2fbtm1s27bNJGGzd5K0CSGEA9Fp1G4/xzVtAMWLg2F+fi6mSAJ4ecHatWphr/BwmDgxr5EKIfLLypUrGTFiBOPHjycqKoqQkBDatWtHTExMtu137dpF37596d+/P9HR0fz444/89ddfDBgwwMqRC+G4JGkTQggHYkjaHjjSBvD88+rfuUzaAAID/ytGMnUq/PRTrk8hhMhHM2fOpH///gwYMIAaNWoQGhpK+fLlmTt3brbt//jjDypVqsSwYcOoXLkyzzzzDG+88Qb7ZfGqEFYjSZsQQjgQJ41asljRPKT779RJ/Xv7drh5M9fP07MnGPZV7tcPjh3L9SmEEPkgNTWVyMhIWrdubXK8devW7NmzJ9vHNGrUiAsXLhAWFoaiKFy5coXVq1fToUOHbNunpKSQkJBgchFCPBpJ2oQQwoE4ac1Y0wbw2GPqkJler+7ZlgeffgpNm8Lt2+o+3bdu5ek0QggLun79Onq9Hj8/P5Pjfn5+XL58OdvHNGrUiBUrVtC9e3dcXFwoXbo03t7ezJ49O9v206ZNw8vLy3gpX768xV+HEI5GkjYhhHAgxpG2hyVtAJ07q3///HOensvZGVatgnLl4MQJePlldb9uIYTtaTSmfYCiKFmOGRw9epRhw4YxceJEIiMj+f333zl37hyDBg3Ktv3YsWO5deuW8XL+/HmLxy+Eo5GkTQghHIghacswp/s3JG2//w537+bp+UqVUte0ubrC+vXwwQd5Oo0QwkJ8fX3R6XRZRtWuXr2aZfTNYNq0aTRu3JjRo0cTFBREmzZtmDNnDosWLSI2NjZLe1dXVzw9PU0uQohHI0mbEEI4ECdtLkba6taFChXgzh2IiMjzc9arB998o17/6CO1uqQQwjZcXFwIDg4m4r73dERERJbNhg3u3LmDVmv6kVGnu9eXKEr+BCqEMFFokrZ//vmH/v37U7lyZYoUKcLjjz/O+++/T2pqqkm7mJgYOnbsSNGiRfH19WXYsGFZ2gghRGHlZKweaUb3r9H8N9r2iCUg+/b9rzBJ377qFnBCCNsYNWoUCxYsYNGiRRw7doyRI0cSExNjnO44duxY+vbta2zfsWNH1q5dy9y5czl79iy7d+9m2LBhNGjQgDJlytjqZQjhUJxsHYClHD9+nIyMDL7++muqVKnCkSNHGDhwIElJScyYMQMAvV5Phw4dKFmyJLt27eLGjRv069cPRVFyXEwrhBCFibP2v+qRD1rDYvTCC/DFF2rp//R0cMr7r43p09VkbfNmdUeBv/4CX988n04IkUfdu3fnxo0bTJo0idjYWGrVqkVYWBgVK1YEIDY21mTPtldeeYXbt2/z5Zdf8vbbb+Pt7U2LFi345JNPbPUShHA4hWakrW3btixevJjWrVvz2GOP8fzzz/POO++wNtM8nPDwcI4ePcry5cupW7cuLVu25LPPPmP+/PlSjlYI4RCctc6AOtKWnpH+8Ac884yaWd28qZb/fwROTmphkscfh3/+gZdegrS0RzqlsANNmzbF19eXjRs3mv2YH374ge7du5vdvlWrVmzatCkv4YkcDBkyhH/++YeUlBQiIyNp0qSJ8b4lS5awbds2k/ZvvfUW0dHR3Llzh0uXLrF8+XLKli1r5aiFyF/23DcVmqQtO7du3aJ48eLG23v37qVWrVomQ/lt2rQxdlg5kf1GhBB5MWfOHCpXroybmxvBwcHs3Lnzge1XrFhB7dq1cXd3x9/fn1dffZUbN25YNKb/1rSZmbQ5Of03RXLNmkd+/uLF4ZdfoFgxNQd86y2QJTEF27Zt24xfnJojIyODcePGMWHCBLOfY8KECYwePTqvIQohHNyTTz7Jzw+phGzvfVOhTdrOnDnD7NmzTcrRXr58OUtlJB8fH1xcXHLcmwRkvxEhRO6tXLmSESNGMH78eKKioggJCaFdu3YmU44y27VrF3379qV///5ER0fz448/8tdffzFgwACLxuWiVac3KhotaRlmDnN16aL+/dNPFqnZX7MmrFihLpn7+muYM+eRTylsSKPR0LJlS44cOWJW+7CwMIoXL05gYKDZz9GkSRPi4+PZvXt3XsMUQjiotLQ0oqOjCQoKemA7e++b7D5p++CDD9BoNA+87N+/3+Qxly5dom3btnTt2jXLB57s1m88bF2H7DcihMitmTNn0r9/fwYMGECNGjUIDQ2lfPnyzJ07N9v2f/zxB5UqVWLYsGFUrlyZZ555hjfeeCNL//aonA1JG1rS9GYmbc8+C15ecPky7NljkTg6doSPP1avDx/+SMUphR04ePAgJ0+eJM2M+a7r1q2jRYsWJscaNGiAh4cHbm5u6HQ6PDw88PDw4PPPPwfU390tWrRg3bp1+RK/EKJwunjxIp6enqSlpREUFISHhwcHDx7Mtq299012n7QNHTqUY8eOPfBSq1YtY/tLly7RvHlzGjZsyDeGGtP3lC5dOsuIWlxcHGlpaTnuTQKy34gQIndSU1OJjIykdevWJsdbt27NnhySnkaNGnHhwgXCwsJQFIUrV66wevVqOnToYNHYnO4lbeRmpM3FRc2ywCJTJA1Gj1YrSer10LUrHD9usVMLKzpw4ACLFy8mPT2dkydPmtW+evXqJsf27dtHYmIiEydOpHPnziQmJpKYmMjIkSONbQICAjhw4IClwxdCFGJly5ZlwYIFNGrUyNiv1K5dO9u29t432X31SF9fX3zNLC928eJFmjdvTnBwMIsXL86yp0jDhg2ZMmUKsbGx+Pv7A2pxEldXV4KDgy0euxDCMV2/fh29Xp/lyyA/P78cp2I3atSIFStW0L17d5KTk0lPT+f5559/YGXblJQUUlJSjLfNWW+r02gBvflr2gxefBGWL1eTts8+A+2jf+en0aj7t505A7t3q3nhH39AiRKPfOrCRVHUvfKsxd1d/ccxg6IoDB48mIkTJ/Lll19y5MgRatasyd69exk1ahQuLi54eHiwYsUKvL29AfXL0py+/Dx06FCOH6g8PT2Ji4vL00sSQuQvRVG4k2a9fsrd2f3h1Y/viYqKom7dugBcuXKFF154AWdnZ/R6PfPmzTMO/th732T3SZu5Ll26RLNmzahQoQIzZszg2rVrxvtKly4NqN9yBwQE0KdPH6ZPn87Nmzd55513GDhwoIyeCSEs7v5fKA+ain306FGGDRvGxIkTadOmDbGxsYwePZpBgwaxcOHCbB8zbdo0Pvzww1zFZEjaMnIzPRKgTRvw8IDz52HfPnj66Vw9b05cXdWlcg0awOnT6vK5iAh1cE/cc+eO+rO3lsREKFrUrKbz588nOTmZ4cOHs337do4cOUL37t2pWLEimzdvxt3dnXnz5vHVV18xfvx4QF1LntMXDAcPHqRnz57Z3peQkICPj0/eXpMQIl/dSbuDxzTr9VOJYxMp6mJePxUVFUWvXr0AdTBo165daLVatm3bxscff8zy5csB+++b7H56pLnCw8M5ffo0W7ZsoVy5cvj7+xsvBjqdjg0bNuDm5kbjxo3p1q0bnTt3Nu7jJoQQluDr64tOp8syqnb16tUcp2JPmzaNxo0bM3r0aIKCgmjTpg1z5sxh0aJFxMbGZvuYvKy3NVaP1OjMnx4JUKSIurkaqHX7LahkSVi/Hjw9YccOeP11qShZEFy7do3x48fzzTffoNPpCAwMJDo6GoAyZcrg7u4OgJOTE06Z9verU6cOx7OZC5uens6pU6dyLAJw9OhR6tSpY/kXIoQo1A4ePGgcadPpdMaZePHx8SajZ/beNxWakbZXXnmFV1555aHtKlSowPr16/M/ICGEw3JxcSE4OJiIiAheeOEF4/GIiAg6deqU7WPu3Llj8sEW1F8uoI7QZcfV1RVXV9dcxaa7911drgqRGHTtCt99B6tXw4wZFpkiaVCzppoLdugAS5dC1apwb2BGuLuro1/WfD4zjB49mh49elC/fn0AAgMDWXVfQn/jxg3mzJlDeHi48VjHjh2ZNGlSlvMlJiai1+uN/+/vt3XrVpYuXWruqxBCWJG7szuJY63XT7k7m9dPpaWlZZm6ePToUQYMGMD58+dZk2mdtt33TYrItVu3bimAcuvWLVuHIoRdkPdEVj/88IPi7OysLFy4UDl69KgyYsQIpWjRoso///yjKIqijBkzRunTp4+x/eLFixUnJydlzpw5ypkzZ5Rdu3Yp9erVUxo0aGD2c5rz7/B9zH6FrVuVaptnKwdiD+TuRd25oygeHooCirJ3b+4ea6Y5c9TTg6J8/32+PIXdu3v3rnL06FHl7t27tg4lR9u3b1fKli1r8n8tOjpa0Wq1xriTkpKU5s2bK7t37zZ5bHp6ulKpUiXl8OHDWc7bp08fxd3dXVm+fLnJ8R07dihBQUE5xpPTz0z6Jvsg/w6FT0HopwzefvttxcvLSylatKhy8eJF4/EDBw6Y/I61976p0EyPFEIIe9K9e3dCQ0OZNGkSderUYceOHYSFhVGxYkUAYmNjTfZse+WVV5g5cyZffvkltWrVomvXrlSrVo21a9daNC7dvemRGeRyeiSoUyQNVSQtPEXSYPBgGDVKvf7KK7BrV748jXhETZo04cKFCybrwQMCAtDr9bi5uZGenk6PHj146623aNSokcljdTodU6dO5aOPPspy3mXLlpGUlETv3r1Njk+ePFmWMggh8mTGjBnEx8eTmJhIiUyVrry9vY3TuMH++yaNosjKgdxKSEjAy8uLW7duSQETIZD3hL0w599h7aXDvHjyBlWUEyyrEkTD8g1z9yS//AKdO0PZshATY9EpkgaGLQB++gmKF1crSlatavGnsVvJycmcO3eOypUr4+bmZutw8uTbb7/lrbfeMq7z6NChA6NHj86358vpZyZ9k32Qf4fCp6D2U3v37mXMmDFotVoURWHmzJk8+eST+fZ8luybCs2aNiGEEA/npFGTrIzclvw3aNtW3Wj74kW1Tn9IiIUjBJ1O3V2gWTP46y9o1w727lULloiCoU+fPvTp08fWYQghhImGDRuyfft2W4eRJzI9UgghHIixeiS52Fw7M1dXdaQNYOVKywV2H3d3+PVXqFxZ3cetY0frblUmhBBC2BNJ2oQQwoFoNZmSttxWjzTo3l39+8cfIT0Po3Vm8vODsDDw8YE//4RevdSpk0IIIYSjkaRNCCEciI57hUg0eRxpA2jZUl1sdvUqbNtmueCyUb26uozO1VX9e9gw2cNNCCGE45GkTQghHIiTsXqklnR9at5O4uysVgoB+P57C0WWs5AQdY2bRgNz5sDHH+f7UwohhBB2RZI2IYRwIE6a/5K2NH1K3k/Uq5f695o1kJxsgcge7KWXIDRUvT5unLoBtxBCCOEoJGkTQggHotOqRYMz0JKW8QhJ2zPPQLlycOsW/PabhaJ7sGHD4N131ev9+6vr3YQQQghHIEmbEEI4EF3mkv+PMtKm1ULPnur1776zQGTmmTYN+vT5by+3P/6w2lMLIYQQNiNJmxBCOBCnTCNt+ow8rmkzMEyR/PVXdcTNCrRaWLgQ2rRRtwB47jk4dswqTy2EEELYjCRtQgjhQJw0atKmR/do0yMBateGGjUgJQXWrrVAdOZxdobVq6FBA7hxA1q3hvPnrfb0QgghhNVJ0iaEEA7EMD1SQYP+UaZHglrO8eWX1evffvuIkeWOhwds2KBuCXDhgpq4Xb9u1RCEEEIIq5GkTQghHIhOowHurWnL6z5tmfXurf69bZvVh7t8fWHjRrUeyvHj0K4d3L5t1RAE0LRpU3x9fdm4caPZj/nhhx/obtik3QytWrVi06ZNeQlPCCHMZs99kyRtQgjhQAxJmx7do69pA6hYEZo0UXe8tsKebferUAHCw6FECdi/Hzp1ssoOBCKTbdu20bZtWxYvXmxW+4yMDMaNG8eECRPMfo4JEyYwevTovIYohHBwTz75JD///PMD29h73yRJmxBCOBCTkbZHnR5pYJgiuWyZmrxZWY0a6q4DHh6wdSv06AHp6VYPw2FpNBpatmzJkSNHzGofFhZG8eLFCQwMNPs5mjRpQnx8PLt3785rmEIIB5WWlkZ0dDRBQUEPbGfvfZMkbUII4UB09/7OQItescD0SFBr77u6QnQ0REVZ5py5VL++WsTS1RV++QVeew0yMmwSikM6ePAgJ0+eJC3t4f+n1q1bR4sWLUyONWjQAA8PD9zc3NDpdHh4eODh4cHnn38OqIlhixYtWLduXb7EL4QonC5evIinpydpaWkEBQXh4eHBwYMHs21r732TU74/gxBCCLuReaTNItMjAby91XmJq1bB0qXw5JOWOW8uNWumVpV84QW1LkqxYvDll2q9lIJMURTupN2x2vO5O7ujycUP7cCBAyxevJj09HROnjxJzZo1H9p+0KBBJsf27dsHwNSpU4mMjGTNmjVZHhcQEEB4eLjZcQkhrEdRFDIyrNdPabXm9VNly5ZlwYIFzJ07l127dj2wrb33TZK0CSGEA9FmTtr0FhppA+jXT03avvsOpk8HFxfLnTsXnntOnaXZuzfMmQNFi8InnxTsxO1O2h08pnlY7fkSxyZS1KWoWW0VRWHw4MFMnDiRL7/8kiNHjlCzZk2uXLnCCy+8gLOzM3q9nnnz5lGrVi0A4uLi8PT0zPZ8hw4donbt2tne5+npSVxcXN5elBAiX2Vk3GHnTuv1UyEhieh05vVTUVFR1K1b1+TYrl27CAkJ4dq1a/j6+gL23zfJ9EghhHAgptMjLTTSBmrN/dKl1br7v/1mufPmQc+e8PXX6vXp0+Gjj2waTqE2f/58kpOTGT58OIGBgcZ1bb6+vuzatYvt27czefJkPv74Y+NjfHx8SEhIyPZ8Bw8ezPGDUUJCAj4+PpZ/EUKIQi0qKoo6deqYHPv888+pV6+eyTF775tkpE0IIRyIYXqkgpZ0vQWTNicndXjrs8/UKZKdOlnu3HkwcCAkJcHIkfD++1CkCBTU4oPuzu4kjk206vOZ49q1a4wfP56wsDB0Oh2BgYFER0cDoNPpjO3i4+NNPuzUqVOH48ePZzlfeno6p06dyrEIwNGjR7N88BJC2Aet1p2QEOv1U1qtef0UqAnX9OnTjbd//fVXnnnmGX755ReTdvbeN0nSJoQQDkSXaZ6gXtFb9uSvvqombb/+CteuQcmSlj1/Lo0YAXfuwPjx8O674OYGb71l05DyRKPRmD1d0ZpGjx5Njx49qF+/PgCBgYGsWrXKeP/Ro0cZMGAA58+fN1kH0rFjRyZNmpTlfImJiej1epOEL7OtW7eydOlSC78KIYQlaDQas6crWlNaWprJ1MWMjAzmzp3L2rVrsyRt9t43yfRIIYRwIJk7/XRLVY80qFlTLeOYng7Ll1v23Hk0bhwYttwZNgy++ca28RQWO3bsYNOmTUyZMsV4LDAwkDNnzpB8b6O8gIAA9uzZw/r163krU7bcvn17rl+/nmWLAG9vb/r06UNAQAArVqwwuW/nzp0UK1aMkJCQfHxVQojCxtnZmZEjR9KiRQs8PDxYvnw5zz//PG5ublna2nvfJEmbEEI4kMwjbWkZFh5pA7XWPsCiRTbZsy07kybBO++o1994A8zcA1o8QJMmTbhw4YLJov2AgAD0ej1ubm6kpPy3B6C3tzfu7v9NZdLpdEydOpWPsllsuGzZMpKSkujdu7fJ8cmTJzNjxox8eCVCiMJuxowZxMfHk5iYSHR0NKtXr6Zt27YcOnSIXr16GdvZe98k0yOFEMKBZE7aMiw9PRLUna1HjoQjRyAyEu5b6G0LGg18+imkpsIXX0D//qDTQd++to6s8Pr7778ZM2YMWq0WRVGYOXOmyf09e/akZ8+eZp9v48aNlg5RCOGAPvnkE+P1Zs2a8d1335ncb899kyRtQgjhQDInben5MdLm7Q1duqil/xcssIukDdTELTQU0tJg7lx45RXQauHll20dWeHUsGFDtm/fbuswhBAiR9u2bbN1CLki0yOFEMKBZF5GrSc9f56kf3/17+++U0s42gmNRt1s+/XX1Zmb/frBfcsThBBCCLskSZsQQjgQbebqkRkZ+fMkzZrBY4/B7dvw44/58xx5pNWqI20DBkBGhjpF0k5qpgghhBA5kqRNCCEciNZkTVs+JW1arZoVgTpF0s5oterm24bErV8/+PZbW0clhBBC5EySNiGEcDBa1LVsevIpaQM1E9LpYPduOHo0/54njwyJ2+uv/5e4LVli66iEEEKI7EnSJoQQDkZ3L1nT50chEoMyZaBDB/X6/Pn59zyPwDBVcvBgdY3ba6/Z5cCgEEIIIUmbEEI4Gg3q/mkK+byP2htvqH8vXQp37+bvc+WRVgtffQVvvaUmbgMHwpw5to5KCCGEMCVJmxBCOBjtvWRNn99JW5s2ULEixMXZXUGSzDQamDULRo1Sb7/5Jty3rZgQQghhU5K0CSGEgzFMj8zIr+qRxifSqUNXoC4gs2MaDcyYAWPGqLfffhumTLFtTELkpzlz5lC5cmXc3NwIDg5m586dD2yfkpLC+PHjqVixIq6urjz++OMsWrTIStEKIQpl0paSkkKdOnXQaDQcOHDA5L6YmBg6duxI0aJF8fX1ZdiwYaSmptomUCGEsAHDSFuGJp9H2kBdKObkBHv2wOHD+f98j0CjgalT4cMP1dsTJsC4ceq0SSEKk5UrVzJixAjGjx9PVFQUISEhtGvXjpiYmBwf061bNzZv3szChQs5ceIE33//PdWrV7di1EI4tkKZtL377ruUKVMmy3G9Xk+HDh1ISkpi165d/PDDD6xZs4a3337bBlEKIYRtGJO2/Cr5n5m/P3TurF6fOzf/n+8RaTQwcSJMn67enjYNhg9XK0yK7DVt2hRfX182btxo9mN++OEHunfvbnb7Vq1asWnTpryEJ7Ixc+ZM+vfvz4ABA6hRowahoaGUL1+euTm8R3///Xe2b99OWFgYLVu2pFKlSjRo0IBGjRpZOXIh8pc9902FLmn77bffCA8PZ8aMGVnuCw8P5+jRoyxfvpy6devSsmVLPvvsM+bPn09CQoINohVCCOv7L2mz0hMOGaL+/e23UED62nfeUXNMjQZmz4b+/SE93dZR2adt27bRtm1bFi9ebFb7jIwMxo0bx4QJE8x+jgkTJjB69Oi8higySU1NJTIyktatW5scb926NXv27Mn2MevWraNevXp8+umnlC1blieeeIJ33nmHu3ZaYEiI+z355JP8/PPPD2xj731ToUrarly5wsCBA/n2229xd3fPcv/evXupVauWyShcmzZtSElJITIyMsfzpqSkkJCQYHIRQoiCSntvWmRGfhciMWjWDKpXh8REWL7cOs9pAYMGwbJl6tK8JUugRw9ISbF1VPZHo9HQsmVLjhw5Ylb7sLAwihcvTmBgoNnP0aRJE+Lj49m9e3dewxT3XL9+Hb1ej5+fn8lxPz8/Ll++nO1jzp49y65duzhy5Ag//fQToaGhrF69mjfffDPb9vK5SdiTtLQ0oqOjCQoKemA7e++bCk3SpigKr7zyCoMGDaJevXrZtrl8+XKWTsrHxwcXF5ccOyqAadOm4eXlZbyUL1/eorELIYQ16Qwl/621WEuj+W+0bc6cArVI7OWXYfVqcHGBNWugY0c19xSmDh48yMmTJ0lLS3to23Xr1tGiRQuTYw0aNMDDwwM3Nzd0Oh0eHh54eHjw+eefA2pi2KJFC9atW5cv8TsijUZjcltRlCzHDDIyMtBoNKxYsYIGDRrQvn17Zs6cyZIlS7IdbZPPTcJeXLx4EU9PT9LS0ggKCsLDw4ODBw9m29be+ya7T9o++OADNBrNAy/79+9n9uzZJCQkMHbs2AeeL7sO6UEdFcDYsWO5deuW8XL+/PlHfl1CCGErhumR5NztWV7fvlC0KERHw7ZtVnziR9e5M2zYoIYfEQEtW8LNm9Z7fkVR0OuTrHbJbTJ/4MABFi9eTHp6OidPnjSr/f0FLPbt20diYiITJ06kc+fOJCYmkpiYyMiRI41tAgICshQXE7nn6+uLTqfL8mX11atXs3yxbeDv70/ZsmXx8vIyHqtRowaKonDhwoUs7eVzk+NRFIUkvd5qF3P7qbJly7JgwQIaNWpk7Fdq166dbVt775uc8v0ZHtHQoUPp0aPHA9tUqlSJyZMn88cff+Dq6mpyX7169ejduzdLly6ldOnS/Pnnnyb3x8XFkZaWlmNHBeDq6prlvEIIUVBpNYACVq2t4eWlJm5z56qLxJo3t+azP7KWLWHzZmjXDv78E5o0gY0boWzZ/H/ujIw77Nzpkf9PdE9ISCI6XVGz2iqKwuDBg5k4cSJffvklR44coWbNmsb7d+3aRUhICNeuXcPX1xdQf+96enpme75Dhw7l+IHK09OTuLi4XL4acT8XFxeCg4OJiIjghRdeMB6PiIigU6dO2T6mcePG/PjjjyQmJuLhof5fPHnyJFqtlnLlymVpL5+bHM+djAw8HrJthCUlhoRQVKczq21UVBR169Y13i5atCj169cHYMyYMbRt2xaw/77J7kfafH19qV69+gMvbm5ufPHFFxw8eJADBw5w4MABwsLCALWs7ZR7m+00bNiQI0eOEBsbazx/eHg4rq6uBAcH2+T1CSGEtRmnR1r7iQ3rX375Bf7919rP/sieegp27oQyZdQBw0aN4MQJW0dlW/Pnzyc5OZnhw4cTGBiYZV3b559/nmXJgo+PT45rnA4ePJjjB6OEhAR8fHwsE7iDGzVqFAsWLGDRokUcO3aMkSNHEhMTw6BBgwB1pKxv377G9r169aJEiRK8+uqrHD16lB07djB69Ghee+01ihQpYquXIYRZoqKiqFOnjvF25cqV2bZtm7GIkoG99012P9JmrgoVKpjcNnwT9Pjjjxu/BWrdujUBAQH06dOH6dOnc/PmTd555x0GDhyYY2YthBB5NWfOHKZPn05sbCw1a9YkNDSUkJCQHNunpKQwadIkli9fzuXLlylXrhzjx4/ntddes2hc2nvTIq2etNWsCS1awJYt6ojbxx9bO4JHVrMm7N4NrVvDqVPwzDMQFgb3vrTNF1qtOyEh1ltIp9VmLeSVnWvXrjF+/HjCwsLQ6XQEBgYSHR1tvP/XX3/lmWee4ZdffjF5XJ06dTh+/HiW86Wnp3Pq1KkciwAcPXrU5IOXyLvu3btz48YNJk2aRGxsLLVq1SIsLIyKFSsCEBsba7Jnm4eHBxEREbz11lvUq1ePEiVK0K1bNyZPnmyrlyDsjLtWS+IDfr/lx/OZ6+DBg0w37OMCnD9/niZNmlC+fHm++OILSpQoAdh/31RokjZz6HQ6NmzYwJAhQ2jcuDFFihShV69e2W4PIIQQj8Kwee2cOXNo3LgxX3/9Ne3atePo0aNZvmQy6NatG1euXGHhwoVUqVKFq1evkp4PdeYNE0oUNA9d02txb72lJm3z56sbomVT6dfeVaoEu3ZB+/YQGanO9FyzBtq0yZ/n02g0Zk9XtKbRo0fTo0cP4zSjwMBAVq1aBaiFK+bOncvatWuzJG0dO3Zk0qRJWc6XmJiIXq9Hl8OUp61bt7J06VILvwrHNWTIEIYYCgTdZ8mSJVmOVa9enYiIiHyOShRUGo3G7OmK1pSWlpZl6uLZs2cpUaIEixYtYvz48cybNw+w/77J7qdH5lWlSpVQFCVL5luhQgXWr1/PnTt3uHHjBrNnz5Z510IIi7PnzWt193I0jUZLWsbDq/1ZVMeOULmyWsmjAJX/v1+pUrB1K7RqBUlJ8Nxz6vYAjmLHjh1s2rTJuPwA1KTtzJkzJCcn89133/H888/j5uaW5bHt27fn+vXrWaZSent706dPHwICAlixYoXJfTt37qRYsWIPHKkWQoj7OTs7M3LkSFq0aIGHhweXLl0yjqz16NGDqKgoY1t775sKbdImhBC2Yq3Na/O6F5Lhu0KNRktyerJZj7EYnQ6GDVOvh4YWqPL/9ytWDNavh1691I23+/WDadMK9EsyW5MmTbhw4YLJ0oKAgAD0ej1ubm4cPnyY1atX07ZtWw4dOkSvXr2M7XQ6HVOnTuWjjz7Kct5ly5aRlJRE7969TY5PnjxZZsUIIfJkxowZxMfHk5iYiJeXF3q9HoDt27dTtWpVYzt775scanqkEEJYw6NsXuvm5sZPP/3E9evXGTJkCDdv3mTRokXZPmbatGl8+OGHuY7PMNLGvaTN09XKa3pfe02dGnnsGISH59+8QitwcYFvv1WrSE6fDuPGwfnzaoFMO5wpZDWffPKJ8XqzZs347rvvTO7v2bMnPXv2NPt8GzdutFhsQgjHdfz4cQYOHIiHhweurq4sXLjQ5H577ptkpE0IIfJJfm5eC3nfC0l3LwabjLQBeHqqiRvAzJnWf34L02rh009h1ix1H/G5c6FLF3XapIBt27YZy/0LIYQtBQcH8/fff7Njxw4iIiJyXGNujyRpE0IIC7PG5rWg7oXk6elpcjGHoeS/Bi0p6SlmPcbihg9Xs53wcLhv/UBBNWwY/PgjuLrCunVqgZIrV2wdlRBCiMJAkjYhhLCwzJvXZhYREZFjYZHGjRtz6dIlEhP/K+3+oM1rH4XW1iNtoBYj6dJFvV4IRtsMXnxRLY5ZogT89Rc8/bQ6C1QIIYR4FJK0CSFEPrDnzWuNHb8tkzaAt99W/16xAmJjbReHhTVqBHv2wOOPwz//QMOGaiInhBBC5JUkbUIIkQ+6d+9OaGgokyZNok6dOuzYscOszWvj4+OpV68evXv3pmPHjnzxxRcWj+2/QiQ62yZtTz+tZjipqWrljkLkiSdg71715d26pdZayaGejBBCCPFQUj1SCCHyib1uXpt5n7YUvY3WtBm8+y507gxz5sDYsWod/UKiZEnYvBlefRV++AH694eTJ2HqVHU5nxBCFHSKI+xx8ggs+fORXxtCCOFgtBizNtuOtIG62Xa1aupw1Pz5to0lH7i5qbM/33tPvf3JJ9C1q1SWFEIUbM7OzgDcuXPHxpHYt9TUVEDdA+5RyUibcGj6DD07Y3YSezsW/2L+hFQIQad14M2VhEOwm+mRoA45jR4NAwaoBUmGDlU3PytEtFqYNEmdMtm/P6xdC+fOqRUmLVxjRgghrEKn0+Ht7c3Vq1cBcHd3z3FLG0eVkZHBtWvXcHd3x8np0VMuSdqEw1p7bC3Dfx/OhYT/yqmX8yzHrLaz6FKjiw0jEyJ/Gb6W0GDjQiQGL7+sDkVdvKgOS736qq0jyhcvv6wWzXzhBYiKgvr14eef4amnbB2ZEELkXunSpQGMiZvISqvVUqFCBYsktJK0CYe09thaXlr1Egqmc40vJlzkpVUvsbrbakncRKFl2Fxb0dhwn7bMXF1h1Ch1xO3jj6FvX7DAVBJ71Lgx7Nunzgo9cgSaNoWFC6F3b1tHJoQQuaPRaPD396dUqVKkpaXZOhy75OLigtZCi5hlTZtwOPoMPcN/H54lYQOMx0b8PgJ9ht7aoQkbSktL4/z585w4cYKbN2/aOpx8pbOHfdru98Yb4OOjVupYu9bW0eSrSpXULQGefx5SUtQRuP/9D/QFtMtp2rQpvr6+bNy40ezH/PDDD3Tv3t3s9q1atWLTpk15CU8Ikc90Oh1ubm5yyeZiqYQNJGkTDmhnzE6TKZH3U1A4n3CenTE7rRiVsIXExES+/vprmjVrhpeXF5UqVSIgIICSJUtSsWJFBg4cyF9//WXrMC1Om+ma3SRtxYrBW2+p16dNg0JekaxYMfjpJ7VgJsCnn6qjb/HxNg0rT7Zt20bbtm1ZvHixWe0zMjIYN24cEyZMMPs5JkyYwOjRo/MaYoHmSF8oCSFyJkmbcDixt83bxNfcdqJg+vzzz6lUqRLz58+nRYsWrF27lgMHDnDixAn27t3L+++/T3p6Oq1ataJt27acOnXK1iFbjGGkzeaba99v2DAoWlRd8BUWZuto8p1Wq5b//+47tcrkb7+p69vOnLF1ZLmj0Who2bIlR44cMat9WFgYxYsXJzAw0OznaNKkCfHx8ezevTuvYRYojvqFkhAiZ5K0CYfjX8zfou1EwbRnzx62bt3K/v37mThxIm3btiUwMJAqVarQoEEDXnvtNRYvXsyVK1d4/vnn2b59u61DtpjMSZvN92nLrEQJGDxYvf7RR4V+tM2gZ0/YvRvKl1dnh3brBgWtivbBgwc5efKkWeta1q1bR4sWLUyONWjQAA8PD9zc3NDpdHh4eODh4cHnn38OqIlhixYtWLduXb7Eb08c+QslIUTOpBCJcDghFUIo51mOiwkXs13XpkFDOc9yhFQIsUF0wlp+/PFH4/X169fTvn37bOeeu7q65rhBdkFltyNtAG+/DV9+CX/+qe5M3bKlrSOyiiefhP371T3c/v0Xrl2DK1egQgWw9yraBw4cYPHixaSnp3Py5Elq1qz50PaDBg0yObZv3z4Apk6dSmRkJGvWrMnyuICAAMLDwy0XuJ0yfKGU00ik4UulefPmsXDhQrZv307VqlWtHKUQwtpkpE04HJ1Wx6y2swA1QcvMcDu0bajs1+ZAOnXqxPXr120dhtUYN9e2pzVtBqVLw+uvq9cnTXKY0TaAUqVg0ya1MAmoidvp05CWppCk11vtouTiZ64oCoMHD2bixIlUqlTJZIpk0aJFadasGc2aNeP33383Ho+Li8PT0zPb8x06dIjatWtne5+npydxcXFmx1ZQ/fjjj2ZNHTV8oTRgwAArRCWEsDUZaRMOqUuNLqzutjrbfdpC24ZKuX8Hk5sPqYVB5pL/yWl2lrQBvPsuzJsHO3fC1q1w31S6wszZGSZMgMOH1RG2W7fg72MZPH3TeoWREkNCKGrmlgvz588nOTmZ4cOHs337do4cOWKsClm5cmW2bduW5TE+Pj4kJCRke76DBw/Ss2fPbO9LSEjAx8fHvBdRSDxoFoAQwrFILyAcVpcaXfhn+D9s7beV77p8x9Z+Wzk3/JwkbA7qwIEDJCUlmRy7ePFijiMCBZlxeqQ9jrQBlC3732jbBx841GibgYcHPPYYuLhAqh0tO8zs2rVrjB8/nm+++QadTkdgYCDR0dHG+8+fP0+TJk3o3bs3N27cMB6vU6cOx48fz3K+9PR0Tp06leMo09GjR6lTp47FX4c9c7RZAEKInMlIm3BoOq2OZpWa2ToMYQfatWuHRqOhUqVKBAUFUa1aNf7991+8vb1tHZrF6TT3vq/T6EjR22HSBjBmDMyfr462bdkCzz5r64isrkgRCAiAs2e17EBdY+vrC+XKqZUn84u7mScfPXo0PXr0oH79+gAEBgayatUq4/1nz56lRIkSLFq0iPHjxzNv3jwAOnbsyKRJk7KcLzExEb1ejy6HUb6tW7eydOnS3L6cAs3RZgEIIXImI21CCAGcPHmSnTt38u6771KmTBkOHz5MfHw833zzja1Ds7j/CpFo7HOkDUxH2yZOdMjRNgAnJ6haVcPjZXUU0ehIuqHj/CkdTuk6iury56Ixo/LJjh072LRpE1OmTDEeCwwM5MyZMyQnq/+nSpQoAUCPHj2Iiooytmvfvj3Xr1/PskWAt7c3ffr0ISAggBUrVpjct3PnTooVK0ZIiOMViHKkWQBCiJzleqRt1qxZDB8+nBMnTlC1alWZZy2EKBSKFSvG448/TsOGDW0dSr7T3StEoqAlxV6TNlB3np4/H/bsgd9/h3btbB2RTWg0UKaMuoXduXPqdgBHj0LlymCrgeAmTZpw4cIFk2MBAQHo9XoAkpKSjOX7769uqNPpmDp1Kh999BErV640OceyZctYtmxZluebPHkyM2bMyIdXYv8caRaAECJnuU7aatWqBcDIkSM5ffo0Hh4e1KxZk1q1alGrVi06dOhg8SCFECI/dezYEWdnZ1uHYTXG6ZHo7HekDcDfH4YOhRkz1Oocbdvaf/37fOTlpU6XPHMGkpLUypJ+fuqgpL19f3r8+HEGDhyIh4cHrq6uLFy40OT+nj175lhwJDsbN260dIgFxsmTJ7l69SqHDx/m0KFDHD58mIyMjEI5C0AIkbNcJ23P3ltXEBYWBqjVnI4cOcKRI0eIiIiQpE0IUeD88ssvtg7BqtTpkQqKRktKup1WuTAwVJL8+2/46Sfo4tiFglxcoFo1uHhR3cftyhVITFSLlri62jq6/wQHB/P333/bOoxCwZFmAQghcmb2d3Pjxo3L9rinpycNGzbk9ddfJzQ01FJxCSGEyCeGkTbFXqtHZlayJIwYoV6fMAHS020ajj3QaqF8eXj8cdDp1FG3o0fBAbYwcziONgtACJEzs5O2tWvXZjvPfNWqVcYpk0IIUVDExMTkqv3FixfzKRLr0xqrRxaApA3gnXegeHE4dgy+/dbW0dgNHx91umTRoqDXq9MmY2IgI8PWkQlL+eWXXxxubzohRPbMTtrWr1/P2LFj2bFjB6AmcUFBQfTs2ZOAgIB8C1AIIfJD/fr1GThwIPv27cuxza1bt5g/fz61atVi7dq1VowufzkVpJE2UBdzjR2rXv/gA0guADFbiaurOl3Sz0+9ffWqmtvevWvbuETeOfIXSkKInJmdtFWpUoWVK1fSo0cP6tatS9euXalRowaHDh3ixx9/zM8YhRDC4o4dO4aXlxdt27bFz8+PDh06MHDgQN566y1efvllnnzySUqVKsWSJUuYPn06b731lq1DthhDyX9FoyVFb+dr2gzefFOtuBETA3Pm2Doau2KYLlm1qrpFwN27auJ27ZrD7pRQoDnyF0pCiJyZXYhk79691K1bl88++4xXXnmF3bt38/TTT+dnbEIIkW+KFy/OjBkzmDx5MmFhYezcuZN//vmHu3fv4uvrS+/evWnTpk2hnP5doNa0GRQpApMmQf/+MHkyvPqqOj9QGBmqS/7zDyQkwL//qn9XrKgmc6JgOHbsGFOnTqVt27Y4OztTr149ypQpg5ubG3FxcRw9epTo6Gjq1avH9OnTaeegW2EI4WjM7sYbN26MVqvl8ccfx9XVlQkTJjBy5Ehq165NuXLl8jNGIYTIN25ubnTp0oUuDlSV0MlY8r8AJW0A/frBzJkQHQ0ffwyffGLriPKVkodhMhcXdcTtyhW1wmRcnFpdsnJlKMx7MWcUooV8jvyFkhAiZ2YnbXFxcRw8eNDk0rVrV5KTk/Hx8eHGjRv5GacQQuS7WbNmMXz4cE6cOEHVqlXR2tvmVxZiKESi3CtEoigKmoKw/5lOpyZqzz0Hs2bBkCHqMFIh4+zsjEaj4dq1a5QsWTJP/zbe3moCd+ECpKbCyZPg6wulStnfnm6PQlEUUlNTuXbtGlqtFhcXF1uHZDGO+IWSECJnZidtXl5eNGnShCZNmhiPZWRkcPz4cQ4dOpQvwQkhhDUZvrkeOXIkp0+fxsPDg5o1a1KrVi1q1apVaPah1Gl0gDo9EiAtIw0XXQH5sNu+PTRvDlu3wrhxsGKFrSOyOJ1OR7ly5bhw4QL//PPPI55LrduSmAjXr8PZs2ryVtiqyLu7u1OhQoVC+0WLo3yhJITI2SPNctdqtQQEBEj1SCFEofDss88CEBYWBkBCQgJHjhzhyJEjREREFKKkTf3Al3EveUtOTy44SZtGA599BsHB8N13MHw4NGhg66gszsPDg6pVq5KWlmaR823erG5zFxenjsCNHKnONi0Mn/11Oh1OTk4FY7Q4jxzlCyUhRM5kabIQQuTA09OTRo0a0ahRI1uHYlGZS/6DmrR5uhagBU9160LfvrB0qZp97NqlJnOFjE6nQ6fTWeRcHTqoeW7//hAWps4sXbUKliwplDNMC42GDRuyceNGh/lCSQiRs0LwHZupDRs28NRTT1GkSBF8fX2zzAWPiYmhY8eOFC1aFF9fX4YNG0ZqaqqNohVC2Jv09HSio6NZuXIl7733Hi+88IKtQ7I47b0RNkOiU6CKkRhMmQLu7rBnD6xcaetoCoTSpWH9evj6a3VD7m3bIDAQFi2SrQHs1Z9//klyDvsS/vTTT7z++uuEhoZaNyghhE0UqpG2NWvWMHDgQKZOnUqLFi1QFIXDhw8b79fr9XTo0IGSJUuya9cubty4Qb9+/VAUhdmzZ9swciGELZw9e5bDhw8bv7E+cuQIJ0+eJD09HRcXF2rUqEFgYKCtw7Q4J63a9evR4ayBlPQCsldbZmXLwpgxMHEivPsuPP+8msSJB9Jo4PXX4dln1emRu3ero29r18I330CZMraOUAB06dKFBg0aoNFouHr1KqVKlTK5PykpiZkzZzJ9+nQbRSiEsLZCk7Slp6czfPhwpk+fTv/+/Y3Hq1WrZrweHh7O0aNHOX/+PGXu/WYy7Ds3ZcoUPAtzPWQhhImXX36Z77//Ho1Gg7u7O0lJSXTo0IGJEycSGBhI1apVLTY1zd44adUqFBlocdEW0JE2gHfegQUL1A23p0+H99+3dUQFxuOPw/bt6g4K770HGzZAzZrwxRfw8suFcrZpgVKxYkXWr1+PoijUrl2bEiVKULt2bWrXrk1QUBCHDh3C39/f1mEKIayo0EyP/Pvvv7l48SJarZa6devi7+9Pu3btiI6ONrbZu3cvtWrVMiZsAG3atCElJYXIyMgcz52SkkJCQoLJRQhRsK1evZrZs2eTmJjIpUuXGDp0KOHh4fz1119UrFix0CZsAE4a9fu6DLQ4F+SkrUgRNVkDdd+2R6y06Gh0Ohg9Gv7+G+rVg/h4dang88/DpUu2js6xff755+zatQtnZ2f++OMP5s+fT+PGjTl9+jQffvgha9as4dNPP7V1mEIIKyo0SdvZs2cB+OCDD5gwYQLr16/Hx8eHpk2bcvPmTQAuX76Mn5+fyeN8fHxwcXHh8uXLOZ572rRpeHl5GS/ly5fPvxcihLCK0aNH07dvX9zc3PDw8GDWrFns3r2brVu3EhAQwO+//27rEPONk9ZQiERTsEfaALp2hWbN1Lr2b79t62gKpIAA2LtXXSbo7Kyue6tZUy1SImvdbCspKYn69evTqVMnPvjgA37++WfOnDnDuXPn6NWrl63DE0JYkd0nbR988AEajeaBl/3795ORkQHA+PHjefHFFwkODmbx4sVoNBp+/PFH4/myKwn8sI1lx44dy61bt4yX8+fPW/6FCiGs6qOPPsLDw8PkWHBwMPv27WPEiBF0796dXr16ce3aNRtFmH8MHb8eXcEeaQN1Ht8XX6jDRmvXQkSErSMqkJyc1G3vMo+6vfoqtGsH//5r6+gcl5NToVnFIoR4RHaftA0dOpRjx4498FKrVi3j3O7Me8a5urry2GOPERMTA0Dp0qWzjKjFxcWRlpaWZQQuM1dXVzw9PU0uQojCSaPRMHz4cI4ePUpKSgrVq1e3dUgWp7v3JVUGWrUQib4AFiLJLDAQ3nxTvT50KKQU8NdjQ7VqqaNun3wCrq6wcaM66jZ7Nuj1to5OCCEcl90nbb6+vlSvXv2BFzc3N4KDg3F1deXEiRPGx6alpfHPP/9Q8d4mNA0bNuTIkSPExsYa24SHh+Pq6kpwcLDVX5sQwn6VLVuWNWvWsGzZMluHYnGZk7YCPz3SYNIktab9yZMwY4atoynQnJzUgpwHD8Izz0BSEgwbBiEhkGmZuCjg5syZQ+XKlY2foXbu3GnW43bv3o2TkxN16tTJ3wCFECbsPmkzl6enJ4MGDeL9998nPDycEydOMHjwYAC6du0KQOvWrQkICKBPnz5ERUWxefNm3nnnHQYOHCijZ0KIbBXGTWsNJVYKVdLm5QWffaZenzwZzp2zbTyFQLVqaoXJr76CYsXUEbi6ddVdFnLYOkwUECtXrmTEiBGMHz+eqKgoQkJCaNeunXFmUk5u3bpF3759jZt9CyGsp9AkbQDTp0+nR48e9OnTh/r16/Pvv/+yZcsWfHx8ANDpdGzYsAE3NzcaN25Mt27d6Ny5MzPkW1khhAPRFsaRNoCePaF5czWjePNNqaJhAVotDBkCR4+qVSXT0uCjj6B2bdi61dbRibyaOXMm/fv3Z8CAAdSoUYPQ0FDKly/P3LlzH/i4N954g169etGwYUMrRSqEMChUSZuzszMzZszgypUrJCQkEBERQc2aNU3aVKhQgfXr13Pnzh1u3LjB7NmzcXV1tVHEQghhfSZr2rQFdHPt7Gg0MHcuuLjAb7/B6tW2jqjQKFcOfv5Z/ZEaZqG2aAGvvALXr9s6OpEbqampREZG0rp1a5PjrVu3Zs+ePTk+bvHixZw5c4b3zdgPUbZKEsLyClXSJoQQ4uEK5fRIg2rVYOxY9fqwYWoZRGERGg28+CIcP66Ovmk0sHSp+iNfuBDuFXEWdu769evo9fosBdj8/Pxy3P7o1KlTjBkzhhUrVphV0VK2ShLC8iRpE0KIfGKvC/0NI216dDhrClnSBjBmjJpJXL6sVtQQFuXlpa5z27NHnSZ58yYMGKAWKjl40NbRCXPdv9VRTtsf6fV6evXqxYcffsgTTzxh1rllqyQhLE+SNiGEyAf2vNDfkLQVis21s+PmBvPnq9fnz4dt22waTmH19NOwf79a/6VoUTWJCw6GESPg1i1bRydy4uvri06nyzKqdvXq1Wy3P7p9+zb79+9n6NChODk54eTkxKRJkzh48CBOTk5s2bIly2NkqyQhLE+SNiGEyAf2vNDf0PEb17QV9H3ashMSAoMGqdcHDoQ7d2wbTyHl5ASjRqlTJrt2VfdymzULqleH5culFow9cnFxITg4mIj7NqKPiIigUaNGWdp7enpy+PBhDhw4YLwMGjSIatWqceDAAZ566ilrhS6EQ5OkTQghLMwaC/0h74v9M0+PLJQjbQYff6xW0Dh9Gt57z9bRFGrlysGqVepm3E88oc5M7dMHmjSRKZP2aNSoUSxYsIBFixZx7NgxRo4cSUxMDIPufdExduxY+vbtC4BWq6VWrVoml1KlSuHm5katWrUoWrSoLV+KEA5DkjYhhLAwayz0h7wv9r+/emShTdq8vOCbb9Trn3+ubjQm8lXr1nDoEEydCu7usGsXPPmkWrjkxg1bRycMunfvTmhoKJMmTaJOnTrs2LGDsLAwKlasCEBsbOxDp3ILIaxLkjYhhMgn+bnQH/K+2F9XWPdpy067dtCvnzpP75VXZJqkFbi6qgU8jx+Hbt3UqpJz50LVqvDll5CebusIBcCQIUP4559/SElJITIykiZNmhjvW7JkCdsesBb0gw8+4MCBA/kfpBDCyLyvc4UQQpgtrwv9o6KiGDp0KAAZGRkoioKTkxPh4eG0aNEiy+NcXV3ztM+k4ds6QyGShMK4pi2zzz+HiAh1c7Hx49XbIt+VLw8rV8LgwTB8uDoC99ZbMG+e+k/QqpWtIxR2Q1HkCxVR+Li7q3ujWIgkbUIIYWGZF/q/8MILxuMRERF06tQpS3vDQv/M5syZw5YtW1i9ejWVK1e2aHyFvuT//Xx81I3E2rWD0FDo1AmaNbN1VA6jWTOIjFQLeb73HkRHq9Mon3sOZsxQd2cQDu7OHfDwsHUUQlhWYqJaWtdCZHqkEELkA3te6O9Q0yMN2rZVNxMDdbqk1KS3KicndcTt1Cl11M3JCdavh1q11Ns3b9o6QiGEsG8y0iaEEPmge/fu3Lhxg0mTJhEbG0utWrXsZqG/7t7fxkIkKQ6QtAHMnAlbtsDZszBsGCxdauuIHI6PjzrYOXgwvPOOmrh98QUsW6aOwr35promTjgYd3d1VEKIwsTd3aKn0yiK7KKSWwkJCXh5eXHr1i3ZMFII5D1hL8z9d4i6fZsnIyPx5RoDr3Rjy+2n+GPAH1aM1IZ271br0GdkqAuuunWzdUQObdMmePttdb0bQOXKauXJbt1Aa4G5QNI32Qf5dxDCVF7eEzI9UgghHIz2vumRd9IcqABA48ZqaUOA118HKWtuUy1bwt9/q0sO/f3h3Dno2ROeegq2brV1dEIIYT8kaRNCCAdjMj1SA4mpDjYt6f331azg1i11B2i93tYROTSdDl57TV3vNmmSWo9i/35o0QLat5fNuYUQAiRpE0IIh3N/IZLbqbdtHJGVOTvDihVqdrBjB0yebOuIBGqRtffegzNn1LVtTk7w229Qt66aW587Z+sIhRDCdiRpE0IIB5O55L+LFm6nOFjSBvD44+qGYaAO72zfbtt4hFGpUuom3MeOQffu6hZey5erWwMMHQr3bX8ohBAOQZI2IYRwMPdvrp2iTyFNn2bTmGyid2949VW1KEmvXnD1qq0jEplUqQI//KBOlWzdGtLS4Kuv4LHHYMwY2SZACOFYJGkTQggHk3l6pPO93wIOt67NYPZsqFEDLl2Cl1+W9W12KDgYNm5UC5M8/TTcvQuffKJWmpw0CRISbB2hEELkP0nahBDCwWSeHul677eAw61rMyhaFH78EYoUgYgIWd9mx5o1gz17YN06qF1bTdbefx/27bN1ZEIIkf8kaRNCCAdjUohEp/4acMh1bQY1a8Lcuer1Dz9Uh3WEXdJooGNHdZuAVatgwAB49llbRyWEEPlPkjYhhHAwhpL/6po2NYFz2OmRBv36wcCBatWLXr3g339tHZF4AK0WunaF+fPVRE4IIQo7SdqEEMLB/DfSpsPpXtLmsNMjM/viC3UB1c2b0KWLunhKCCGEsAOStAkhhINx02bq+jVOgINPjzRwc4M1a8DXV51/9/rr6sibEEIIYWOStAkhhIMpkilpyzAkbTLSpqpYUV0spdOpm4N9/rmtIxJCCCEkaRNCCEfjpNUa17WlaVzRImvaTDRvDp99pl4fPVoKkwghhLA5SdqEEMIBGaZIpuKCi1amR2YxbBi89pq68Xb37nD8uK0jEkII4cAkaRPCjiiKnri4bVy58j1xcdtQFNnoV+SPIrr7kjaZHmlKo4E5c6BxY7h1C557Dq5ft3VUQgghHJSTrQMQQqiuXVvL6dPDSUm5YDzm6lqOKlVmUbJkFxtGJgojN60OSCcFV1y0Mj0yW66usHYtPPUUnDmjVpSMiFCPCyGEEFYkI21C2IFr19YSHf2SScIGkJJykejol7h2ba2NIhOFVebpkc4yPTJnpUrBhg3g6Qk7d8Krr6pTJoUQQggrkqRNCBtTFD2nTw8Hsistrh47fXqETJUUFpUlaZPpkTkLCFC3AnBygu+/h/fes3VEQgghHIwkbULYWHz8ziwjbKYUUlLOEx+/02oxicKvyP2FSCRpe7CWLeGbb9TrU6fC3Lm2jUcIIYRDkaRNCBtLTY21aDshzGEy0qaRNW1mefVV+PBD9frQofDTT7aNRwghhMOQpE0IG3Nx8bdoOyHMISX/8+i99+D119V1bT17wrZtto5ICCGEA5CkTQgb8/YOwdW1HKDJoYUGV9fyeHuHWDMsUcjJ9Mg80mjgq6+gc2dISYHnn4fISFtHJYQQopCTpE0IG9NodFSpMstw6/57AahSJRSNRmfVuEThZhhpM5T8l5G2XDAUJGnWDG7fhrZt4dgxW0clhBCiECtUSdvJkyfp1KkTvr6+eHp60rhxY7Zu3WrSJiYmho4dO1K0aFF8fX0ZNmwYqampNopYCFXJkl2oWXM1rq5lTY67upajZs3Vsk+bsLj7q0fKmrZccnODX36BevXUTbdbtoSzZ20dlRBCiEKqUG2u3aFDB5544gm2bNlCkSJFCA0N5bnnnuPMmTOULl0avV5Phw4dKFmyJLt27eLGjRv069cPRVGYPXu2rcMXDq5kyS74+nYiPn4nqamxuLj44+0dIiNsIl8Uua8Qyd30u6RnpOOkLVS/FvKXpyf8/js0bQrR0dCiBezYARUq2DoyIYQQhUyhGWm7fv06p0+fZsyYMQQFBVG1alU+/vhj7ty5Q3R0NADh4eEcPXqU5cuXU7duXVq2bMlnn33G/PnzSUhIsPErEEKdKunj0ww/v574+DSThE3km/sLkYCMtuVJiRKwaRNUrQr//qsmbhcetIWHEEIIkXuFJmkrUaIENWrUYNmyZSQlJZGens7XX3+Nn58fwcHBAOzdu5datWpRpkwZ4+PatGlDSkoKkbKQXAjhQDKvaXPTqdclacuj0qVhyxaoXBnOnIHmzeHiRVtHJYQQohApNPNgNBoNERERdOrUiWLFiqHVavHz8+P333/H29sbgMuXL+Pn52fyOB8fH1xcXLh8+XKO505JSSElJcV4W0blhBAFXeaRtuIursBdKUbyKMqVg61b1eIkp0+rf2/ZAuXL2zoyIYQQhYDdj7R98MEHaDSaB17279+PoigMGTKEUqVKsXPnTvbt20enTp147rnniI39b1NijSZrWXVFUbI9bjBt2jS8vLyMl/LyS1gIUcAV0alTb1NxoaizCyBl/x9ZxYrqvm2VK6uJW9Om8M8/to5KCCFEIWD3I21Dhw6lR48eD2xTqVIltmzZwvr164mLi8PT0xOAOXPmEBERwdKlSxkzZgylS5fmzz//NHlsXFwcaWlpWUbgMhs7diyjRo0y3k5ISJDETQhRoGUeaSvq5AxI2X+LqFgRtm9Xp0ieOQMhIbB5MzzxhK0jE0IIUYDZfdLm6+uLr6/vQ9vduXMHAK3WdPBQq9WSkZEBQMOGDZkyZQqxsbH4+/sDanESV1dX47q37Li6uuLq6prXlyCEEHYnc9Lmfi9pkzVtFlK+vFpF8tln4fhxNXGLiICgIFtHJoQQooCy++mR5mrYsCE+Pj7069ePgwcPcvLkSUaPHs25c+fo0KEDAK1btyYgIIA+ffoQFRXF5s2beeeddxg4cKBxdE4IIRxBEZOkTf3+TqZHWlCZMuqIW506cPUqNGkCu3fbOiohhBAFVKFJ2nx9ffn9999JTEykRYsW1KtXj127dvHLL79Qu3ZtAHQ6HRs2bMDNzY3GjRvTrVs3OnfuzIwZM2wcvRBCWFfmkTa3e+vbZHqkhZUqpRYnadwYbt2CVq1g/XpbRyWEEKIAsvvpkblRr149Nm7c+MA2FSpUYL380hRCODiTkv9O95I2GWmzPG9vCA+Hrl0hLAw6d4ZvvoHXXrN1ZEIIIQqQQjPSJoQQwnwmI21atXqurGnLJ+7u8PPP0K8f6PXQvz98+CEoiq0jE0IIUUBI0iaEEA4o85o2l3tJm0yPzEfOzrB4MYwbp97+4AN49VVITbVpWEIIIQoGSdqEEMIBuZkkbeqxhJQEG0bkADQamDIFvv4adDpYuhRat4abN20dmRBCCDsnSZsQQjggk6RNp4603bh7w5YhOY7XX1cLkhQrplaYfOopdWsAIYQQIgeStAkhhAMymR6p5mxcu3PNhhE5mLZt1S0AKlaE06fh6afht99sHZVwIHPmzKFy5cq4ubkRHBzMzp07c2y7du1aWrVqRcmSJfH09KRhw4YPLfwmhLAsSdqEECKf2POHoswjbc5atSDGtSRJ2qwqMBD27ftvS4AOHeDjj6VAich3K1euZMSIEYwfP56oqChCQkJo164dMTEx2bbfsWMHrVq1IiwsjMjISJo3b07Hjh2JioqycuRCOC5J2oQQIh/Y+4ciQ9KWhgsoajEMGWmzgVKlYPNmGDhQTdbGjoVu3eC2FIUR+WfmzJn079+fAQMGUKNGDUJDQylfvjxz587Ntn1oaCjvvvsu9evXp2rVqkydOpWqVavy66+/WjlyIRyXJG1CCJEP7P1DUZF7G2oDpGWoSVt8cjxp+rR8eT7xAK6uanGSuXPVKpOrV0ODBnD0qK0jE4VQamoqkZGRtG7d2uR469at2bNnj1nnyMjI4Pbt2xQvXjzb+1NSUkhISDC5CCEejSRtQghhYdb4UASP9sHIVaMxXk/WJ6O9d/v6netmn0NYkEYDgwaphUnKlFELk9SvD8uX2zoyUchcv34dvV6Pn5+fyXE/Pz8uX75s1jk+++wzkpKS6NatW7b3T5s2DS8vL+OlfPnyjxy3EI5OkjYhhLAwa3wogkf7YOSk1eJ0L1FLRkeZoiUAmSJpcw0bQlQUPPss3LkDffqom3HfuWPryEQho8n0xQ2AoihZjmXn+++/54MPPmDlypWUKlUq2zZjx47l1q1bxsv58+ctErMQjkySNiGEyCf5+aEIHv2DUeZiJOU8vAEpRmIXSpWCjRth4kR1BG7RIqhXDw4etHVkohDw9fVFp9Nl+QLp6tWrWb5out/KlSvp378/q1atomXLljm2c3V1xdPT0+QihHg0krQJIYSFWeNDETz6B6PMZf/LengBMj3Sbuh08OGHsGkT+PvDsWPqfm6hoZCRYevoRAHm4uJCcHAwERERJscjIiJo1KhRjo/7/vvveeWVV/juu+/o0KFDfocphLiPJG1CCGFhBeVDUeaRttLuHoBMj7Q7LVqoI2zPPQcpKTBypLrH28WLto5MFGCjRo1iwYIFLFq0iGPHjjFy5EhiYmIYNGgQoI7i9+3b19j++++/p2/fvnz22Wc8/fTTXL58mcuXL3Pr1i1bvQQhHI4kbUIIkQ8KwoeizElbKTd3QKZH2qWSJWHdOvjqK3Bzg4gIqFULVqyQPd1EnnTv3p3Q0FAmTZpEnTp12LFjB2FhYVSsWBGA2NhYk+1Jvv76a9LT03nzzTfx9/c3XoYPH26rlyCEw3GydQBCCFEYde/enRs3bjBp0iRiY2OpVauW2R+K3nzzTePxfv36sWTJknyJMfP0yBJuroCMtNktjQaGDFFH3vr2hb/+gpdfVrcHmDsXSpe2dYSigBkyZAhDhgzJ9r77+5xt27blf0BCiAeSpE0IIfKJvX8oMpke6aL+OpCkzc5Vrw579sC0afDRR/Dzz+o2AZ9/riZzZhS6EUIIUfDI9EghhHBQhqQtBVc8ndXrMj2yAHBygvfeg/37oW5diIuDV16BNm3g7FlbRyeEECIfSNImhBAOKvNIW1GdujZKRtoKkKAg+PNP+PhjcHX9b63btGmQmmrr6IQQQliQJG1CCOGgMq9pK6JNB2SkrcBxdob//Q8OH1bXu929C+PGQZ06sGWLraMTQghhIZK0CSGEg8o8PdJZo47M3Lh7gwxF9gErcKpWVfd0+/ZbdXPuY8fg2Wehe3fI5abrQggh7I8kbUII4aAyT4/UcQeADCWDm3dv2jIskVcajVpR8vhxGDoUtFpYtQqqVVM36r5zx9YRCiGEyCNJ2oQQwkEV0ekANWnTpyfg5eoFyBTJAs/HB2bPhshICAlRp0x+8AE88QQsWwYZMpIqhBAFjSRtQgjhoDKPtKWnx1OyaElAipEUGnXqqNsBrFoFlSrBxYvQrx8EB8PGjbIxtxBCFCCStAkhhIPKkrS530vaZKSt8NBooGtXdY3bJ5+AlxccOABt26pr3v74w9YRCiGEMIMkbUII4aCKZBlp8wXgatJVW4Yl8oObG7z7Lpw+DSNHgosLbN0KDRvC889DVJStIxRCCPEAkrQJIYSDyjzSBnoqeZYGIOZWjA2jEvnK1xdmzoSTJ+G119RiJb/+Ck8+CS+8IMmbEELYKUnahBDCQf1X8r8IAFW9/QE4E3fGZjEJK6lYERYuVKdN9uqlTqP8+Wc1eevYUaZNCiGEnZGkTQghHJQhaUvXFAWgYrESAJyNO2uzmISVPfEErFgB0dFq8qbVwvr16rTJ5s3h99+lYIkQQtgBSdqEEMJBGda0pWnUkbZyxbwBSdocUo0aavJ27Jg6bdLJCbZtg3btoHZtdauA1FRbRymEEA5LkjYhhHBQxjVt95K2Um7uAMQlxxF3N85mcQkbeuIJddrk2bMwYgQULQqHD6tbBVSsCJMnwzWpLiqEENYmSZsQQjio/wqRuAGgI5nSHmoxEhltc3Dly8Pnn8P58zB1KpQpA5cvw3vvqfe98gr89ZetoxRCCIchSZsQQjgoTycnAJJQR9jS0+N5zOcxQJI2cY+PD4wdC+fOwbffQr16kJICS5dCgwZQv746MpeYaOtIhRCiUJOkTQghHFRpFxcArmWohUjS0+OMSZtUkBQmXFzg5Zdh3z7Yu1e97uIC+/fDgAHqSNygQeromxQuEUIIi5OkTQghHJT/vaQtUXEhGVfS0+N53OdxQEbaRA40Gnj6aXXU7cIF+OQTqFIFbt+Gr79WR9+CgtS94K5csXW0QghRaEjSJoQQDqqYTmesIHmDEqSl3ZDpkcJ8JUvCu+/CiROwZQv07g1ubnDkCLz9NpQtC+3bw3ffQVKSraMVQogCrcAkbVOmTKFRo0a4u7vj7e2dbZuYmBg6duxI0aJF8fX1ZdiwYaTeV6L48OHDNG3alCJFilC2bFkmTZqEIlM5hBAOSKPRGEfbblKc5ORzxpE2mR4pzKbVqnu6LV8OsbEwbx489RTo9fDbb2oy5+en7gO3bp26Jk4IIUSuFJikLTU1la5duzJ48OBs79fr9XTo0IGkpCR27drFDz/8wJo1a3j77beNbRISEmjVqhVlypThr7/+Yvbs2cyYMYOZM2da62UIIYRdyZy03b17xjjSFnMrhjR9mi1DEwWRtze88Qb88Yc6Avfee/DYY+pI2/ffQ6dOUKoU9O2rJnDJybaOWAghCoQCk7R9+OGHjBw5ksDAwGzvDw8P5+jRoyxfvpy6devSsmVLPvvsM+bPn09CQgIAK1asIDk5mSVLllCrVi26dOnCuHHjmDlzpoy2CSEckr+rK6BOj0xNvUjJIl4UcSpChpJBzK0YG0cnCrQnnoBJk+D0aTWJGzFCLViSkKCuievUSZ1i2b27mtDFx9s6YiGEsFsFJml7mL1791KrVi3KlCljPNamTRtSUlKIjIw0tmnatCmu9z6kGNpcunSJf/75x9ohCyGEzRlG2uI0/gCkpPzDEyWeAODw1cM2i0sUIhqNOl3SsO/bjh0wfLi65i0xEVatUqdOliwJzz4LoaFqoieEEMKo0CRtly9fxs/Pz+SYj48PLi4uXL58Occ2htuGNtlJSUkhISHB5CKEEIWBoex/vK4iAHfvnqFB2QYA/HHhD5vFJQoprRZCQtTELCYG/vwTxoyBGjUgPV0taDJyJFStCtWqqaNzGzfC3bu2jlwIIWzKpknbBx98gEajeeBl//79Zp9Po9FkOaYoisnx+9sYpkVm91iDadOm4eXlZbyUL1/e7JiEEMKe/TfSpn6BdffuGRqWawjA3gt7bRaXcABarbpFwLRpcPQonDqlbhXQogU4OcHJkzBrFrRtC8WLQ6tW8Omn8PffkJFh6+iFEMKqnGz55EOHDqVHjx4PbFOpUiWzzlW6dGn+/PNPk2NxcXGkpaUZR9NKly6dZUTt6tWrAFlG4DIbO3Yso0aNMt5OSEiQxE0IUSj8V4jEB4Dk5DM8XW4IAH9d/Iv0jHSctDb9VSEcRZUq6ijbyJFw6xZs3qxWn9y4UZ1WuWmTegE1iWvWTK1a2bWrWp1SCCEKMZv+Jvb19cXX19ci52rYsCFTpkwhNjYWf391bUZ4eDiurq4EBwcb24wbN47U1FRc7n1QCQ8Pp0yZMg9MDl1dXU3WwQkhRGFhKERyLcMdgLt3z1KrSjW83byJT47n0JVDPOn/pC1DFI7Iywu6dFEvigLHjkFEhHrZvh1u3oS1a9VLo0aStAkhCr0Cs6YtJiaGAwcOEBMTg16v58CBAxw4cIDExEQAWrduTUBAAH369CEqKorNmzfzzjvvMHDgQDw9PQHo1asXrq6uvPLKKxw5coSffvqJqVOnMmrUqAdOjxRCiMLKMNJ2Q69Dj5a7d8+g1Wh5quxTgKxrE3ZAo4GAALV4yfr1asK2Zw9MmQLPPw+1a9s6QiGEyHcFJmmbOHEidevW5f333ycxMZG6detSt25d45o3nU7Hhg0bcHNzo3HjxnTr1o3OnTszY8YM4zm8vLyIiIjgwoUL1KtXjyFDhjBq1CiTqY9CCOFIfJ2d0QEKGuMG24qi5+lyTwOyrk3YIWdnaNgQxo2DX34Bnc7WEQkhRL4rMAsVlixZwpIlSx7YpkKFCqxfv/6BbQIDA9mxY4cFIxNCiIJLq9Hg5+LCpdRUblKKksp1UlIuGouRyEibEEIIYXsFZqRNCCFE/jBMkUx0qQ78V/Zfg4bTN09zNu6sLcMTQgghHJ4kbUII4eAMxUhuOVUB4M6dE/gU8eHZx54FYMWhFTaLTQghhBCStAkhhMMzjLTddlZH2uLi1LLqfYL6APDtoW+Ne1oKIYQQwvokaRNCCAdn3KvNqSoAcXHhZGSk8kL1FyjiVIRTN0+x7+I+W4YohBBCODRJ2oQQwsHV9fAAYM8dJ5yd/dDrb3Pr1k6KuRbjhRovAOpomxBCCCFsQ5I2IYRwcM/6+OCs0XD6bjK3vboDcOPGBgD6BvUFYMmBJVxIuGCzGIUQQghHJkmbEEI4uGJOTjTx8gIg0qk1ADduqNuntHq8FY3KNyIpLYnREaNtFqMQQgjhyCRpE0IIQfsSJQDYllwajcaZu3dPcft2FFqNlq/af4VWo+WHIz+w5dwWG0cqhBBCOJ4Cs7m2EML69IrCzvh4YlNT8XdxIcTbG51GY+uwRD5oX7w4b585w45bibiX6ErS9e84eXIwTz65mzql6zAoeBBz9s+h649d2dpvK0F+QbYOWQhRSCiKwp20O7YOQwiLcnd2R2PBz0yStAkhsrX22jWGnz7NhZQU47Fyrq7MqlKFLiVL2jCygmPOnDlMnz6d2NhYatasSWhoKCEhITm23759O6NGjSI6OpoyZcrw7rvvMmjQIKvEWs3dncfc3DibnMwh7wk8Efcrt2//ycWLcyhX7i0+bvkxkbGR/HnxT1oua8mPXX+kaaWmVolNCFG43Um7g8c0D1uHIYRFJY5NpKhLUYudT5I2IUQWa69d46XoaO7fmetiSgovRUezumZNSdweYuXKlYwYMYI5c+bQuHFjvv76a9q1a8fRo0epUKFClvbnzp2jffv2DBw4kOXLl7N7926GDBlCyZIlefHFF/M9Xo1GQy8/Pyb/+y/Dz15jddlQ3M7358yZd4AMypYdxu8v/07LZS2JjI2k2dJm9K3dlxFPjaCuf918j08IUXgpioKbLNgRhYyl9zfVKLJjaq4lJCTg5eXFrVu38PT0tHU4QliUXlGo9McfJiNsmWlQR9zOPf20caqkvCeyeuqpp3jyySeZO3eu8ViNGjXo3Lkz06ZNy9L+f//7H+vWrePYsWPGY4MGDeLgwYPs3bvXrOd81H+HtIwM2h8+zKa4OEo5O9PLZQd1k+biTyw+HrUoVaoHOrdApv/1PbP3Lyfj3uOqlahGSIUQ6pSuQ5XiVShTrAwli5bE09WTIk5FLDo9RIjckL4pZ9acCfCwf4e0tNts2l0qz69FCHvUsvFVnJ2LZXtfXvomGWkTQpjYGR+fY8IGoADnU1LYGR9PMx8f6wVWgKSmphIZGcmYMWNMjrdu3Zo9e/Zk+5i9e/fSunVrk2Nt2rRh4cKFpKWl4ezsnOUxKSkppGT6t0pISHikuJ21WlbXrElIVBSHk5IITWsINERDBu6Jd3BLTMaJBHRF21K6aVtQQCGDBAU2oLAhEZTEK8CV+86syfSnEJb1bZUKPFtBpurmhr3NBLirKLTnt0c+jxD25JaikPU3d95J0iaEMBGbmmrRdo7o+vXr6PV6/Pz8TI77+flx+fLlbB9z+fLlbNunp6dz/fp1/P39szxm2rRpfPjhh5YLHPBycuKPJ5/kh6tXWXz5MpG3b3M3A5LwIIn71pxo7vtbCBtIycj5SyaRvZkzZ9K/f38GDBgAQGhoKBs3bmTu3LnZzgSYN28eFSpUIDQ0FFBnDezfv58ZM2ZYJGnTat0f+RxC2BtL/7+WpE0IYcLfxcWi7RzZ/dMCFUV54FTB7Npnd9xg7NixjBo1yng7ISGB8uXL5zVcI3edjtf8/XnN3x9FUbiWlkZcejpJej3pikJ6hp50/W3S9IlkZKSiKKlkKPp78Wbciz2D1PRUkvV3SdOno1fS0Weko1cyUJQMFCBDyQBFMa6dVDL9mRPlIfcLxxNc8jlbh1CgWGsmQG4U1elIfMDUTCEKInetZRdqStImhDAR4u1NOVdXLqakZPvx2LCmLcTb28qRFRy+vr7odLoso2pXr17NMppmULp06WzbOzk5UeLeHmr3c3V1xdXV1TJB50Cj0VDKxYVSWZJ0mRorREFkjZkAuZ26rdFoKKrT5eZlCOFwpFaPEMKETqNhVpUqQNZZb4bboVWqyH5tD+Di4kJwcDAREREmxyMiImjUqFG2j2nYsGGW9uHh4dSrV++Rv8UWQoj75edMgGnTpuHl5WW8WGIGgBCOTpI2IUQWXUqWZHXNmpS9bxSnnKurlPs306hRo1iwYAGLFi3i2LFjjBw5kpiYGGO1tbFjx9K3b19j+0GDBvHvv/8yatQojh07xqJFi1i4cCHvvPOOrV6CEKIQssZMgLFjx3Lr1i3j5fz585Z7AUI4KJkeKYTIVpeSJenk68vO+HhiU1Pxd3EhxNtbRtjM1L17d27cuMGkSZOIjY2lVq1ahIWFUbFiRQBiY2OJiYkxtq9cuTJhYWGMHDmSr776ijJlyvDFF19YZY82IYTjyDwT4IUXXjAej4iIoFOnTtk+pmHDhvz6668mxx40E8AaU7eFcDSyT1seyL4vQpiS94R9kH8HIUzJeyJ7K1eupE+fPsybN4+GDRvyzTffMH/+fKKjo6lYsSJjx47l4sWLLFu2DFBL/teqVYs33niDgQMHsnfvXgYNGsT3339v1hdL8u8ghCnZp00IIYQQQjyQzAQQouCRkbY8kG+MhDAl7wn7IP8OQpiS94R9kH8HIUzl5T0hhUiEEEIIIYQQwo5J0iaEEEIIIYQQdkySNiGEEEIIIYSwY1KIJA8MywATEhJsHIkQ9sHwXpAlsrYlfZMQpqRvsg/SNwlhKi99kyRteXD79m0Aypcvb+NIhLAvt2/fxsvLy9ZhOCzpm4TInvRNtiV9kxDZy03fJNUj8yAjI4NLly5RrFgxNDlsNJyQkED58uU5f/68XVRKsqd47CkWiccy8SiKwu3btylTpgxarcy6tpWC2DcVVvJzzn/SNxUcD+ub5P1iHfJzto786ptkpC0PtFot5cqVM6utp6enXb0x7Ckee4oFJJ6HeVg88i227RXkvqmwkp9z/pO+yf6Z2zfJ+8U65OdsHZbum+RrJyGEEEIIIYSwY5K0CSGEEEIIIYQdk6Qtn7i6uvL+++/j6upq61AA+4rHnmIBiedh7C0e8Wjk39M65Oec/+RnXHjIv6V1yM/ZOvLr5yyFSIQQQgghhBDCjslImxBCCCGEEELYMUnahBBCCCGEEMKOSdImhBBCCCGEEHZMkjYhhBBCCCGEsGOStOWDOXPmULlyZdzc3AgODmbnzp02i2XHjh107NiRMmXKoNFo+Pnnn20Wy7Rp06hfvz7FihWjVKlSdO7cmRMnTtgsnrlz5xIUFGTc/LBhw4b89ttvNosns2nTpqHRaBgxYoRNnv+DDz5Ao9GYXEqXLm2TWETu5bYP2r59O8HBwbi5ufHYY48xb948K0VacOXmZ7xt27Ys7yeNRsPx48etGHHBk5ffX/J/2T5Jn2Q90jflP1v1TZK0WdjKlSsZMWIE48ePJyoqipCQENq1a0dMTIxN4klKSqJ27dp8+eWXNnn+zLZv386bb77JH3/8QUREBOnp6bRu3ZqkpCSbxFOuXDk+/vhj9u/fz/79+2nRogWdOnUiOjraJvEY/PXXX3zzzTcEBQXZNI6aNWsSGxtrvBw+fNim8Qjz5LYPOnfuHO3btyckJISoqCjGjRvHsGHDWLNmjZUjLzjy2s+fOHHC5D1VtWpVK0VcMOX295f8X7ZP0idZj/RN1mGzvkkRFtWgQQNl0KBBJseqV6+ujBkzxkYR/QdQfvrpJ1uHYXT16lUFULZv327rUIx8fHyUBQsW2Oz5b9++rVStWlWJiIhQmjZtqgwfPtwmcbz//vtK7dq1bfLc4tHktg969913lerVq5sce+ONN5Snn34632Is6HL7M966dasCKHFxcVaIrnAy5/eX/F+2T9InWY/0TdZnzb5JRtosKDU1lcjISFq3bm1yvHXr1uzZs8dGUdmvW7duAVC8eHEbRwJ6vZ4ffviBpKQkGjZsaLM43nzzTTp06EDLli1tFoPBqVOnKFOmDJUrV6ZHjx6cPXvW1iGJh8hLH7R3794s7du0acP+/ftJS0vLt1gLqkfp5+vWrYu/vz/PPvssW7duzc8wHZL8X7Y/0idZj/RN9stS/6clabOg69evo9fr8fPzMznu5+fH5cuXbRSVfVIUhVGjRvHMM89Qq1Ytm8Vx+PBhPDw8cHV1ZdCgQfz0008EBATYJJYffviBv//+m2nTptnk+TN76qmnWLZsGRs3bmT+/PlcvnyZRo0acePGDVuHJh4gL33Q5cuXs22fnp7O9evX8y3WgiovP2N/f3+++eYb1qxZw9q1a6lWrRrPPvssO3bssEbIDkP+L9sf6ZOsR/om+2Wp/9NOlg5MgEajMbmtKEqWY45u6NChHDp0iF27dtk0jmrVqnHgwAHi4+NZs2YN/fr1Y/v27VZP3M6fP8/w4cMJDw/Hzc3Nqs+dnXbt2hmvBwYG0rBhQx5//HGWLl3KqFGjbBiZMEdu+6Ds2md3XPwnNz/jatWqUa1aNePthg0bcv78eWbMmEGTJk3yNU5HI/+X7ZP0SdYjfZN9ssT/aRlpsyBfX190Ol2WbzSuXr2aJcN2ZG+99Rbr1q1j69atlCtXzqaxuLi4UKVKFerVq8e0adOoXbs2s2bNsnockZGRXL16leDgYJycnHBycmL79u188cUXODk5odfrrR5TZkWLFiUwMJBTp07ZNA7xYHnpg0qXLp1teycnJ0qUKJFvsRZUlurnn376aXk/WZj8X7Y/0idZj/RN9stS/6clabMgFxcXgoODiYiIMDkeERFBo0aNbBSV/VAUhaFDh7J27Vq2bNlC5cqVbR1SFoqikJKSYvXnffbZZzl8+DAHDhwwXurVq0fv3r05cOAAOp3O6jFllpKSwrFjx/D397dpHOLB8tIHNWzYMEv78P+3d3chUbVrGMev5bwzZmIZln0RI4RoxaRGZNJAghYENZAQYVBGkRJY9AFh0UlnFZmCRAch0lkHhjVRgRGIESlF40xlUKAjCUJBgfZxUPnsk53s3u12dr46a83M/wfrYPCZ8X7G5T3rWj5r2dmp9evXy+12z1qtiWqm+nwoFOL3aYaxLzsPPSl+6E3ONWP79B/dtgQx3bhxw7jdbtPa2mr6+/vNsWPHTGZmpolGo7bUMzY2ZkKhkAmFQkaSuXz5sgmFQmZoaCjutRw+fNjMnz/fdHV1mZGRkYnt69evca/FGGNOnz5turu7zeDgoIlEIubMmTMmLS3NdHZ22lLP39l598iTJ0+arq4uMzAwYHp6esz27dtNVlaWbfsx/n+xelBDQ4PZu3fvxPiBgQEzd+5cc/z4cdPf329aW1uN2+027e3tdk3B8f70PW5qajIdHR3mzZs35uXLl6ahocFIMjdv3rRrCgkh1ucX+3JioCfFD70pPuzqTYS2WXDlyhXj9XqNx+Mx69ats/WW9r9u5/r3raamJu61TFaHJNPW1hb3Wowx5sCBAxM/p0WLFpmKigrHBDZj7A1tu3fvNkuXLjVut9ssW7bMVFVVmVevXtlSC/7cVD2opqbGbN68+bfxXV1dpqSkxHg8HpOXl2euXr0a54oTz5+8xxcuXDArV640c+bMMQsWLDB+v9/cvXvXhqoTS6zPL/blxEFPih960+yzqzdZxvz7SjgAAAAAgONwTRsAAAAAOBihDQAAAAAcjNAGAAAAAA5GaAMAAAAAByO0AQAAAICDEdoAAAAAwMEIbQAAAADgYIQ2AAAAAHAwQhsSWl1dnfbs2WN3GQAAAMCssYwxxu4igOn6+PGj0tPTlZmZaXcpAFLc/v37tWTJEp0/f97uUgBgAr0pORDaAAD4h8bHx7V48WIFg0GVlZXZXQ4ASKI3JROWRyJhRaNRWZaloaEhu0sBkEQCgYAsy5p0CwaDkz7n8ePHSktLU2lp6aRfLy8vV319verr65Wdna2cnBydPXtW/3netL29XT6fTxkZGcrJyVFlZaW+fPkyK3MEkHjoTantL7sLAKarr69P2dnZ8nq9dpcCIIm0tbXp+/fv+vz5s/Lz83Xv3j2VlJRIkhYuXDjpc4LBoHbs2KG0tP99LvT69es6ePCgent79ezZM9XW1srr9erQoUMaGRlRdXW1Ll68qJ07d2psbEyPHj0Si2EA/EJvSm2ENiSscDisoqIiu8sAkGRycnIkSU+ePJFlWfL7/crKypryOcFgUJcuXZpyzIoVK9TU1CTLslRQUKAXL16oqalp4sDox48fqqqqmjgR5fP5ZmZCAJICvSm1sTwSCauvr4/QBmDWRCIR5eXlxTwoev36tYaHh1VZWTnluI0bN8qyrInHZWVlevv2rX7+/KmioiJVVFTI5/Np165dunbtmj59+jQj8wCQXOhNqYnQhoQVDodVXFxsdxkAklQkEtHatWtjjgsGg9qyZYsyMjKm/b1cLpcePHig+/fva/Xq1WppaVFBQYEGBwen/ZoAkhO9KTUR2pCQRkdHFY1G+UsbgFkTjUZVUFAQc9zt27cVCARijuvp6fmvx/n5+XK5XJIky7K0adMmnTt3TqFQSB6PRx0dHdMrHkDSojelJq5pQ0IKh8NyuVxas2aN3aUASFLj4+MaGhrS8PCwli9f/tvyoV/ev3+vp0+f6tatWzFf7927dzpx4oTq6ur0/PlztbS0qLGxUZLU29urhw8fauvWrcrNzVVvb68+fPigVatWzfS0ACQ4elNqIrQhIYXDYRUWFio9Pd3uUgAkqaNHj6q2tlaFhYUaHR2d9MDozp07Ki0tVW5ubszX27dvn759+6YNGzbI5XLpyJEjqq2tlSTNmzdP3d3dam5u1ujoqLxerxobG7Vt27YZnxeAxEZvSk38c20AAKYpEAjI7/fr1KlTU44rLy9XcXGxmpub41MYgJRGb0o+XNMGAMA0+f1+VVdX210GAPyG3pR8WB4JAMA0xTqLDQB2oDclH5ZHAgAAAICDsTwSAAAAAByM0AYAAAAADkZoAwAAAAAHI7QBAAAAgIMR2gAAAADAwQhtAAAAAOBghDYAAAAAcDBCGwAAAAA4GKENAAAAAByM0AYAAAAADkZoAwAAAAAH+xeUPCdt0T6deAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 787.402x275.591 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(1, 3, figsize=(20*in2cm, 7*in2cm), facecolor='white')\n", "\n", "\n", "ax[0].plot(0, Qevals[0], 'ko');\n", "ax[0].plot(1, <PERSON><PERSON><PERSON>[1], 'bo');\n", "ax[0].plot(2, <PERSON><PERSON><PERSON>[2], 'ro');\n", "ax[0].plot(3, <PERSON><PERSON><PERSON>[3], 'go');\n", "ax[0].plot(4, <PERSON><PERSON><PERSON>[4], 'yo');\n", "ax[0].plot(5, <PERSON><PERSON><PERSON>[5], 'co');\n", "ax[0].set_xticks([0,1,2,3,4,5])\n", "ax[0].set_xlabel(r'$i$') \n", "ax[0].set_ylabel(r'$\\kappa_i$') \n", "ax[0].set_title('Eigenvalues $\\mathcal{Q}$')\n", "\n", "ax[1].plot(tau, Kevals[0,:], 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON>s[1,:], 'b', label = r'$\\lambda_1(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[2,:], 'r', label = r'$\\lambda_2(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[3,:], 'g', label = r'$\\lambda_3(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[4,:], 'y', label = r'$\\lambda_4(\\tau)$');\n", "ax[1].plot(tau, <PERSON><PERSON><PERSON>[5,:], 'c', label = r'$\\lambda_5(\\tau)$');\n", "ax[1].set_xlabel(r'$\\tau$ / ps') \n", "ax[1].set_ylabel(r'$\\lambda_i(\\tau)$') \n", "ax[1].set_title(r'Eigenvalues $\\mathcal{K}_{\\tau}$')\n", "ax[1].legend(loc='upper right', fontsize=9)\n", "\n", "#ax[2].plot(tau, - tau / np.log(Kevals[0,:]), 'k', label = r'$\\lambda_0(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(Kevals[1,:]), 'b', label = r'$t_1(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON>s[2,:]), 'r', label = r'$t_2(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[3,:]), 'g', label = r'$t_3(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[4,:]), 'y', label = r'$t_4(\\tau)$');\n", "ax[2].plot(tau, - tau / np.log(<PERSON><PERSON><PERSON>[5,:]), 'c', label = r'$t_5(\\tau)$');\n", "ax[2].set_xlabel(r'$\\tau$ / ps') \n", "ax[2].set_ylabel(r'$t_i(\\tau)$') \n", "ax[2].set_title(r'Timescales $t_i(\\tau)$')\n", "ax[2].legend(loc='upper right',fontsize=9)\n", "\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.5, hspace=0.8)\n", "#fig.savefig('evals.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 629.921x629.921 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax) = plt.subplots(6, 2, figsize=(16*in2cm, 16*in2cm), facecolor='white')\n", "\n", "\n", "####################################################################################################\n", "y1 = np.zeros(x.shape)\n", "y2 = np.real(Qevecs[:,0])\n", "phi0 = y2 / np.sum( y2*dx)\n", "ax[0,0].plot(x, phi0, 'k-', label= r'$\\varphi_0$');\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,0].fill_between(x, y1, phi0, where=phi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,0].set_title('Eigenfunctions $\\mathcal{Q}$')\n", "ax[0,0].set_ylim(0,1.3)\n", "ax[0,0].legend(loc='upper left')\n", "ax[0,0].set_xticks([])\n", "\n", "y2 = np.real(Kevecs[:,0])\n", "psi0 = y2 / y2\n", "ax[0,1].plot(x, psi0 , 'k-', label= r'$\\psi_0$');\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 >= y1, facecolor='red', interpolate=True)\n", "ax[0,1].fill_between(x, y1, psi0, where=psi0 < y1, facecolor='blue', interpolate=True)\n", "ax[0,1].set_ylim(0,1.3)\n", "ax[0,1].set_title(r'Eigenfunctions $\\mathcal{K}_{\\tau}$')\n", "ax[0,1].legend(loc='upper left')\n", "ax[0,1].set_xticks([])\n", "\n", "####################################################################################################\n", "phi1 = np.real(Qevecs[:,1])\n", "ax[1,0].plot(x, phi1 , 'k-', label= r'$\\varphi_1$');\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,0].fill_between(x, y1, phi1, where=phi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,0].legend(loc='upper left')\n", "ax[1,0].set_xticks([])\n", "ax[1,0].set_ylim(-0.4,0.4)\n", "\n", "psi1 =  np.real(-Kevecs[:,1])\n", "ax[1,1].plot(x, psi1 , 'k-', label= r'$\\psi_1$');\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 >= y1, facecolor='red', interpolate=True)\n", "ax[1,1].fill_between(x, y1, psi1, where=psi1 < y1, facecolor='blue', interpolate=True)\n", "ax[1,1].legend(loc='upper left')\n", "ax[1,1].set_xticks([])\n", "ax[1,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi2 = -np.real(Qevecs[:,2])\n", "ax[2,0].plot(x, phi2 , 'k-', label= r'$\\varphi_2$');\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,0].fill_between(x, y1, phi2, where=phi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,0].legend(loc='upper left')\n", "ax[2,0].set_xticks([])\n", "ax[2,0].set_ylim(-0.4,0.4)\n", "\n", "psi2 = np.real(Kevecs[:,2])\n", "ax[2,1].plot(x, psi2 , 'k-', label= r'$\\psi_2$');\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 >= y1, facecolor='red', interpolate=True)\n", "ax[2,1].fill_between(x, y1, psi2, where=psi2 < y1, facecolor='blue', interpolate=True)\n", "ax[2,1].legend(loc='upper left')\n", "ax[2,1].set_xticks([])\n", "ax[2,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi3 = np.real(Qevecs[:,3])\n", "ax[3,0].plot(x, phi3 , 'k-', label= r'$\\varphi_3$');\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,0].fill_between(x, y1, phi3, where=phi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,0].legend(loc='upper left')\n", "ax[3,0].set_xticks([])\n", "ax[3,0].set_ylim(-0.4,0.4)\n", "\n", "psi3 = np.real(Kevecs[:,3])\n", "ax[3,1].plot(x, psi3 , 'k-', label= r'$\\psi_3$');\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 >= y1, facecolor='red', interpolate=True)\n", "ax[3,1].fill_between(x, y1, psi3, where=psi3 < y1, facecolor='blue', interpolate=True)\n", "ax[3,1].legend(loc='upper left')\n", "ax[3,1].set_xticks([])\n", "ax[3,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi4 = np.real(Qevecs[:,4])\n", "ax[4,0].plot(x, phi4 , 'k-', label= r'$\\varphi_4$');\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,0].fill_between(x, y1, phi4, where=phi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,0].legend(loc='upper left')\n", "ax[4,0].set_xticks([])\n", "ax[4,0].set_ylim(-0.4,0.4)\n", "\n", "psi4 = - np.real(Kevecs[:,4])\n", "ax[4,1].plot(x, psi4 , 'k-', label= r'$\\psi_4$');\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 >= y1, facecolor='red', interpolate=True)\n", "ax[4,1].fill_between(x, y1, psi4, where=psi4 < y1, facecolor='blue', interpolate=True)\n", "ax[4,1].legend(loc='upper left')\n", "ax[4,1].set_xticks([])\n", "ax[4,1].set_ylim(-0.4,0.4)\n", "\n", "####################################################################################################\n", "phi5 = np.real(Qevecs[:,5])\n", "ax[5,0].plot(x, phi5 , 'k-', label= r'$\\varphi_5$');\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,0].fill_between(x, y1, phi5, where=phi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,0].legend(loc='upper left')\n", "ax[5,0].set_xticks([])\n", "ax[5,0].set_ylim(-0.4,0.4)\n", "ax[5,0].set_xlabel(r'$x$ / nm')\n", "\n", "psi5 = - np.real(Kevecs[:,5])\n", "\n", "ax[5,1].plot(x, psi5 , 'k-', label= r'$\\psi_5$');\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 >= y1, facecolor='red', interpolate=True)\n", "ax[5,1].fill_between(x, y1, psi5, where=psi5 < y1, facecolor='blue', interpolate=True)\n", "ax[5,1].legend(loc='upper left')\n", "ax[5,1].set_xticks([])\n", "ax[5,1].set_ylim(-0.4,0.4)\n", "ax[5,1].set_xlabel(r'$x$ / nm')\n", "\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.1)\n", "#fig.savefig('evecs.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0.18309931553250794+0j)\n", "(0.18309931575056493+0j)\n", "(0.18309932077730418+0j)\n"]}, {"data": {"text/plain": ["(0.0, 0.6)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 944.882x708.661 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tt = 1\n", "\n", "# initial distribution\n", "pi = np.abs(Qevecs[:,0]) \n", "\n", "rho0 = np.zeros(xbins)\n", "rho0[23] = 1\n", "rho0 = rho0 / np.sum(rho0 * dx)\n", "\n", "# 6 == Number of eigenfunctions\n", "c = np.zeros(6)\n", "c[0] = np.dot(rho0, pi * pi) \n", "c[1] = np.dot(rho0, phi1 * pi) \n", "c[2] = np.dot(rho0, phi2 * pi) \n", "c[3] = np.dot(rho0, phi3 * pi) \n", "c[4] = np.dot(rho0, phi4 * pi) \n", "c[5] = np.dot(rho0, phi5 * pi)\n", "\n", "\n", "tt = np.array([0.1, 0.5, 20.0])\n", "\n", "fig, (ax1) = plt.subplots(1, 1, figsize=(24*in2cm, 18*in2cm), facecolor='white')\n", "\n", "ax1.plot([-1.21,-1.21], [0,1], 'k')\n", "\n", "for i in tt:\n", "\n", "    tt  = i\n", "    rho = \\\n", "    np.exp(tt * Qevals[0]) * pi * c[0] + \\\n", "    np.exp(tt * Qevals[1]) * phi1 * c[1] + \\\n", "    np.exp(tt * Qevals[2]) * phi2 * c[2] + \\\n", "    np.exp(tt * Qevals[3]) * phi3 * c[3] + \\\n", "    np.exp(tt * Qevals[4]) * phi4 * c[4] + \\\n", "    np.exp(tt * <PERSON>evals[5]) * phi5 * c[5]\n", "\n", "    ax1.plot(x, rho, linewidth=2)\n", "    print(np.sum(rho*dx))\n", "    \n", "ax1.set_ylim(0,0.6)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 944.882x708.661 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tt = 1\n", "\n", "# initial distribution\n", "pi = np.ones(xbins) #np.abs(Qevecs[:,1])\n", "\n", "f0 = np.exp(-(x - -1.2)**2 / 0.1)\n", "f0 = f0 / np.sum(f0 * dx)\n", "\n", "# 6 == Number of eigenfunctions\n", "d = np.zeros(6)\n", "d[0] = np.dot(f0, pi ) \n", "d[1] = np.dot(f0, psi1 ) \n", "d[2] = np.dot(f0, psi2 ) \n", "d[3] = np.dot(f0, psi3 ) \n", "d[4] = np.dot(f0, psi4 ) \n", "d[5] = np.dot(f0, psi5 )\n", "\n", "\n", "tt = np.array([0.1, 0.5, 20.0])\n", "\n", "fig, (ax1) = plt.subplots(1, 1, figsize=(24*in2cm, 18*in2cm), facecolor='white')\n", "\n", "#ax1.plot([-1.21,-1.21], [0,1], 'k')\n", "for i in tt:\n", "\n", "    tt  = i\n", "    ft = \\\n", "    np.exp(tt * Qevals[0]) * pi * d[0] + \\\n", "    np.exp(tt * Qevals[1]) * psi1 * d[1] + \\\n", "    np.exp(tt * Qevals[2]) * psi2 * d[2] + \\\n", "    np.exp(tt * Qevals[3]) * psi3 * d[3] + \\\n", "    np.exp(tt * Qevals[4]) * psi4 * d[4] + \\\n", "    np.exp(tt * Qevals[5]) * psi5 * d[5]\n", "\n", "    ax1.plot(x, ft, linewidth=2)\n", "    \n", "#ax1.set_ylim(0,0.6)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}