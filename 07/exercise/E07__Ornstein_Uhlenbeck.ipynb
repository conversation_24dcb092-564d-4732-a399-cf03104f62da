{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1dcb0594", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import sympy as sp\n", "import matplotlib.pyplot as plt\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "id": "00027977-dcc6-4561-b4db-7a80e6abf2a3", "metadata": {}, "source": ["## The Ornstein-Uhlenbeck process\n", "\n", "\\begin{eqnarray}\n", "    dx_t= - k (x_t - x_e)\\,dt+\\sigma \\,dW_t \\, \n", "\\end{eqnarray}"]}, {"cell_type": "code", "execution_count": 2, "id": "1cbd843c-0664-4035-901a-937df000a030", "metadata": {}, "outputs": [], "source": ["# Ornstein-<PERSON><PERSON><PERSON> parameters\n", "k     = 1\n", "xe    = 3\n", "sigma = 1\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1fd6e460-5f0c-46ed-abda-8ddf871b9097", "metadata": {}, "outputs": [], "source": ["# Grid\n", "xbins     = 100   \n", "xmin      = - 2\n", "xmax      = - xmin\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins     = xbins - 1\n"]}, {"cell_type": "code", "execution_count": 4, "id": "342e3839", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 907.087x453.543 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Integration step\n", "dt  = 0.001\n", "sdt = np.sqrt(dt)\n", "\n", "# Number of steps\n", "N  = 1000\n", "\n", "# Time intervals\n", "t  = np.linspace(0, N-1, N) * dt\n", "\n", "x0 = 3\n", "x = np.zeros(N)\n", "x[0] = x0\n", "\n", "y = np.zeros(N)\n", "y[0] = x0\n", "\n", "# Generate random numbers\n", "eta    = np.random.normal(0,1,N)\n", "\n", "for i in range(N-1):\n", "\n", "    # Euler-Maruyama scheme\n", "    x[i+1]  = x[i] - k * ( x[i] - xe ) * dt + sigma * eta[i] * sdt\n", "\n", "    # Analytical solution\n", "    y[i+1]  = xe + np.exp( - k * t[i] ) * ( x0 - xe ) + sigma * np.sum( sdt * eta[0:i+1] * np.exp( - k * ( t[i] - t[0:i+1] ) ) )\n", "\n", "fig, ax = plt.subplots(1, 1,figsize=(32*in2cm, 16*in2cm))  \n", "\n", "ax.plot(t, x, 'r',   label='Numerics', linewidth = 2)\n", "ax.plot(t, y, 'k--', label='Analytical solution', linewidth = 1)\n", "ax.set_xlabel('t')\n", "ax.set_ylabel('x(t)')\n", "ax.set_ylim(x0-0.5, x0+0.5)\n", "ax.legend();"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}