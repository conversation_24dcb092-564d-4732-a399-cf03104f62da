{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1f845db6", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "# import sympy as sp\n", "# from tqdm import tqdm\n", "\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "2b6a7c43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4009078317304838\n"]}], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "mass  = 1           # amu\n", "beta  = 1 / kB / T\n", "print(beta)"]}, {"cell_type": "code", "execution_count": 4, "id": "f2dfde40", "metadata": {}, "outputs": [], "source": ["# # Initialize the variables x and y for symbolic calculation\n", "# x   =  sp.symbols('x')\n", "# # Define the potentials Va, Vb and V with sympy\n", "# V   = ( x ** 2 - 1 ) ** 2\n", "# # Calculate derivative and second derivative with sympy\n", "# gradVx   =  V.diff(x)\n", "# grad2Vx2 =  gradVx.diff(x)\n", "# # To display sympy functions:\n", "# # display(gradVx)\n", "# # Convert potential and derivatives in numpy functions\n", "# V         =  sp.lambdify((x), V, modules=['numpy'])\n", "# gradVx    =  sp.lambdify((x), gradVx, modules=['numpy'])\n", "# grad2Vx2  =  sp.lambdify((x), grad2Vx2, modules=['numpy'])\n", "\n", "V = lambda x: (x**2 - 1)**2\n", "gradVx = lambda x: 4*x*(x**2 - 1)\n", "grad2Vx2 = lambda x: 12*x**2 - 4"]}, {"cell_type": "code", "execution_count": 5, "id": "51d0d778", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.9959919839679359\n", "0.9959919839679359\n", "-2.220446049250313e-16\n"]}], "source": ["# Grid\n", "xbins     = 500   \n", "xmin      = - 3.5\n", "xmax      = - xmin\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges[:-1] + (dx / 2)                # array with x centers\n", "xbins     = xbins - 1\n", "\n", "iA        = np.argmin(V(xcenters[0:int(xbins/2)]))\n", "iC        = int(xbins/2) + np.argmin(V(xcenters[int(xbins/2):-1]))\n", "iB        = iA   + np.argmax(V(xcenters[iA:iC]))\n", "\n", "print(xcenters[iA])\n", "print(xcenters[iC])\n", "print(xcenters[iB])\n", "\n", "# Approximation at xA and xB\n", "xA        = xcenters[iA]\n", "omegaA    = np.sqrt( np.abs(grad2Vx2(xA)) / mass )\n", "kspringA  = omegaA ** 2 * mass\n", "\n", "xB        = xcenters[iB]\n", "omegaB    = np.sqrt( np.abs(grad2Vx2(xB)) / mass )\n", "kspringB  = omegaB ** 2 * mass\n", "\n", "xC        = xcenters[iC]\n", "omegaC    = np.sqrt( np.abs(grad2Vx2(xC)) / mass )\n", "kspringC  = omegaC ** 2 * mass\n", "\n", "# Energy barriers\n", "Eb_plus   = V(xB) - V(xA)\n", "Eb_minus  = V(xB) - V(xC)"]}, {"cell_type": "markdown", "id": "d08cf2bb", "metadata": {}, "source": ["The next block defines the BBK integrator for Langevin dynamics."]}, {"cell_type": "code", "execution_count": 6, "id": "0f1e04a7", "metadata": {}, "outputs": [], "source": ["def langevin_bbk_step(Q, P, M, gamma, beta, der_V, dt):\n", "  L = 1 / (1 + 0.5 * gamma*dt)\n", "  \n", "  # Deterministic force\n", "  F  =  - der_V(Q)\n", "  \n", "  # Random force \n", "  R  =  np.random.normal(0, 1, size = np.array(Q).shape)\n", "  \n", "  # update p_{n+1/2}\n", "  Phalf = ( 1 - 0.5 * gamma * dt ) * P + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R\n", "  \n", "  # update q_{n+1}\n", "  Q  =  Q + Phalf / M * dt\n", "  \n", "  # Recalculate deterministic force (but not the random force)\n", "  F  =  - der_V(Q)\n", "  \n", "  # update p_{n+1}\n", "  P = ( Phalf + 0.5 * F * dt + 0.5 * np.sqrt( 2 / beta * dt * gamma * M ) * R ) / ( 1 + 0.5 * gamma * dt ) \n", "\n", "  return Q, P"]}, {"cell_type": "code", "execution_count": 7, "id": "781ac34d", "metadata": {}, "outputs": [], "source": ["# Number of gamma values being test\n", "Ng     = 30 #25\n", "\n", "# Generate gamma values between 0.05 and 10\n", "# Use logspace to generate more points close to 0.05 than 10\n", "gammas =  np.logspace(np.log(0.01), np.log(25), Ng, base=np.exp(1))"]}, {"cell_type": "markdown", "id": "6f6455ef", "metadata": {}, "source": ["### Numerical experiment"]}, {"cell_type": "code", "execution_count": 8, "id": "68140f57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gamma:  0.010000000000000007\n", "Gamma:  0.013096955295775894\n", "Gamma:  0.017153023801955224\n", "Gamma:  0.02246523859215873\n", "Gamma:  0.029422622555044218\n", "Gamma:  0.03853467722879015\n", "Gamma:  0.05046869450026176\n", "Gamma:  0.06609862357060987\n", "Gamma:  0.08656907180165961\n", "Gamma:  0.11337912633831491\n", "Gamma:  0.14849213491270372\n", "Gamma:  0.1944794852726002\n", "Gamma:  0.254708912456075\n", "Gamma:  0.33359112398729096\n", "Gamma:  0.436902803792918\n", "Gamma:  0.5722096489874995\n", "Gamma:  0.7494204192600892\n", "Gamma:  0.9815125728791008\n", "Gamma:  1.2854826289239565\n", "Gamma:  1.6835908524513516\n", "Gamma:  2.2049914130932584\n", "Gamma:  2.8878673964852104\n", "Gamma:  3.7822270191895493\n", "Gamma:  4.953565818880125\n", "Gamma:  6.4876630084556455\n", "Gamma:  8.496863239580248\n", "Gamma:  11.128303800310405\n", "Gamma:  14.574689739047827\n", "Gamma:  19.08840599621129\n", "Gamma:  24.99999999999999\n"]}], "source": ["# Number of simulations\n", "Nreps = 500\n", "\n", "# Integrator timestep\n", "dt  =  0.005 # ps\n", "sdt =  np.sqrt(dt)\n", "\n", "# Array to store simulation time for each replica and each gamma value\n", "simTimes = np.zeros((<PERSON><PERSON><PERSON>,<PERSON>))\n", "\n", "# For loop over gamma values\n", "for g, gamma in enumerate(gammas):\n", "  print(\"Gamma: \", str(gamma))\n", "  # Recalculate diffusion constant\n", "  D     = kB * T / mass / gamma # nm2 ps-1\n", "  sigma = np.sqrt(2 * D)\n", "\n", "  for r in range(Nreps):\n", "    # Initial position\n", "    x0  =  xA \n", "    # Final position\n", "    xF  =  xC\n", "    # Initial momentum drawn from the <PERSON><PERSON><PERSON> distribution\n", "    p0  =  np.random.normal(0, 1) * np.sqrt( mass / beta )\n", "    # Initialize position and velocity\n", "    x   =   x0\n", "    v   =   p0 / mass\n", "    t = 0\n", "    # Start the simulation and stop if x > xC\n", "    while x < xF:\n", "      # Update position and momentum\n", "      x, p = langevin_bbk_step(x, mass * v, mass, gamma, beta, gradVx, dt)\n", "      # Calculate velocity\n", "      v    = p / mass\n", "      # Update simulation time\n", "      t    = t + 1\n", "    # Store simulation time to go from xA to xB\n", "    simTimes[r,g] = t * dt\n", "\n", "# MFPT from experiment\n", "MFPT = np.mean(simTimes, axis=0)\n", "\n", "# Rate from experiment\n", "expRate = 1 / MFPT    "]}, {"cell_type": "markdown", "id": "3deae671-b8da-4363-9c7b-068a4bff253a", "metadata": {}, "source": ["## Pontryagin formula"]}, {"cell_type": "code", "execution_count": 9, "id": "c925d363-cd7b-4533-8d92-e91bc897feec", "metadata": {}, "outputs": [], "source": ["def pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters ):\n", "  D = 1 / beta / mass / gamma\n", "  \n", "  i0 = np.argmin(np.abs(xcenters - x0)) + 1\n", "  xcenters0 = xcenters[0:i0]\n", "  \n", "  iF = np.argmin(np.abs(xcenters - xF)) + 1\n", "  xcentersF = xcenters[0:iF]\n", "  \n", "  nn = iF - i0\n", "  innerInt = np.zeros(nn+1)\n", "  \n", "  for i in range(nn):\n", "    x = xcenters[0:iA+i+1]\n", "    innerInt[i+1] = np.sum(dx * np.exp( - beta * V(x) ) )\n", "\n", "  x        = xcenters[iA:iA+i+2]\n", "  outerInt = sum( dx * np.exp(beta * V(x)) * innerInt )\n", "\n", "  mfpt = outerInt / D\n", "\n", "  return mfpt\n", "\n", "exactRates = np.zeros(Ng)\n", "\n", "for g, gamma in enumerate(gammas):\n", "  exactRates[g] = 1 / pontryagin_mfpt( gamma, beta, V, x0, xF, xcenters )"]}, {"cell_type": "code", "execution_count": 10, "id": "22623f67-8259-477e-9cec-3b096357ef13", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 4), facecolor='white')  \n", "\n", "ax1.plot(xcenters, V(xcenters), 'k', label = 'Potential') \n", "#ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax1.plot(xA, V(xA), 'go', label = 'Reactant') \n", "#ax1.plot(xcenters, - 0.5 * kspringB * (xcenters - xB) ** 2 + V(xB), 'k--', linewidth = 0.5)\n", "ax1.plot(xB, V(xB), 'ro', label = 'Barrier') \n", "#ax1.plot(xcenters,   0.5 * kspringC * (xcenters - xC) ** 2 + V(xC), 'k--', linewidth = 0.5 ) \n", "ax1.plot(xC, V(xC), 'bo' , label = 'Product') \n", "ax1.set_title('Potential')\n", "ax1.set_xlabel(r'$x$')\n", "ax1.set_ylabel(r'$V(x)$')\n", "ax1.legend()\n", "ax1.set_ylim((-0, 2.5))\n", "ax1.set_xlim((xmin, xmax))\n", "\n", "ax2.plot(gammas, expRate, 'ro', label = 'Numerics') \n", "#ax1.plot(xcenters,   0.5 * kspringA * (xcenters - xA) ** 2 + V(xA), 'k--', linewidth = 0.5, label = 'Harmonic approximation') \n", "ax2.plot(gammas, exactRates, 'k--', label = 'Exact') \n", "ax2.set_title('Rates')\n", "ax2.set_xlabel(r'$\\gamma$ / ps $^{-1}$')\n", "ax2.set_ylabel(r'$k$ / ps$^{-1}$')\n", "ax2.legend()\n", "ax2.set_xlim((0, gammas[-1]))\n", "ax2.set_ylim((0, 0.3))\n", "\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.2, hspace=0.4);\n", "# fig.savefig('figures/potential.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}