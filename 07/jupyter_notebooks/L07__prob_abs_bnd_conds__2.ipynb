{"cells": [{"cell_type": "code", "execution_count": 1, "id": "259d2304", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "# from sympy import *\n", "# import matplotlib.cm as cm\n", "# from IPython.display import display\n", "# from tqdm import tqdm\n", "\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "id": "3247490b-6031-4631-acc8-9b3605f6c1df", "metadata": {}, "source": ["## Brownian motion"]}, {"cell_type": "code", "execution_count": 2, "id": "f0004bf6-6c24-484b-8a4b-4b2e0fb44b1b", "metadata": {}, "outputs": [], "source": ["def pdf( D, x,t,x0,t0 ):\n", "  return 1 / np.sqrt(4 *D* np.pi * ( t - t0 ) ) * np.exp( - 0.25 * ( x - x0 ) ** 2 / D / (t - t0) )\n", "\n", "def pdf_abs( D, xF, x, t, x0, t0 ):\n", "  dx = x[1] - x[0]\n", "  Pa       = pdf( D, x, t, x0, t0 ) - pdf( D, 2*xF - x, t, x0, t0 )\n", "  Pa[x>xF] = 0\n", "  Pa  = Pa / np.sum(Pa*dx)\n", "  return Pa\n", "\n", "def phi_func( D, xF, xcenters, t, x0, t0 ):\n", "  f = np.argmin(np.abs(xcenters - xF)) + 1\n", "  xcentersF = xcenters[0:f]\n", "  dx = xcentersF[1] - xcentersF[0]\n", "  Nsteps = len(t)\n", "  phi = np.zeros(Nsteps)\n", "  for k in range(Nsteps):\n", "    if k == 0:\n", "      phi[k] = 1\n", "    else:\n", "      Pa       = pdf( D, xcenters, t[k], x0, t0 ) - pdf( D, 2*xF - xcenters, t[k], x0, t0 )\n", "      Pa[xcenters>xF] = 0\n", "      phi[k] = np.sum([ dx * Pa ])\n", "  return phi"]}, {"cell_type": "code", "execution_count": 3, "id": "49cd3951-d76e-456b-911e-a987932b3c01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1501\n"]}], "source": ["# Grid\n", "xbins     =  100\n", "xmin      = - 5\n", "xmax      = - xmin\n", "\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges + 0.5* dx\n", "xcenters  = np.delete(xcenters,-1)\n", "xbins     = len(xcenters)\n", "\n", "t0      = 0\n", "tmax    = 1.5\n", "dt      = 0.001\n", "\n", "t       = np.arange(t0, tmax+dt, dt)\n", "Nsteps  = len(t)\n", "print(Nsteps)\n", "\n", "# Fraction of trajectories in A = {x | x < xF}\n", "\n", "Nreps  = 10000\n", "x0     = 0\n", "x      = np.zeros((<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>))\n", "x[0,:] = x0\n", "\n", "D      = 1.0\n", "sigma  = np.sqrt(2*D)\n", "sdt    = np.sqrt(dt)\n", "\n", "# final point\n", "xF        = 1.8\n", "arg_xF    = np.argmin(np.abs(xcenters - xF))\n", "\n", "# First passage time of each replica\n", "x_cross_xF       = np.zeros((Nsteps, Nreps))\n", "\n", "# array of x values from xmin to xF --> to be used with phi_func()\n", "xcentersF = xcenters[0:arg_xF]\n", "\n", "# Fraction of trajectories hat did not reach xF at time t\n", "Phi       = np.zeros(Nsteps)\n", "Phi[0]    = np.sum( x[0,:] < xF ) / Nreps\n", "\n", "#while np.sum(fpt==0)>0:\n", "for k in range(Nsteps-1):\n", "    eta      = np.random.normal(0, 1, Nreps)\n", "\n", "    x[k+1,:] = x[k,:] + sigma * eta * sdt\n", "\n", "    x_cross_xF[k+1,:] = x_cross_xF[k,:]\n", "    x_cross_xF[k+1, ( x[k+1,:] >= xF ) & ( x_cross_xF[k+1,:] == 0 ) ] = 1\n", "\n", "    Phi[k+1] = np.sum( (x[k+1,:] < xF) & ( x_cross_xF[k+1,:] == 0 ) ) / Nreps\n", "\n", "# Exact value\n", "P      = pdf( D, xcenters, tmax, x0, t0 )\n", "Pa     = pdf_abs( D, xF, xcenters, tmax, x0, t0 )\n", "Phi_ex = phi_func( D, xF, xcenters, t, x0, t0 )\n", "\n", "# Histogram\n", "xcf   = x_cross_xF[-1,:]\n", "H, _  = np.histogram(x[-1,:], xedges, density=True)\n", "Ha, _ = np.histogram(x[-1,xcf==0], xedges, density=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "6862f774-4e61-49d8-aab6-e033dbbc1b02", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1, ax2, ax3, ax4) = plt.subplots(1, 4, figsize=(12, 4), facecolor='white')  \n", "\n", "xx  = x\n", "xxf = x[:,xcf==0]\n", "\n", "ax1.plot(t, xx[:,0:: 50], 'b-', linewidth = 0.5);\n", "ax1.plot(t, xxf[:,0::50], 'r-', linewidth = 0.5);\n", "\n", "ax1.plot(t0, x0, 'go', markersize = 5);\n", "ax1.plot(t, xF * np.ones(t.shape), 'r--', linewidth = 1);\n", "ax1.set_xlabel(r'$t$ / ps')\n", "ax1.set_ylabel(r'$x$ / nm')\n", "ax1.text(10*dt, xF + 0.1, r'$x_F$', fontsize = 12)\n", "ax1.set_xlim(t0,tmax)\n", "ax1.set_ylim(xmin, xmax)\n", "ax1.set_title('Trajectories')\n", "\n", "ax2.plot(xcenters, H, 'b',      label = 'Numerics')\n", "ax2.plot(xcenters, P, 'k--', label = 'Exact')\n", "ax2.set_xlabel(r'$x$ / nm')\n", "ax2.set_ylabel(r'$P(x,t|x_0,t_0)$')\n", "ax2.set_xlim(xmin, xmax)\n", "ax2.set_ylim(0,0.8)\n", "ax2.legend()\n", "ax2.set_title(r'PDF')\n", "\n", "ax3.plot(xcenters, Ha, 'r',      label = 'Numerics')\n", "ax3.plot(xcenters, Pa, 'k--', label = 'Exact')\n", "ax3.set_xlabel(r'$x$ / nm')\n", "ax3.set_ylabel(r'$P_a(x,t|x_0,t_0)$')\n", "ax3.set_xlim(xmin, xmax)\n", "ax3.set_ylim(0,0.8)\n", "ax3.legend()\n", "ax3.set_title(r'PDF with abs. bnd. cond.')\n", "\n", "ax4.plot(t, Phi, 'r',      label   = 'Numerics')\n", "ax4.plot(t, Phi_ex, 'k--',      label = 'Exact')\n", "ax4.set_xlabel(r'$t$ / ps')\n", "ax4.set_ylabel(r'$\\phi(x_F,t|x_0)$')\n", "ax4.set_xlim(t0, tmax)\n", "ax4.set_ylim(0,1.05)\n", "ax4.legend()\n", "ax4.set_title(r'Trajs with $x_t < x_F$ at time $t$')\n", "\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.5);\n", "#fig.savefig('figures/potential.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "markdown", "id": "8285160c-a338-45c8-b801-c8918cebec97", "metadata": {}, "source": ["## Double well potential"]}, {"cell_type": "code", "execution_count": 6, "id": "a5fa218a-91ed-48f7-b9a4-04e6a9d1ced3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10001\n"]}], "source": ["# Grid\n", "xbins     =  100\n", "xmin      = - 3\n", "xmax      = - xmin\n", "\n", "xedges    = np.linspace(xmin, xmax, xbins)  # array with x edges\n", "dx        = xedges[1] - xedges[0]\n", "xcenters  = xedges + 0.5* dx\n", "xcenters  = np.delete(xcenters,-1)\n", "xbins     = len(xcenters)\n", "\n", "def gradVx(x):\n", "  return 4 * x * ( x**2 - 1 )\n", "\n", "t0      = 0\n", "tmax    = 10\n", "dt      = 0.001\n", "\n", "t       = np.arange(t0, tmax+dt, dt)\n", "Nsteps  = len(t)\n", "print(Nsteps)\n", "\n", "Nreps  = 50000\n", "x0     = 0\n", "x      = np.zeros((<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>))\n", "x[0,:] = x0\n", "\n", "D      = 1.0\n", "sigma  = np.sqrt(2*D)\n", "sdt    = np.sqrt(dt)\n", "\n", "# final point\n", "xF        = 1.0\n", "arg_xF    = np.argmin(np.abs(xcenters - xF))\n", "\n", "# First passage time of each replica\n", "x_cross_xF       = np.zeros((Nsteps, Nreps))\n", "\n", "# array of x values from xmin to xF --> to be used with phi_func()\n", "xcentersF = xcenters[0:arg_xF]\n", "\n", "# Fraction of trajectories hat did not reach xF at time t\n", "Phi       = np.zeros(Nsteps)\n", "Phi[0]    = np.sum( x[0,:] < xF ) / Nreps\n", "\n", "#while np.sum(fpt==0)>0:\n", "for k in range(Nsteps-1):\n", "  F      = - gradVx(x[k,:])\n", "  \n", "  eta    = np.random.normal(0, 1, Nreps)\n", "\n", "  x[k+1,:] = x[k,:] + D * F * dt + sigma * eta * sdt\n", "\n", "  x_cross_xF[k+1,:] = x_cross_xF[k,:]\n", "  x_cross_xF[k+1, ( x[k+1,:] >= xF ) & ( x_cross_xF[k+1,:] == 0 ) ] = 1\n", "  \n", "  Phi[k+1] = np.sum( (x[k+1,:] < xF) & ( x_cross_xF[k+1,:] == 0 ) ) / Nreps\n", "\n", "# Histogram\n", "xcf   = x_cross_xF[-1,:]\n", "H, _  = np.histogram(x[-1,:], xedges, density=True)\n", "Ha, _ = np.histogram(x[-1,xcf==0], xedges, density=True)"]}, {"cell_type": "code", "execution_count": 7, "id": "56668804-1765-4085-aec2-6059fac73d20", "metadata": {}, "outputs": [{"data": {"image/png": "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******************************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", "text/plain": ["<Figure size 1200x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, (ax1, ax2, ax3, ax4) = plt.subplots(1, 4, figsize=(12, 4), facecolor='white')  \n", "\n", "xx  = x\n", "xxf = x[:,xcf==0]\n", "\n", "ax1.plot(t, xx[:,0:: 50], 'b-', linewidth = 0.5);\n", "ax1.plot(t, xxf[:,0::50], 'r-', linewidth = 0.5);\n", "ax1.plot(t0, x0, 'go', markersize = 5);\n", "ax1.plot(t, xF * np.ones(t.shape), 'r--', linewidth = 1);\n", "ax1.set_xlabel(r'$t$ / ps')\n", "ax1.set_ylabel(r'$x$ / nm')\n", "ax1.text(10*dt, xF + 0.1, r'$x_F$', fontsize = 12)\n", "ax1.set_xlim(t0,tmax)\n", "ax1.set_ylim(xmin, xmax)\n", "ax1.set_title('Trajectories')\n", "\n", "ax2.plot(xcenters, H, 'b',      label = 'Numerics')\n", "ax2.set_xlabel(r'$x$ / nm')\n", "ax2.set_ylabel(r'$P(x,t|x_0,t_0)$')\n", "ax2.set_xlim(xmin, xmax)\n", "ax2.set_ylim(0,0.8)\n", "ax2.legend()\n", "ax2.set_title(r'PDF')\n", "\n", "ax3.plot(xcenters, Ha, 'r',      label = 'Numerics')\n", "ax3.set_xlabel(r'$x$ / nm')\n", "ax3.set_ylabel(r'$P_a(x,t|x_0,t_0)$')\n", "ax3.set_xlim(xmin, xmax)\n", "ax3.set_ylim(0,1.2)\n", "ax3.legend()\n", "ax3.set_title(r'PDF with abs. bnd. cond.')\n", "\n", "ax4.plot(t, Phi, 'r',      label = 'Numerics')\n", "ax4.set_xlabel(r'$t$ / ps')\n", "ax4.set_ylabel(r'$\\phi(x_F,t|x_0)$')\n", "ax4.set_xlim(t0, tmax)\n", "ax4.set_ylim(0,1.05)\n", "ax4.legend()\n", "ax4.set_title(r'Trajs with $x_t < x_F$ at time $t$')\n", "\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.5);\n", "#fig.savefig('figures/potential.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": 8, "id": "8cf3fc5c-7dbe-4372-87e3-bdeca5142dbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x10d11eb00>]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(t[0:-1],-np.diff(Phi)/dt)"]}, {"cell_type": "code", "execution_count": 9, "id": "de296c78-102a-4660-af5b-7e6cc38fe376", "metadata": {}, "outputs": [{"data": {"text/plain": ["(99,)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["Pa.shape"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}