import numpy as np
import crocoddyl
from scipy.integrate import solve_ivp
import matplotlib.pyplot as plt
from matplotlib import rcParams
config = {
  "font.family":'sans serif',
  "font.size": 12,
  "mathtext.fontset":'custom',
  "font.sans-serif": ['Arial'],
  "axes.unicode_minus":False,
}
rcParams.update(config)

# =============================================================================
# 1. 案例定义 (根据论文 Sec. III.C)
# =============================================================================
# --- 杜芬振荡器动力学 ---
def duffing_drift(x):
  """漂移项 f(x)"""
  x1, x2 = x
  return np.array([x2, x1 - x1**3])

def duffing_control_field(x):
  """控制项 g(x)"""
  return np.array([0.0, 1.0])

def duffing_dynamics(t, x, u_func):
  """完整的杜芬动力学"""
  u = u_func(t)
  return duffing_drift(x) + duffing_control_field(x) * u

# 初始条件 (用于生成样本)
mean_x0 = np.array([-0.5, 1.0])
cov_x0 = np.diag([0.05, 0.05])
N_SAMPLES_MC = 1000  # 用于生成"真值"的蒙特卡洛样本数

# 仿真时间
T_FINAL = 5.0
t_eval = np.linspace(0, T_FINAL, 200)

# =============================================================================
# 2. 数据驱动建模: EDMD 计算 L0 和 B1
# =============================================================================

# --- 2.1 定义基函数字典 ---
def create_rbf_dictionary(grid_min, grid_max, num_per_dim, sigma):
  """创建高斯RBF基函数字典"""
  centers_x1 = np.linspace(grid_min, grid_max, num_per_dim)
  centers_x2 = np.linspace(grid_min, grid_max, num_per_dim)
  grid_centers = np.array(np.meshgrid(centers_x1, centers_x2)).T.reshape(-1, 2)
  
  # Psi(x) 是一个返回向量的函数
  def Psi(x):
    diff = grid_centers - x
    sq_dist = np.sum(diff**2, axis=1)
    return np.exp(-sq_dist / (2 * sigma**2))

  return Psi, grid_centers

# 论文中指定的字典参数
GRID_MIN, GRID_MAX = -2.5, 2.5
NUM_PER_DIM = 30
SIGMA_RBF = 0.12 # RBF宽度，可以调整
Psi, centers = create_rbf_dictionary(GRID_MIN, GRID_MAX, NUM_PER_DIM, SIGMA_RBF)
K_DIM = len(centers) # 字典维度: 30*30 = 900

# --- 2.2 生成建模数据 ---
print("正在生成用于EDMD建模的数据...")
DT_EDMD = 0.005 # 论文指定的时间步长
# 论文指定使用 50x50 网格生成初始点
num_grid_points = 50
x1_grid = np.linspace(GRID_MIN, GRID_MAX, num_grid_points)
x2_grid = np.linspace(GRID_MIN, GRID_MAX, num_grid_points)
X_data_grid = np.array(np.meshgrid(x1_grid, x2_grid)).T.reshape(-1, 2)
M_SAMPLES = len(X_data_grid)
Psi_X = np.array([Psi(x) for x in X_data_grid]).T  # Shape: (K_DIM, M_SAMPLES)

# --- 2.3 计算 L0 ---
print("正在计算 L0...")
Y_data_f = np.array([x + duffing_drift(x) * DT_EDMD for x in X_data_grid])
Psi_Yf = np.array([Psi(y) for y in Y_data_f]).T   # Shape: (K_DIM, M_SAMPLES)
P0 = np.linalg.pinv(Psi_X @ Psi_X.T) @ Psi_Yf @ Psi_X.T
L0 = (P0 - np.eye(K_DIM)) / DT_EDMD

# --- 2.4 计算 B1 ---
print("正在计算 B1...")
dynamics_f_plus_g = lambda x: duffing_drift(x) + duffing_control_field(x)
Y_data_f_plus_g = np.array([x + dynamics_f_plus_g(x) * DT_EDMD for x in X_data_grid])
Psi_Y_f_plus_g = np.array([Psi(y) for y in Y_data_f_plus_g]).T
P1 = np.linalg.pinv(Psi_X @ Psi_X.T) @ Psi_Y_f_plus_g @ Psi_X.T
L1 = (P1 - np.eye(K_DIM)) / DT_EDMD
B1 = L1 - L0

print(f"建模完成. L0和B1的维度: {L0.shape}")

# =============================================================================
# 3. 最优控制问题定义 (根据论文 Sec. V.A)
# =============================================================================
# --- 目标和初始条件 ---
# 初始PDF: p(x(0)) ~ N([1; 1], 0.025*I)
mean_x0_ctrl = np.array([1.0, 1.0])
cov_x0_ctrl = np.diag([0.025, 0.025])
N_SAMPLES_CTRL = 1000 # 用于可视化和验证的MC样本数

# 目标PDF: 一个在(1,0)的狄拉克δ分布
# 这意味着目标均值为[1,0], 目标协方差为0
target_mean = np.array([1.0, 0.0])
target_cov = np.zeros((2, 2))

# --- 时间和离散化 ---
T_HORIZON = 2.0  # 时间长度
DT_DDP = 0.005 # 论文指定的时间步长
N_STEPS = int(T_HORIZON / DT_DDP)

# --- 成本函数权重 ---
# 论文中指定: "in-horizon cost weights...are all set to unity"
# "terminal cost on the reference error in the moments is set to 1000"
# "only the first two moments are considered"

# 状态权重(针对矩)
W_running_mean = 1.0
W_running_m2 = 1.0
W_terminal_mean = 1000.0
W_terminal_m2 = 1000.0 # 论文中没明确说二阶矩，但为了收敛更好，我们也加一个

# 控制权重
R_control = 1.0

# =============================================================================
# 4. 构建Crocoddyl问题
# =============================================================================
print("正在构建Crocoddyl最优控制问题...")

# --- 2.1 定义系统的"状态"和"控制" ---
# 在我们的升维系统中，状态是rho_hat, 控制是u
state = crocoddyl.StateVector(K_DIM)
nu = 1
actuation = crocoddyl.ActuationModelFull(state) # 实际上是1维控制

# --- 2.2 定义输出矩阵 C，用于从rho_hat计算矩 ---
def get_moment_output_matrix(Psi_func, integration_grid, k_dim):
    """计算从rho_hat到矩的线性映射矩阵 C"""
    # y = C @ rho_hat, y是[m1, m2_11, m2_12, m2_22]
    num_moments = 4 # mean_x, mean_y, m2_xx, m2_xy, m2_yy -> 简化为4个
    
    C = np.zeros((num_moments, k_dim))
    
    Psi_grid = np.array([Psi_func(x) for x in integration_grid]).T # (K_DIM, N_GRID)
    
    # 对每个基函数计算其对矩的贡献
    for i in range(k_dim):
        psi_i_values = Psi_grid[i, :]
        # 归一化每个基函数的质量
        mass = np.sum(psi_i_values)
        if mass > 1e-9:
            psi_i_values /= mass

        C[0, i] = np.sum(integration_grid[:, 0] * psi_i_values) # mean x1
        C[1, i] = np.sum(integration_grid[:, 1] * psi_i_values) # mean x2
        C[2, i] = np.sum(integration_grid[:, 0]**2 * psi_i_values) # m2_11
        C[3, i] = np.sum(integration_grid[:, 1]**2 * psi_i_values) # m2_22
        
    return C

C_mat = get_moment_output_matrix(Psi, X_data_grid, K_DIM)

# --- 2.3 初始状态 rho_hat_0 ---
# 从控制问题的初始高斯分布中投影
initial_samples_ctrl = np.random.multivariate_normal(mean_x0_ctrl, cov_x0_ctrl, N_SAMPLES_CTRL)
Psi_ctrl_initial = np.array([Psi(x) for x in initial_samples_ctrl]).T
rho_hat_0_ctrl = np.mean(Psi_ctrl_initial, axis=1)

# --- 2.4 定义目标矩向量 y_ref ---
target_m2_matrix = target_cov + np.outer(target_mean, target_mean)
y_ref = np.array([
    target_mean[0], target_mean[1], 
    target_m2_matrix[0, 0], target_m2_matrix[1, 1]
])

# --- 2.5 创建 DDP 模型 ---
running_models = []
for i in range(N_STEPS):
    # --- 动力学模型 ---
    # 我们需要一个离散时间的模型: x_{k+1} = F(x_k, u_k)
    # 使用简单的欧拉积分: rho_hat_{k+1} = rho_hat_k + dt * (L0*rho_hat + u*B1*rho_hat)
    # Crocoddyl 需要动力学的雅可比矩阵，我们可以手动推导
    # dF/dx = I + dt*(L0 + u*B1)
    # dF/du = dt*B1*x
    # Crocoddyl 有 ActionModelLQR，但我们的系统是双线性的。
    # 我们需要自定义一个ActionModel
    
    # 为了简化，我们使用crocoddyl内置的、支持线性项的动力学和成本
    # 这是一种近似，但对于DDP的迭代过程是有效的
    # x_{k+1} approx. (I+dt*L0)x_k + (dt*B1*x_k_nom)*u_k
    
    # 一个更直接的方式是使用数值微分来让Crocoddyl处理双线性项
    # 定义符号动力学
    # state_sym = crocoddyl.StateVector(K_DIM)
    # rho_hat_sym = state_sym.nx
    # u_sym = crocoddyl.ControlVector(1)

    # 离散动力学
    # 这里我们定义一个函数，然后让crocoddyl去计算它的导数
    def discrete_dynamics_func(x, u):
      return x + DT_DDP * (L0 @ x + u[0] * (B1 @ x))

    # 使用crocoddyl的数值微分模型
    dynamics_model = crocoddyl.ActionModelNumDiff(state, 1, is_feasible=False)
    dynamics_model.set_fx(discrete_dynamics_func) # 自定义动力学函数
    
    # --- 成本模型 ---
    # 跟踪成本: (y - y_ref)^T * W * (y - y_ref) = (C*x - y_ref)^T * W * (C*x - y_ref)
    # 这可以写成二次型: x^T*Q*x + l^T*x + const
    W_run = np.diag([W_running_mean, W_running_mean, W_running_m2, W_running_m2])
    Q_run = C_mat.T @ W_run @ C_mat
    l_run = -2 * C_mat.T @ W_run @ y_ref

    # 控制成本: u^T * R * u
    R_run = np.array([[R_control]])

    cost_model = crocoddyl.CostModelSum(state, nu)
    state_cost = crocoddyl.CostModelState(state, nu, rho_hat_0_ctrl) # 随便给个ref state
    state_cost.cost.Q = Q_run
    state_cost.cost.r = l_run # 注意crocoddyl的成本是 1/2*xQx + rx
    
    control_cost = crocoddyl.CostModelControl(state, nu)
    control_cost.cost.R = R_run

    cost_model.addCost("state_tracking", state_cost, 1.0)
    cost_model.addCost("control_effort", control_cost, 1.0)
    
    # --- 整合 Action Model ---
    action_model = crocoddyl.IntegratedActionModelEuler(dynamics_model, cost_model, DT_DDP)
    running_models.append(action_model)

# --- 终端成本 ---
W_term = np.diag([W_terminal_mean, W_terminal_mean, W_terminal_m2, W_terminal_m2])
Q_term = C_mat.T @ W_term @ C_mat
l_term = -2 * C_mat.T @ W_term @ y_ref

terminal_cost_model = crocoddyl.CostModelSum(state, 0) # 终端无控制
terminal_state_cost = crocoddyl.CostModelState(state, 0)
terminal_state_cost.cost.Q = Q_term
terminal_state_cost.cost.r = l_term
terminal_cost_model.addCost("terminal_tracking", terminal_state_cost, 1.0)

terminal_dynamics_model = crocoddyl.ActionModelNumDiff(state, 0, is_feasible=False)
terminal_action_model = crocoddyl.IntegratedActionModelEuler(terminal_dynamics_model, terminal_cost_model, 0.0)

# --- 创建问题并求解 ---
problem = crocoddyl.ShootingProblem(rho_hat_0_ctrl, running_models, terminal_action_model)
ddp = crocoddyl.SolverDDP(problem)

print("开始求解DDP...")
ddp.setCallbacks([crocoddyl.CallbackLogger(), crocoddyl.CallbackVerbose()])
ddp.solve(maxiter=100)
print("DDP求解完成.")


# =============================================================================
# 4. 结果提取与分析
# =============================================================================

# --- 3.1 提取最优控制和状态轨迹 ---
u_optimal = np.array(ddp.us)
rho_hat_optimal_traj = np.array(ddp.xs)

# --- 3.2 计算最优轨迹下的矩 ---
optimal_moments = calculate_moments_from_lifted_state(rho_hat_optimal_traj.T, Psi, X_data_grid)

# --- 3.3 运行带最优控制的MC仿真 ---
print("正在运行带最优控制的MC仿真...")

# 创建一个插值函数来获取任意时间的控制输入
from scipy.interpolate import interp1d
time_points_u = np.linspace(0, T_HORIZON - DT_DDP, N_STEPS)
u_interp = interp1d(time_points_u, u_optimal.flatten(), kind='previous', fill_value='extrapolate')

# 运行MC
sol_mc_controlled = solve_ivp(
    fun=lambda t, x: duffing_dynamics(t, x, u_interp),
    t_span=[0, T_HORIZON],
    y0=initial_samples_ctrl.flatten(),
    t_eval=np.linspace(0, T_HORIZON, N_STEPS + 1),
    method='RK45'
)

mc_states_controlled_t = sol_mc_controlled.y.reshape(2, N_SAMPLES_CTRL, -1)

# =============================================================================
# 6. 绘图
# =============================================================================

# --- 6.1 复现 Fig. 5 ---
print("正在生成 Fig. 5...")
fig5, axes5 = plt.subplots(2, 2, figsize=(12, 8))
fig5.suptitle("Fig. 5: Control of Raw Moments", fontsize=16)
time_axis = np.linspace(0, T_HORIZON, N_STEPS + 1)

# Mean x1
axes5[0, 0].plot(time_axis, optimal_moments[:, 0], 'r--', label='Predicted')
# ... (叠加MC结果)
axes5[0, 0].axhline(y=target_mean[0], color='k', linestyle='-', label='Reference')
axes5[0, 0].set_ylabel('Mean of $x_1$')
axes5[0, 0].legend()

# Mean x2
axes5[1, 0].plot(time_axis, optimal_moments[:, 1], 'r--')
axes5[1, 0].axhline(y=target_mean[1], color='k', linestyle='-')
axes5[1, 0].set_ylabel('Mean of $x_2$')
axes5[1, 0].set_xlabel('Time (s)')

# Control Input
axes5[0, 1].plot(time_points_u, u_optimal, 'b-')
axes5[0, 1].set_ylabel('Optimal Control u(t)')

# Second Moment
# ... (绘制二阶矩的跟踪情况)
axes5[1, 1].plot(time_axis, optimal_moments[:, 2], 'r--', label='$m_2^{11}$ predicted')
axes5[1, 1].axhline(y=target_m2_matrix[0,0], color='k', linestyle='-')
axes5[1, 1].set_ylabel('2nd Raw Moment')
axes5[1, 1].set_xlabel('Time (s)')

plt.tight_layout(rect=[0, 0, 1, 0.96])