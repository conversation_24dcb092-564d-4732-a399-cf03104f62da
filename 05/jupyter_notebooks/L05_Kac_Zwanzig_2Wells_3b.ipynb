{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3c3452a5", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "# import scipy.linalg\n", "# import scipy.sparse\n", "# import scipy.sparse.linalg\n", "# from sympy import *\n", "from tqdm import tqdm\n", "from integrator_functions import euler_integrator_step, vel_verlet_integrator_step, leap_frog_integrator_step, acf\n", "# import scipy.special\n", "import matplotlib.cm as cm\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "id": "23b89411", "metadata": {}, "source": ["# Heavy particle in a heat bath\n", "\n", "Considera heavy particle $\\lbrace Q, \\, P, \\, M \\rbrace$, where $Q$ denotes the position, $P$ the momentum and $M$ the mass, in contact with a heat bath consisting of $N$ coupled oscillators $\\lbrace q_i, \\, p_i, \\, m_i \\rbrace$.\n", "\n", "The hamiltonian of the system is\n", "\n", "\\begin{equation}\n", "H = \\frac{P^2}{2M} + \\sum_{i=1}^N \\frac{p_i^2}{2m_i} + V(Q) + \\frac{1}{2 }  \\sum_{i=1}^N k_i \\,  (q_i - Q)^2\n", "\\end{equation}\n", "\n", "The equations of motion of the heavy particle are \n", "\n", "\\begin{equation}\n", "\\begin{cases}\n", "\\dot{Q} &= \\frac{P}{M} \\\\\n", "\\dot{P} &= -\\nabla V(Q) - \\sum_{i=1}^N k_i \\, (Q - q_i)\\\\\n", "\\end{cases} \\, .\n", "\\end{equation}\n", "\n", "The equations of motion of the $N$ lights particles of the heat bath are \n", "\n", "\\begin{equation}\n", "\\begin{cases}\n", "\\dot{q_i} &= \\frac{p_i}{m_i} \\\\\n", "\\dot{p_i} &= k_i \\, (Q - q_i)\\\\\n", "\\end{cases} \\, .\n", "\\end{equation}"]}, {"cell_type": "markdown", "id": "81d68b8b", "metadata": {}, "source": ["# Initial positions and momenta of the oscillators\n", "\n", "We draw the initial positions and the initial momenta from the <PERSON><PERSON>mann distributions:\n", "\n", "$$\n", "\\pi_q (q_i) = \\frac{1}{Z_q} \\exp\\left( - \\beta \\frac{k_i}{2}(q_i - Q(0))^2 \\right) \\,; \\quad\\quad \\pi_p (p_i) = \\frac{1}{Z_p} \\exp\\left( - \\beta \\frac{p_i^2}{2m_i}\\right) \\, .\n", "$$\n", "\n", "Comparing with the Gaussian function\n", "\n", "$$\n", "f(x) = \\frac{1}{\\sqrt{2\\pi \\sigma^2}} \\exp\\left( -  \\frac{1}{2}\\frac{(x - x(0))^2}{\\sigma^2} \\right) \\, ,\n", "$$\n", "\n", "we obtain that the initial positions and momenta can be estimated as\n", "\n", "$$\n", "q_i(0) = Q(0) + \\xi_i \\sqrt{\\frac{1}{\\beta k_i}} \\\\\n", "p_i(0) = \\eta_i \\sqrt{\\frac{m_i}{\\beta}} \\, ,\n", "$$\n", "\n", "with $\\xi, \\eta_i \\in \\mathcal{N}(0,1)$."]}, {"cell_type": "markdown", "id": "c7ba4dff", "metadata": {}, "source": ["## Integrator setup, system parameters, position discretization"]}, {"cell_type": "code", "execution_count": 2, "id": "35f9d62a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#integrator = 'Euler'\n", "#integrator = 'velocity_verlet'\n", "integrator = 'leap_frog'\n", "\n", "# Number of timesteps\n", "Nsteps = 400000\n", "\n", "# Integration timestep\n", "dt = 0.02\n", "\n", "# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "beta  = 1 / kB / T  # kJ-1 mol\n", "M     = 1           # amu\n", "\n", "# Potential energy function kJ mol-1\n", "# V     = (x**2 - 1)**2\n", "# #V     = x**2\n", "# x = symbols('x')\n", "# der_V = V.diff(x)\n", "# V     = lambdify((x), V, modules=['numpy'])\n", "# der_V = lambdify((x), der_V, modules=['numpy'])\n", "V = lambda x: (x**2 - 1)**2\n", "der_V = lambda x: 4*x*(x**2 - 1)\n", "\n", "# Q-<PERSON>rid\n", "xmin  = -6.5\n", "xmax  = - xmin\n", "xbins = 100\n", "xedges = np.linspace(xmin,xmax,xbins)\n", "dx = xedges[1] - xedges[0]\n", "xcenters = xedges + 0.5* dx\n", "xcenters = np.delete(xcenters,-1)\n", "xbins = len(xcenters)\n", "\n", "stat_distr = np.exp(-beta * V(xcenters)) / np.sum(np.exp(-beta * V(xcenters)) * dx)\n", "\n", "fig, (ax1, ax2) = plt.subplots(1, 2,figsize=(10, 3))  \n", "ax1.plot(xcenters, V(xcenters), 'k-',label='Potential energy function')\n", "ax1.legend(loc='upper left')\n", "ax1.set_xlabel('x');\n", "ax1.set_ylim((0,5))\n", "ax2.plot(xcenters, stat_distr, 'k-',label='Stationary distribution')\n", "ax2.set_xlabel('x');\n", "ax2.legend(loc='upper left')\n", "ax2.set_ylim((0,0.6))\n", "fig.tight_layout();"]}, {"cell_type": "markdown", "id": "84a9d8c7", "metadata": {}, "source": ["# Initial frequencies, spring constants and masses of the oscillators\n", "\n", "The $i$th particle, with mass $m_i$ and spring constant $k_i$, oscillates with frequency\n", "\n", "$$\n", "\\omega_i = \\sqrt{\\frac{k_i}{m_i}} \\, .\n", "$$\n", "\n", "The frequencies are drawn from a uniform distribution:\n", "\n", "$$\n", "\\omega_i = N^a \\nu_i\\,; \\quad \\nu_i \\in \\mathcal{U}(0,1)\\,; \\quad a \\in[0,1]\n", "$$\n", "\n", "The spring constants are written as\n", "\n", "$$\n", "k_i = f^2(\\omega_i) \\Delta \\omega \\, , \n", "$$\n", "\n", "where \n", "\n", "$$\n", "\\Delta \\omega = \\frac{N^a}{N} \\,,\n", "$$\n", "\n", "and\n", "\n", "$$\n", "f(\\omega_i) = \\sqrt{\\frac{2}{\\pi} \\frac{1}{1 + \\omega^2}} \\, .\n", "$$"]}, {"cell_type": "code", "execution_count": 3, "id": "02e7e2f6", "metadata": {}, "outputs": [], "source": ["# Number of oscillators\n", "N     = 2000\n", "\n", "# case 1\n", "omega1    = np.ones(N)\n", "m1        = M * np.ones(N) / 100\n", "k_spring1 = omega1**2 * m1\n", "\n", "# case 2\n", "omega2    = np.linspace(1, 12.5, N)\n", "m2        = M * np.ones(N) / 100\n", "k_spring2 = omega2 **2 * m1\n", "\n", "# case 3\n", "gamma   = 1\n", "a      = 0.333333\n", "deltaw = N**a / N\n", "alpha   = 10\n", "barbeta = (alpha * gamma)**-1\n", "\n", "def f2(omega):\n", "  return 2*alpha / (np.pi*barbeta) * 1 / ( alpha**2  + omega ** 2)\n", "\n", "omega3    = np.random.uniform(0,1,N) * N ** a\n", "k_spring3 = f2(omega3) * N**a / N\n", "m3        = k_spring3 / omega3 ** 2"]}, {"cell_type": "code", "execution_count": 4, "id": "5e9f3b96", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 399999/399999 [00:18<00:00, 21084.01it/s]\n"]}], "source": ["# Trajectoryes arrays\n", "Q1 = np.zeros((Nsteps))\n", "q1 = np.zeros(N)\n", "P1 = np.zeros((Nsteps))\n", "p1 = np.zeros(N)\n", "\n", "Q2 = np.zeros((Nsteps))\n", "q2 = np.zeros(N)\n", "P2 = np.zeros((Nsteps))\n", "p2 = np.zeros(N)\n", "\n", "Q3 = np.zeros((Nsteps))\n", "q3 = np.zeros(N)\n", "P3 = np.zeros((Nsteps))\n", "p3 = np.zeros(N)\n", "\n", "\n", "# Initial position heavy particle\n", "Q0    = -1\n", "Q1[0] = Q0\n", "Q2[0] = Q0\n", "Q3[0] = Q0\n", "\n", "#Draw initial positions of oscillators from <PERSON><PERSON>mann distribution:\n", "q1 = Q0 + np.random.normal(0, 1, N) * np.sqrt( 1 / beta / k_spring1 )\n", "q2 = Q0 + np.random.normal(0, 1, N) * np.sqrt( 1 / beta / k_spring2 )\n", "q3 = Q0 + np.random.normal(0, 1, N) * np.sqrt( 1 / beta / k_spring3 )\n", "\n", "#Draw initial momentum of heavy particle from <PERSON><PERSON><PERSON> distribution:\n", "P1[0] = np.random.normal(0, 1) * np.sqrt( M / beta )\n", "P2[0] = np.random.normal(0, 1) * np.sqrt( M / beta )\n", "P3[0] = np.random.normal(0, 1) * np.sqrt( M / beta )\n", "\n", "#Draw initial momenta of oscillators from <PERSON><PERSON>mann distribution:\n", "p1 = np.random.normal(0, 1, N) * np.sqrt(m1 / beta )\n", "p2 = np.random.normal(0, 1, N) * np.sqrt(m2 / beta )#\n", "p3 = np.random.normal(0, 1, N) * np.sqrt(m3 / beta )\n", "#p3 = np.random.uniform(-0.2, 0.2, N) \n", "\n", "\n", "for k in tqdm(range(Nsteps-1)):\n", "  if integrator == 'Euler':\n", "    Q1[k+1], q1, P1[k+1], p1 = euler_integrator_step(Q1[k], q1, P1[k], p1, M, m1, der_V, k_spring1, dt)\n", "    Q2[k+1], q2, P2[k+1], p2 = euler_integrator_step(Q2[k], q2, P2[k], p2, M, m2, der_V, k_spring2, dt)\n", "    Q3[k+1], q3, P3[k+1], p3 = euler_integrator_step(Q3[k], q3, P3[k], p3, M, m3, der_V, k_spring3, dt)\n", "      \n", "  elif integrator =='velocity_verlet':\n", "    Q1[k+1], q1, P1[k+1], p1 = vel_verlet_integrator_step(Q1[k], q1, P1[k], p1, M, m1, der_V, k_spring1, dt)\n", "    Q2[k+1], q2, P2[k+1], p2 = vel_verlet_integrator_step(Q2[k], q2, P2[k], p2, M, m2, der_V, k_spring2, dt)\n", "    Q3[k+1], q3, P3[k+1], p3 = vel_verlet_integrator_step(Q3[k], q3, P3[k], p3, M, m3, der_V, k_spring3, dt)\n", "\n", "  elif integrator =='leap_frog':\n", "    Q1[k+1], q1, P1[k+1], p1 = leap_frog_integrator_step(Q1[k], q1, P1[k], p1, M, m1, der_V, k_spring1, dt)\n", "    Q2[k+1], q2, P2[k+1], p2 = leap_frog_integrator_step(Q2[k], q2, P2[k], p2, M, m2, der_V, k_spring2, dt)\n", "    Q3[k+1], q3, P3[k+1], p3 = leap_frog_integrator_step(Q3[k], q3, P3[k], p3, M, m3, der_V, k_spring3, dt)\n", "\n", "\n", "# Build histogram\n", "h  = np.histogram(Q1, bins=xedges, density=True)\n", "h1 = h[0]\n", "\n", "h  = np.histogram(Q2, bins=xedges, density=True)\n", "h2 = h[0]\n", "\n", "h  = np.histogram(Q3, bins=xedges, density=True)\n", "h3 = h[0]"]}, {"cell_type": "code", "execution_count": 5, "id": "c7e7aada", "metadata": {}, "outputs": [], "source": ["# FIGURES \n", "# 1D grid for position axis\n", "rmin     = - 4\n", "rmax     = - rmin\n", "rbins    = 200\n", "redges   = np.linspace(rmin, rmax, rbins)\n", "dr       = redges[1] - redges[0]\n", "rcenters = redges + 0.5* dr\n", "rcenters = np.delete(rcenters, -1)\n", "rbins    = len(rcenters)\n", "\n", "# 1D grid for momentum axis\n", "pmin     = - 4\n", "pmax     = - pmin\n", "pbins    = 200\n", "pedges   = np.linspace(pmin, pmax, pbins)\n", "dp       = pedges[1] - pedges[0]\n", "pcenters = pedges + 0.5 * dp\n", "pcenters = np.delete(pcenters, -1)\n", "pbins    = len(pcenters)\n", "\n", "# 2D grid for 2D histograms\n", "grid     = np.meshgrid(rcenters, pcenters)\n", "rgrid    = grid[0]\n", "pgrid    = grid[1]"]}, {"cell_type": "code", "execution_count": 6, "id": "b7b6a712", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["qq =Q3.copy()\n", "pp =P3.copy()\n", "\n", "# Build histogram of position and momenta\n", "hist  = np.histogram2d(qq, pp, bins=[redges, pedges], density=True)[0]\n", "row_norms_l1 = np.linalg.norm(hist, ord=1, axis=1, keepdims=True)\n", "#hist  = hist / row_norms_l1\n", "\n", "# Boltzmann distribution\n", "pi = np.exp( - beta * V(rgrid) ) * np.exp( - beta * pgrid ** 2 / (2 * M) )\n", "\n", "# Normalize Boltzmann distribution\n", "pi = pi / np.sum( pi * dr * dp )\n", "\n", "# Time intervals\n", "tvalues = np.linspace(0, Nsteps-1, Nsteps) * dt\n", "\n", "fig, ax = plt.subplots(2, 2,figsize=(10, 6))  \n", "ax[0,0].plot(tvalues[0:10000], qq[0:10000],'k-', linewidth = 0.5, label = r'$r(t)$')\n", "ax[0,0].set_xlabel('time / ps')\n", "ax[0,0].set_ylabel('r / nm')\n", "ax[0,0].set_title('Trajectory')\n", "\n", "ax[0,1].plot(qq[0:10000], pp[0:10000], 'k-', linewidth = 0.5, label = r'$\\lbrace r(t), p(t)\\rbrace$')\n", "ax[0,1].set_xlabel(r'$r$ / nm')\n", "ax[0,1].set_ylabel(r'$p$ / $amu\\cdot nm \\cdot s^{-1}$')\n", "ax[0,1].set_title('Phase space')\n", "ax[0,1].set_xlim((rmin, rmax))\n", "ax[0,1].set_ylim((pmin, pmax))\n", "ax[0,1].set_aspect('equal')\n", "\n", "pos = ax[1,0].pcolormesh(rgrid, pgrid, hist.T,  cmap = cm.afmhot, vmin=0, vmax=0.5*np.maximum(np.max(pi), np.max(hist)))\n", "ax[1,0].set_title('Normalized histogram')\n", "ax[1,0].set_xlabel(r'$r$ / nm')\n", "ax[1,0].set_ylabel(r'$p$ / $\\mathrm{amu\\cdot nm \\cdot s^{-1}}$')\n", "ax[1,0].set_xlim((rmin, rmax))\n", "ax[1,0].set_ylim((pmin, pmax))\n", "ax[1,0].set_aspect('equal')\n", "fig.colorbar(pos, ax=ax[1,0])\n", "\n", "pos = ax[1,1].pcolormesh(rgrid, pgrid, pi, cmap = cm.afmhot, vmin=0, vmax=0.06)\n", "ax[1,1].set_title('Boltzmann distribution')\n", "ax[1,1].set_xlabel(r'$r$ / nm')\n", "ax[1,1].set_ylabel(r'$p$ / $\\mathrm{amu\\cdot nm \\cdot s^{-1}}$')\n", "ax[1,1].set_xlim((rmin, rmax))\n", "ax[1,1].set_ylim((pmin, pmax))\n", "ax[1,1].set_aspect('equal')\n", "fig.colorbar(pos, ax=ax[1,1])\n", "\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3);\n", "#fig.savefig('figures/sim_UL_'+str(N3)+'_gamma_'+str(gamma)+'.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}