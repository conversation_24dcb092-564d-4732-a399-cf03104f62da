{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3c3452a5", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "# import scipy.linalg\n", "# import scipy.sparse\n", "# import scipy.sparse.linalg\n", "# from sympy import *\n", "from tqdm import tqdm\n", "from integrator_functions import euler_integrator_step, vel_verlet_integrator_step, leap_frog_integrator_step, acf\n", "# import scipy.special\n", "import matplotlib.cm as cm\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "markdown", "id": "23b89411", "metadata": {}, "source": ["# Heavy particle in a heat bath\n", "\n", "Considera heavy particle $\\lbrace Q, \\, P, \\, M \\rbrace$, where $Q$ denotes the position, $P$ the momentum and $M$ the mass, in contact with a heat bath consisting of $N$ coupled oscillators $\\lbrace q_i, \\, p_i, \\, m_i \\rbrace$.\n", "\n", "The hamiltonian of the system is\n", "\n", "\\begin{equation}\n", "H = \\frac{P^2}{2M} + \\sum_{i=1}^N \\frac{p_i^2}{2m_i} + V(Q) + \\frac{1}{2 }  \\sum_{i=1}^N k_i \\,  (q_i - Q)^2\n", "\\end{equation}\n", "\n", "The equations of motion of the heavy particle are \n", "\n", "\\begin{equation}\n", "\\begin{cases}\n", "\\dot{Q} &= \\frac{P}{M} \\\\\n", "\\dot{P} &= -\\nabla V(Q) - \\sum_{i=1}^N k_i \\, (Q - q_i)\\\\\n", "\\end{cases} \\, .\n", "\\end{equation}\n", "\n", "The equations of motion of the $N$ lights particles of the heat bath are \n", "\n", "\\begin{equation}\n", "\\begin{cases}\n", "\\dot{q_i} &= \\frac{p_i}{m_i} \\\\\n", "\\dot{p_i} &= k_i \\, (Q - q_i)\\\\\n", "\\end{cases} \\, .\n", "\\end{equation}"]}, {"cell_type": "markdown", "id": "81d68b8b", "metadata": {}, "source": ["# Initial positions and momenta of the oscillators\n", "\n", "We draw the initial positions and the initial momenta from the <PERSON><PERSON>mann distributions:\n", "\n", "$$\n", "\\pi_q (q_i) = \\frac{1}{Z_q} \\exp\\left( - \\beta \\frac{k_i}{2}(q_i - Q(0))^2 \\right) \\,; \\quad\\quad \\pi_p (p_i) = \\frac{1}{Z_p} \\exp\\left( - \\beta \\frac{p_i^2}{2m_i}\\right) \\, .\n", "$$\n", "\n", "Comparing with the Gaussian function\n", "\n", "$$\n", "f(x) = \\frac{1}{\\sqrt{2\\pi \\sigma^2}} \\exp\\left( -  \\frac{1}{2}\\frac{(x - x(0))^2}{\\sigma^2} \\right) \\, ,\n", "$$\n", "\n", "we obtain that the initial positions and momenta can be estimated as\n", "\n", "$$\n", "q_i(0) = Q(0) + \\xi_i \\sqrt{\\frac{1}{\\beta k_i}} \\\\\n", "p_i(0) = \\eta_i \\sqrt{\\frac{m_i}{\\beta}} \\, ,\n", "$$\n", "\n", "with $\\xi, \\eta_i \\in \\mathcal{N}(0,1)$."]}, {"cell_type": "markdown", "id": "c7ba4dff", "metadata": {}, "source": ["## Integrator setup, system parameters, position discretization"]}, {"cell_type": "code", "execution_count": 2, "id": "35f9d62a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#integrator = 'Euler'\n", "#integrator = 'velocity_verlet'\n", "integrator = 'leap_frog'\n", "\n", "# Number of timesteps\n", "Nsteps = 400000\n", "\n", "# Integration timestep\n", "dt = 0.02\n", "\n", "# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "beta  = 1 / kB / T  # kJ-1 mol\n", "M     = 1           # amu\n", "\n", "# Potential energy function kJ mol-1\n", "# V     = 0.5*x**2\n", "# #V     = x**2\n", "# x = symbols('x')\n", "# der_V = V.diff(x)\n", "# V     = lambdify((x), V, modules=['numpy'])\n", "# der_V = lambdify((x), der_V, modules=['numpy'])\n", "V = lambda x: 0.5*x**2\n", "der_V = lambda x: x\n", "\n", "# Q-<PERSON>rid\n", "xmin  = -6.5\n", "xmax  = - xmin\n", "xbins = 100\n", "xedges = np.linspace(xmin,xmax,xbins)\n", "dx = xedges[1] - xedges[0]\n", "xcenters = xedges + 0.5* dx\n", "xcenters = np.delete(xcenters,-1)\n", "xbins = len(xcenters)\n", "\n", "stat_distr = np.exp(-beta * V(xcenters)) / np.sum(np.exp(-beta * V(xcenters)) * dx)\n", "\n", "fig, (ax1, ax2) = plt.subplots(1, 2,figsize=(10, 3))  \n", "ax1.plot(xcenters, V(xcenters), 'k-',label='Potential energy function')\n", "ax1.legend(loc='upper left')\n", "ax1.set_xlabel('x');\n", "ax1.set_ylim((0,5))\n", "ax2.plot(xcenters, stat_distr, 'k-',label='Stationary distribution')\n", "ax2.set_xlabel('x');\n", "ax2.legend(loc='upper left')\n", "ax2.set_ylim((0,0.3))\n", "fig.tight_layout();"]}, {"cell_type": "markdown", "id": "84a9d8c7", "metadata": {}, "source": ["# Initial frequencies, spring constants and masses of the oscillators\n", "\n", "The $i$th particle, with mass $m_i$ and spring constant $k_i$, oscillates with frequency\n", "\n", "$$\n", "\\omega_i = \\sqrt{\\frac{k_i}{m_i}} \\, .\n", "$$\n", "\n", "The frequencies are drawn from a uniform distribution:\n", "\n", "$$\n", "\\omega_i = N^a \\nu_i\\,; \\quad \\nu_i \\in \\mathcal{U}(0,1)\\,; \\quad a \\in[0,1]\n", "$$\n", "\n", "The spring constants are written as\n", "\n", "$$\n", "k_i = f^2(\\omega_i) \\Delta \\omega \\, , \n", "$$\n", "\n", "where \n", "\n", "$$\n", "\\Delta \\omega = \\frac{N^a}{N} \\,,\n", "$$\n", "\n", "and\n", "\n", "$$\n", "f(\\omega_i) = \\sqrt{\\frac{2}{\\pi} \\frac{1}{1 + \\omega^2}} \\, .\n", "$$"]}, {"cell_type": "code", "execution_count": 3, "id": "02e7e2f6", "metadata": {}, "outputs": [], "source": ["# Number of oscillators\n", "N     = 2000\n", "\n", "#####################################################\n", "############# case 1\n", "#####################################################\n", "\n", "omega1    = np.ones(N)\n", "m1        = M * np.ones(N) / 100\n", "k_spring1 = omega1**2 * m1\n", "\n", "\n", "\n", "#####################################################\n", "############# case 2\n", "#####################################################\n", "\n", "\n", "\n", "omega2    = np.linspace(1, 12.5, N)\n", "m2        = M * np.ones(N) / 100\n", "k_spring2 = omega2 **2 * m1\n", "\n", "\n", "#####################################################\n", "############# case 3\n", "#####################################################\n", "\n", "\n", "\n", "gamma   = 1\n", "a      = 0.333333\n", "\n", "deltaw = N**a / N\n", "\n", "alpha   = 10\n", "barbeta = (alpha * gamma)**-1\n", "\n", "def f2(omega):\n", "    return 2*alpha / (np.pi*barbeta) * 1 / ( alpha**2  + omega ** 2)\n", "\n", "\n", "omega3    = np.random.uniform(0,1,N) * N ** a\n", "k_spring3 = f2(omega3) * N**a / N\n", "m3        = k_spring3 / omega3 ** 2"]}, {"cell_type": "code", "execution_count": null, "id": "5e9f3b96", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 399999/399999 [00:24<00:00, 16474.25it/s]\n"]}], "source": ["# Trajectoryes arrays\n", "Q1 = np.zeros((Nsteps))\n", "q1 = np.zeros(N)\n", "P1 = np.zeros((Nsteps))\n", "p1 = np.zeros(N)\n", "\n", "Q2 = np.zeros((Nsteps))\n", "q2 = np.zeros(N)\n", "P2 = np.zeros((Nsteps))\n", "p2 = np.zeros(N)\n", "\n", "Q3 = np.zeros((Nsteps))\n", "q3 = np.zeros(N)\n", "P3 = np.zeros((Nsteps))\n", "p3 = np.zeros(N)\n", "\n", "\n", "# Initial position heavy particle\n", "Q0    = -1\n", "Q1[0] = Q0\n", "Q2[0] = Q0\n", "Q3[0] = Q0\n", "\n", "#Draw initial positions of oscillators from <PERSON><PERSON>mann distribution:\n", "q1 = Q0 + np.random.normal(0, 1, N) * np.sqrt( 1 / beta / k_spring1 )\n", "q2 = Q0 + np.random.normal(0, 1, N) * np.sqrt( 1 / beta / k_spring2 )\n", "q3 = Q0 + np.random.normal(0, 1, N) * np.sqrt( 1 / beta / k_spring3 )\n", "\n", "#Draw initial momentum of heavy particle from <PERSON><PERSON><PERSON> distribution:\n", "P1[0] = np.random.normal(0, 1) * np.sqrt( M / beta )\n", "P2[0] = np.random.normal(0, 1) * np.sqrt( M / beta )\n", "P3[0] = np.random.normal(0, 1) * np.sqrt( M / beta )\n", "\n", "#Draw initial momenta of oscillators from <PERSON><PERSON>mann distribution:\n", "p1 = np.random.normal(0, 1, N) * np.sqrt(m1 / beta )\n", "p2 = np.random.normal(0, 1, N) * np.sqrt(m2 / beta )#\n", "p3 = np.random.normal(0, 1, N) * np.sqrt(m3 / beta )\n", "#p3 = np.linspace(-6, 6, N)#np.random.uniform(-0.2, 0.2, N) \n", "\n", "Temperature    = np.zeros(Nsteps)\n", "Temperature[0] = np.sum(p3 ** 2 / m3 / kB / N) \n", "\n", "Etot           = np.zeros(Nsteps)\n", "Etot[0]        = P3[0] ** 2 / 2 / M + V(Q3[0]) + np.sum(0.5 * p3 ** 2 / m3) + np.sum(0.5 * k_spring3 * ( Q3[0] - q3 ) ** 2)  \n", "\n", "for k in tqdm(range(Nsteps-1)):\n", "  if integrator == 'Euler':\n", "    Q1[k+1], q1, P1[k+1], p1 = euler_integrator_step(Q1[k], q1, P1[k], p1, M, m1, der_V, k_spring1, dt)\n", "    Q2[k+1], q2, P2[k+1], p2 = euler_integrator_step(Q2[k], q2, P2[k], p2, M, m2, der_V, k_spring2, dt)\n", "    Q3[k+1], q3, P3[k+1], p3 = euler_integrator_step(Q3[k], q3, P3[k], p3, M, m3, der_V, k_spring3, dt)\n", "      \n", "  elif integrator =='velocity_verlet':\n", "    Q1[k+1], q1, P1[k+1], p1 = vel_verlet_integrator_step(Q1[k], q1, P1[k], p1, M, m1, der_V, k_spring1, dt)\n", "    Q2[k+1], q2, P2[k+1], p2 = vel_verlet_integrator_step(Q2[k], q2, P2[k], p2, M, m2, der_V, k_spring2, dt)\n", "    Q3[k+1], q3, P3[k+1], p3 = vel_verlet_integrator_step(Q3[k], q3, P3[k], p3, M, m3, der_V, k_spring3, dt)\n", "\n", "  elif integrator =='leap_frog':\n", "    Q1[k+1], q1, P1[k+1], p1 = leap_frog_integrator_step(Q1[k], q1, P1[k], p1, M, m1, der_V, k_spring1, dt)\n", "    Q2[k+1], q2, P2[k+1], p2 = leap_frog_integrator_step(Q2[k], q2, P2[k], p2, M, m2, der_V, k_spring2, dt)\n", "    Q3[k+1], q3, P3[k+1], p3 = leap_frog_integrator_step(Q3[k], q3, P3[k], p3, M, m3, der_V, k_spring3, dt)\n", "    Temperature[k+1] = np.sum(p3 ** 2 / m3 / kB / N) \n", "    Etot[k+1]        = P3[k+1] ** 2 / 2 / M + V(Q3[k+1]) + np.sum(0.5 * p3 ** 2 / m3) + np.sum(0.5 * k_spring3 * ( Q3[k+1] - q3 ) ** 2)\n", "\n", "# Build histogram\n", "h  = np.histogram(Q1, bins=xedges, density=True)\n", "h1 = h[0]\n", "\n", "h  = np.histogram(Q2, bins=xedges, density=True)\n", "h2 = h[0]\n", "\n", "h  = np.histogram(Q3, bins=xedges, density=True)\n", "h3 = h[0]"]}, {"cell_type": "code", "execution_count": 6, "id": "85bc139e", "metadata": {}, "outputs": [], "source": ["# FIGURES \n", "# 1D grid for position axis\n", "rmin     = - 6\n", "rmax     = - rmin\n", "rbins    = 200\n", "redges   = np.linspace(rmin, rmax, rbins)\n", "dr       = redges[1] - redges[0]\n", "rcenters = redges + 0.5* dr\n", "rcenters = np.delete(rcenters, -1)\n", "rbins    = len(rcenters)\n", "\n", "# 1D grid for momentum axis\n", "pmin     = - 6\n", "pmax     = - pmin\n", "pbins    = 200\n", "pedges   = np.linspace(pmin, pmax, pbins)\n", "dp       = pedges[1] - pedges[0]\n", "pcenters = pedges + 0.5 * dp\n", "pcenters = np.delete(pcenters, -1)\n", "pbins    = len(pcenters)\n", "\n", "# 2D grid for 2D histograms\n", "grid     = np.meshgrid(rcenters, pcenters)\n", "rgrid    = grid[0]\n", "pgrid    = grid[1]"]}, {"cell_type": "code", "execution_count": 7, "id": "1929595b", "metadata": {}, "outputs": [], "source": ["qq =Q3.copy()\n", "pp =P3.copy()\n", "\n", "# Build histogram of position and momenta\n", "hist  = np.histogram2d(qq, pp, bins=[redges, pedges], density=True)[0]\n", "row_norms_l1 = np.linalg.norm(hist, ord=1, axis=1, keepdims=True)\n", "#hist  = hist / row_norms_l1\n", "\n", "# Boltzmann distribution\n", "pi = np.exp( - beta * V(rgrid) ) * np.exp( - beta * pgrid ** 2 / (2 * M) )\n", "\n", "# Normalize Boltzmann distribution\n", "pi = pi / np.sum( pi * dr * dp )\n", "\n", "# Time intervals\n", "tvalues = np.linspace(0, Nsteps-1, Nsteps) * dt"]}, {"cell_type": "code", "execution_count": 10, "id": "1dcfeef4", "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************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***************************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**********************************************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", "text/plain": ["<Figure size 1000x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches\n", "\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(10, 6))  \n", "\n", "ax1.plot(qq[0:10000], pp[0:10000], 'k-', linewidth = 0.5, label = r'$\\lbrace r(t), p(t)\\rbrace$')\n", "ax1.set_xlabel(r'$r$ / nm')\n", "ax1.set_ylabel(r'$p$ / $\\mathrm{amu\\cdot nm \\cdot s^{-1}}$')\n", "ax1.set_title('(a) Phase space')\n", "ax1.set_xlim((rmin, rmax))\n", "ax1.set_ylim((pmin, pmax))\n", "ax1.set_aspect('equal')\n", "\n", "ax2.plot(tvalues, Etot, 'k-')\n", "ax2.set_xlabel('time / ps')\n", "ax2.set_title('(b) Energy')\n", "# ax2.set_ylim((0, 100))\n", "\n", "ax3.plot(tvalues, Temperature, 'k-')\n", "ax3.set_xlabel('time / ps')\n", "ax3.set_title('(c) Temperature')\n", "ax3.set_ylim((0, 900))\n", "\n", "pos = ax4.pcolormesh(rgrid, pgrid, hist.T,  cmap = cm.afmhot, vmin=0, vmax=0.5*np.maximum(np.max(pi), np.max(hist)))\n", "ax4.set_title('(d) Histogram')\n", "ax4.set_xlabel(r'$r$ / nm')\n", "ax4.set_ylabel(r'$p$ / $\\mathrm{amu\\cdot nm \\cdot s^{-1}}$')\n", "ax4.set_xlim((rmin, rmax))\n", "ax4.set_ylim((pmin, pmax))\n", "ax4.set_aspect('equal')\n", "#fig.colorbar(pos, ax=ax4)\n", "\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=1, right=1, top=2, wspace=0.6, hspace=0.8);\n", "#fig.savefig('figures/closed_prob_distr.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}