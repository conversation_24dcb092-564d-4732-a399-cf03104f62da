{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6448c2e3", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "# from sympy import *\n", "from tqdm import tqdm\n", "import matplotlib.cm as cm\n", "\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches\n", "\n", "# for reproducibility\n", "np.random.seed(0) "]}, {"cell_type": "code", "execution_count": 3, "id": "0849fd22", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# System parameters\n", "kB           = 0.008314463           # kJ mol-1 K\n", "T            = 300                   # K\n", "beta         = 1 / kB / T            # kJ-1 mol\n", "M            = 1                     # amu\n", "\n", "# Spring constant\n", "k_spring = 1   #  N m-1  \n", "\n", "# Angular frequency\n", "omega    = np.sqrt(k_spring / M)\n", "\n", "# # Potential energy function kJ mol-1\n", "# r     = symbols('r')\n", "# V     = 0.5*k_spring*r**2 #( r**2 - 1 )**2  \n", "# der_V = V.diff(r)\n", "\n", "# # Convert the potential and its derivative in numpy\n", "# V     = lambdify((r), V, modules=['numpy'])\n", "# der_V = lambdify((r), der_V, modules=['numpy'])\n", "\n", "V = lambda r: 0.5*k_spring * r**2\n", "der_V = lambda r: k_spring * r\n", "\n", "# 1D grid for position axis\n", "rmin     = - 2.5\n", "rmax     = - rmin\n", "rbins    = 200\n", "redges   = np.linspace(rmin, rmax, rbins)\n", "dr       = redges[1] - redges[0]\n", "rcenters = redges + 0.5* dr\n", "rcenters = np.delete(rcenters, -1)\n", "rbins    = len(rcenters)\n", "\n", "# 1D grid for momentum axis\n", "pmin     = - 2.5\n", "pmax     = - pmin\n", "pbins    = 200\n", "pedges   = np.linspace(pmin, pmax, pbins)\n", "dp       = pedges[1] - pedges[0]\n", "pcenters = pedges + 0.5 * dp\n", "pcenters = np.delete(pcenters, -1)\n", "pbins    = len(pcenters)\n", "\n", "# 2D grid for 2D histograms\n", "grid     = np.meshgrid(rcenters, pcenters)\n", "rgrid    = grid[0]\n", "pgrid    = grid[1]\n", "\n", "fig, ax1 = plt.subplots(1, 1,figsize=(5, 3))  \n", "ax1.plot(rcenters, V(rcenters), 'k', linewidth=2)\n", "ax1.set_title('Potential energy function')\n", "ax1.set_xlabel('r')\n", "ax1.set_ylabel('V(r)')\n", "ax1.set_ylim((0, 4));"]}, {"cell_type": "markdown", "id": "ee32d81c", "metadata": {}, "source": ["### Hamiltonian dynamics\n", "\n", "Consider a particle with mass $M>0$, momentum $p\\in \\Gamma_p \\subset \\mathbb{R}$ and position $r\\in \\Gamma_r \\subset \\mathbb{R}$, moving in a potential energy function $V(r):\\Gamma_r \\rightarrow \\mathbb{R}$.\n", "The hamiltonian function of the system is\n", "\n", "\\begin{equation}\n", "H = \\frac{p^2}{2M} + V(r) \\, ,\n", "\\end{equation}\n", "\n", "and the equations of motion are written as\n", "\n", "\\begin{equation}\n", "\\begin{cases}\n", "\\dot{r} &= \\frac{p(t)}{M} \\\\\n", "\\dot{p} &= F(t) = -\\nabla_r V(r(t))\\\\\n", "\\end{cases} \\, .\n", "\\end{equation}\n", "\n", "In the following example, we consider the potential of the harmonic oscillator\n", "\n", "$$\n", "V(r) = \\frac{1}{2}r^2 \\, .\n", "$$\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ff08ee51", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100000/100000 [00:00<00:00, 139661.91it/s]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Number of replicas\n", "Nreps = 1\n", "\n", "# Number of timesteps\n", "Nsteps = 100000\n", "\n", "# Integration timestep\n", "dt = 0.02\n", "\n", "# Time intervals\n", "tvalues = np.linspace(0, Nsteps-1,Nsteps) * dt\n", "\n", "# Array where to store positions\n", "r = np.zeros((<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>))\n", "\n", "# Array where to store momenta\n", "p = np.zeros((<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>))\n", "\n", "E      = 2\n", "\n", "# Generate initial positions and momenta such that the total energy is E \n", "for i in range(Nreps):\n", "  r0     = np.random.uniform(-E, E)\n", "  u      = np.random.randint(0,2)\n", "  sign   = -1 * (u==0) + 1 * (u==1)\n", "  p0     = sign * np.sqrt( 2 * M * np.abs( E - V(r0) ) )\n", "  r[0,i] = r0\n", "  p[0,i] = p0\n", "\n", "# Array where to store energy\n", "Et = np.zeros((<PERSON><PERSON><PERSON>, Nreps))\n", "\n", "# Velocity Verlet integrator\n", "for k in tqdm(range(Nsteps)):\n", "  t = tvalues[k]\n", "  r[k,:]  = r[0,:] * np.cos( omega * t ) + p[0,:] / M / omega * np.sin( omega * t )\n", "  p[k,:]  =  - M * omega * r[0,:] * np.sin( omega * t ) + p[0,:] * np.cos( omega * t )\n", "  # Total energy\n", "  Et[k,:]  =  p[k,:] ** 2 / ( 2 * M ) + V(r[k,:])     \n", "\n", "# Build histogram of position and momenta\n", "hist2  =  np.histogram2d(r.flatten(), p.flatten(), bins=[redges, pedges], density = False )[0]\n", "#hist2  =  hist2  / np.sum(hist2*dr*dp)\n", "# Project the histogram along the momenta axis\n", "hist1  =  np.sum(hist2 * dp, axis=1)\n", "\n", "#np.linalg.norm(hist, ord=1, axis=1, keepdims=True)\n", "#hist  = hist / row_norms_l1\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2,figsize=(10, 6))  \n", "ax1r = ax1.twinx()\n", "ax1.plot(tvalues[0:10000:1], r[0:10000:1],'k.', linewidth = 2, label = r'$x(t)$')\n", "ax1.set_xlabel(r'$t$ / ps')\n", "ax1.set_ylabel(r'$r$ / nm')\n", "ax1.set_title('(a) Trajectory and Energy')\n", "ax1.set_xlim((0,50))\n", "ax1.set_ylim((-4,4))\n", "ax1r.plot(tvalues[0:10000:1], Et[0:10000:1,0],'b-', linewidth = 2, label = r'$E(t)$')\n", "ax1r.set_ylabel(r'Energy / $\\mathrm{kJ\\cdot mol^{-1}}$')\n", "ax1r.set_ylim((0, 3.5))\n", "\n", "ax2.plot(r[0:10000:1], p[0:10000:1], 'k.', linewidth = 2, label = r'$\\lbrace x(t), p(t)\\rbrace$')\n", "ax2.set_xlabel(r'$r$ / nm')\n", "ax2.set_ylabel(r'$p$ / $\\mathrm{amu\\cdot nm \\cdot s^{-1}}$')\n", "ax2.set_title('(b) Phase space')\n", "ax2.set_xlim((rmin, rmax))\n", "ax2.set_ylim((pmin, pmax))\n", "ax2.set_aspect('equal')\n", "\n", "pos = ax3.pcolormesh(rgrid, pgrid, hist2.T,  cmap = cm.afmhot, shading='gouraud', vmin = 0, vmax = 300)\n", "ax3.set_title(r'(c) Histogram $\\pi(r,p)$')\n", "ax3.set_xlabel(r'$r$ / nm')\n", "ax3.set_ylabel(r'$p$ / $\\mathrm{amu\\cdot nm \\cdot s^{-1}}$')\n", "ax3.set_xlim((rmin, rmax))\n", "ax3.set_ylim((pmin, pmax))\n", "ax3.set_aspect('equal')\n", "fig.colorbar(pos, ax=ax3, pad=0.2)\n", "\n", "ax4.plot(rcenters, hist1, 'k-', linewidth = 2)\n", "ax4.set_xlabel(r'$r$ / nm')\n", "ax4.set_title(r'(d) Histogram $\\pi_r(r)$')\n", "ax4.set_xlim((rmin, rmax))\n", "\n", "ax1.legend(loc='upper left')\n", "ax1r.legend(loc='upper right')\n", "ax2.legend(loc='upper right')\n", "plt.tight_layout();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.6, hspace=0.8);\n", "#fig.savefig('figures/isolated_prob_distr.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}