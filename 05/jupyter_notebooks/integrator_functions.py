import numpy as np
# import matplotlib.pyplot as plt
# import scipy.linalg
# import scipy.sparse
# import scipy.sparse.linalg
# from sympy import *
# from tqdm import tqdm

def acf(data,n):
  """Autocorrelation function."""
  data = data[0:n]
  mean = np.mean(data)
  var = np.var(data)
  ndata = data - mean

  acorr = np.correlate(ndata, ndata, 'full')[len(ndata)-1:] 
  acorr = acorr / var / len(ndata)

  return acorr


# Deterministic integrators

def euler_integrator_step(Q, q, P, p, M, m, der_V, k_spring, dt):
  """"Euler integrator, non time-reversible and non symplectic.
  p_{n+1} = p_n + f_n * dt
  q_{n+1} = q_n + p_n / m * dt + f_n / m * dt^2
  """
  f = k_spring * ( Q - q )   # force on bath particles
  F = - der_V(Q) - np.sum(f) # force on big particle

  q  =  q + p / m * dt + f / m * dt**2
  Q  =  Q + P / M * dt + F / M * dt**2
  p  =  p + f * dt
  P  =  P + F * dt

  return Q, q, P, p


def vel_verlet_integrator_step(Q, q, P, p, M, m, der_V, k_spring, dt):
  """Velocity Verlet integrator, time-reversible and symplectic.
  q_{n+1} = q_n + p_n / m * dt + 0.5 * f_n / m * dt^2
  f_{n+1} = f (q_{n+1})
  p_{n+1} = p_n + 0.5 * ( f_n + f_{n+1} ) * dt
  """
  f  = k_spring * ( Q - q )
  F  = - der_V(Q) - np.sum( f )
  q  =  q + p / m * dt + 0.5 * f / m * dt ** 2
  Q  =  Q + P / M * dt + 0.5 * F / M * dt ** 2

  # force at time t_k + 1
  f1 = k_spring * (Q - q)
  F1 = - der_V(Q) - np.sum( f )
  p  =  p + 0.5 * ( f + f1 ) * dt
  P  =  P + 0.5 * ( F + F1 ) * dt

  return Q, q, P, p


def leap_frog_integrator_step(Q, q, P, p, M, m, der_V, k_spring, dt):
  """Leap-frog integrator, time-reversible and symplectic.
  p_{n+1/2} = p_n + 0.5 * f_n * dt
  q_{n+1}   = q_n + p_{n+1/2} / m * dt
  f_{n+1}   = f (q_{n+1})
  p_{n+1}   = p_{n+1/2} + 0.5 * f_{n+1} * dt
  """
  # force 
  f  = k_spring * (Q - q)
  F  = - der_V(Q) - np.sum(f) 

  # half step in momenta   
  ph = p + 0.5 * dt * f
  Ph = P + 0.5 * dt * F

  # position step
  q  =  q + p / m * dt + 0.5 * f /m * dt**2
  Q  =  Q + P / M * dt + 0.5 * F /M * dt**2 

  # force 
  f  = k_spring * ( Q - q )
  F  = - der_V( Q) - np.sum(f) 

  # half step in momenta
  p  =  ph + 0.5 * f * dt
  P  =  Ph + 0.5 * F * dt

  return Q, q, P, p


# Stochastic integrators

def langevin_bbk_step(Q, P, M, gamma, beta, der_V, dt, R):
  """Langevin integrator with the BBK (Brünger, Brooks, and Karplus) scheme.
  p_{n+1/2} = ( 1 - 0.5 * gamma * dt ) * p_n + 0.5 * f_n * dt + 0.5 * f_rand
  q_{n+1}   = q_n + p_{n+1/2} / M * dt
  f_{n+1}   = f (q_{n+1})
  p_{n+1}   = ( 1 + 0.5 * gamma * dt ) * p_{n+1/2} + 0.5 * f_{n+1} * dt + 0.5 * f_rand
  f_rand    = sqrt( 2 / beta * dt * gamma * M ) * R
  """
  # Deterministic force
  F  =  - der_V(Q)
  # Random force 
  # R  =  np.random.normal()
  frand = np.sqrt( 2 / beta * dt * gamma * M ) * R

  Ph = ( 1 - 0.5 * gamma * dt ) * P + 0.5 * F * dt + 0.5 * frand
  Q  = Q + Ph / M * dt
  F  =  - der_V(Q)
  P  = ( Ph + 0.5 * F * dt + 0.5 * frand ) / ( 1 + 0.5 * gamma * dt ) 

  return Q, P


def langevin_isp_step(Q, P, M, gamma, beta, der_V, dt , tau_lng, c1, c2, c3, R):
  """Langevin integrator with the implicit scheme.
  q_{n+1} = q_n + vel * dt
  p_{n+1} = vel * M
  """
  # Deterministic force
  F  =  - der_V(Q)
  # Random force 
  # R  =  np.random.normal()

  # velocity
  vel = c1 * P / M + c2 * F / M + c3 * R

  # update q_{n+1}
  Q  =  Q + vel * dt

  # update p_{n+1}
  P = vel * M

  return Q, P

