{"cells": [{"cell_type": "code", "execution_count": 1, "id": "98db341d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "# import sympy as sp\n", "import matplotlib.pyplot as plt\n", "# import scipy.linalg\n", "# import scipy.sparse\n", "# import scipy.sparse.linalg\n", "# from sympy import *\n", "from tqdm import tqdm\n", "# from integrator_functions import euler_integrator_step, vel_verlet_integrator_step, leap_frog_integrator_step, acf\n", "np.random.seed(0)\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "b82a2782", "metadata": {}, "outputs": [], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "beta  = 1 / kB / T  # kJ-1 mol\n", "M     = 1           # amu"]}, {"cell_type": "markdown", "id": "a048b617", "metadata": {}, "source": ["# Convergence of the memory kernel to $e^{-\\alpha t}$\n", "\n", "The $i$th particle, with mass $m_i$ and spring constant $k_i$, oscillates with frequency\n", "\n", "$$\n", "\\omega_i = \\sqrt{\\frac{k_i}{m_i}} \\, .\n", "$$\n", "\n", "The frequencies are drawn from a uniform distribution:\n", "\n", "$$\n", "\\omega_i = N^a \\nu_i\\,; \\quad \\nu_i \\in \\mathcal{U}(0,1)\\,; \\quad a \\in[0,1]\n", "$$\n", "\n", "The spring constants are written as\n", "\n", "$$\n", "k_i = \\frac{2 }{\\pi} \\frac{\\alpha^2 M \\gamma}{\\alpha^2 + \\omega_i^2} \\Delta \\omega \\, ,\n", "$$\n", "\n", "where \n", "\n", "$$\n", "\\Delta \\omega = \\frac{N^a}{N} \\, .\n", "$$\n", "\n", "The memory kernel is written as\n", "\n", "$$\n", "K(t) = \\sum_{i=1}^N \n", "\\frac{2}{\\pi} \n", "\\frac{\\alpha^2 M \\gamma}{\\alpha^2 + \\omega_i^2} \\, \\Delta \\omega \\cos (\\omega_i t)\n", "$$\n", "\n", "We show that \n", "\n", "$$\n", "\\lim_{N\\rightarrow \\infty}\n", "\\sum_{i=1}^N \\frac{2}{\\pi} \n", "\\frac{\\alpha^2 M \\gamma}{\\alpha^2 + \\omega_i^2} \\, \\Delta \\omega \\cos (\\omega_i t)\n", "=\n", "\\alpha M \\gamma e^{-\\alpha t}\n", "$$"]}, {"cell_type": "code", "execution_count": null, "id": "25282355", "metadata": {}, "outputs": [], "source": ["a       = 0.3333\n", "gamma   = 1.5\n", "alpha   = 1\n", "\n", "# Angular frequencies\n", "def ang_freqs(omega, N):\n", "  domega = N**a / N\n", "  return (2 / np.pi) * (alpha ** 2 * M * gamma) / (alpha ** 2  + omega ** 2) * domega\n", "\n", "# Memory kernel\n", "def memory_kernel1(t, omega, k_spring):\n", "  return np.sum(k_spring * np.cos(omega * t))\n", "\n", "def f(t):\n", "  return alpha * M * gamma * np.exp(-alpha * t)"]}, {"cell_type": "code", "execution_count": 5, "id": "3a5954dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.10101010101010101\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100it [00:00, 167504.15it/s]\n", "100it [00:00, 27374.39it/s]\n", "100it [00:00, 638.07it/s]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Timeline\n", "tmin    = 0\n", "tmax    = 10\n", "Nsteps  = 100\n", "tvalues = np.linspace( 0, tmax, Nsteps )\n", "dt      = tvalues[1] - tvalues[0]\n", "print(dt)\n", "\n", "# Number of oscillators\n", "Noscillators = np.array([20, 2000, 200000])        \n", "\n", "K = np.zeros((<PERSON><PERSON><PERSON>,3))\n", "\n", "for i,N in enumerate(Noscillators):\n", "  omega    = np.random.uniform( 0, 1, N ) * N ** a\n", "  k_spring = ang_freqs(omega, N)\n", "  for k,t in tqdm(enumerate(tvalues)):\n", "    K[k,i] = memory_kernel1(t, omega, k_spring)\n", "\n", "fig, ax = plt.subplots(1, 1,figsize=(5, 3))  \n", "ax.plot(tvalues, f(tvalues), 'k-', linewidth = 2,   label =r'$f(t)$')\n", "ax.plot(tvalues, K[:,0], 'r--',    linewidth = 2,   label =r'$K(t),\\, N=20$')\n", "ax.plot(tvalues, K[:,1], 'g--',    linewidth = 2,   label =r'$K(t),\\, N=2000$')\n", "ax.plot(tvalues, K[:,2], 'b--',    linewidth = 2,   label =r'$K(t),\\, N=200000$')\n", "ax.set_xlabel(r'$t$ / ps')\n", "ax.set_ylabel(r'$K(t)$')\n", "ax.set_title('Memory kernel')\n", "ax.legend();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3)；\n", "\n", "# fig.savefig('figures/memory_kernel1.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "markdown", "id": "30ac73ad", "metadata": {}, "source": ["# Convergence of $e^{-\\alpha t}$ to a delta function\n", "In what follows, we assume an infinite number of oscillators, then the memory kernel can be written as\n", "\n", "$$\n", "K(t) = \\alpha M \\gamma e^{-\\alpha t} \n", "$$"]}, {"cell_type": "code", "execution_count": 6, "id": "326baf5d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100it [00:00, 301965.73it/s]\n", "100it [00:00, 308631.64it/s]\n", "100it [00:00, 365039.51it/s]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["a       = 0.3333\n", "\n", "gamma   = 1.5\n", "alphas   = np.array([1, 5, 10])\n", "\n", "def memory_kernel2(t, alpha):\n", "  return alpha * M * gamma * np.exp(-alpha * t)\n", "\n", "K = np.zeros((<PERSON><PERSON><PERSON>,3))\n", "\n", "for i,alpha in enumerate(alphas):\n", "  for k,t in tqdm(enumerate(tvalues)):\n", "    K[k,i] = memory_kernel2(t, alpha)\n", "\n", "fig, ax = plt.subplots(1, 1,figsize=(5, 3))\n", "ax.plot(tvalues, K[:,0], 'r--',    linewidth = 2,   label =r'$\\alpha = 1$')\n", "ax.plot(tvalues, K[:,1], 'g--',    linewidth = 2,   label =r'$\\alpha = 5$')\n", "ax.plot(tvalues, K[:,2], 'b--',    linewidth = 2,   label =r'$\\alpha = 10$')\n", "ax.set_xlabel(r'$t$ / ps')\n", "ax.set_ylabel(r'$K(t)$')\n", "ax.set_title('Exponential memory kernel')\n", "ax.legend();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3);\n", "# fig.savefig('figures/memory_kernel2.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}