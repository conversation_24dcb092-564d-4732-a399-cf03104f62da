{"cells": [{"cell_type": "code", "execution_count": 1, "id": "143689b3", "metadata": {}, "outputs": [], "source": ["## In this cell we import numpy and matplotlib\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "9c9942bd", "metadata": {}, "source": ["## Harmonic oscillator\n", "\n", "Hamiltonian function\n", "\\begin{equation}\n", "  \\mathcal{H}(x, p) \n", "  = E_k + V(x) \n", "  = \\frac{p^2}{2m} + V(x) \\, .\n", "\\end{equation}\n", "\n", "Equations of motion\n", "\\begin{equation}\n", "  \\begin{cases}\n", "  \\dot{x} &= \\frac{\\partial \\mathcal{H}}{\\partial p} = \\frac{p(t)}{m} \\\\\n", "  \\dot{p} &= - \\frac{\\partial \\mathcal{H}}{\\partial x} =  F(t)  = -\\frac{d}{dx} V(x(t)) \\\\\n", "  \\end{cases} \\, .\n", "\\end{equation}\n", "    \n", "Potential energy function:\n", "\\begin{equation}\n", "  V(x) = \\frac{1}{2}k x^2 \\, ,\n", "\\end{equation}\n", "where $k$ is the spring constant (units: $\\mathrm{N\\,m^{-1}} = \\mathrm{kg\\,s^{-2}}$)."]}, {"cell_type": "code", "execution_count": 2, "id": "19ea2664", "metadata": {}, "outputs": [], "source": ["## This cell is used to define the problem\n", "## The comment on the right side of each variable denotes the physical units\n", "\n", "# Spring constant\n", "k_spring = 1.5   #  N m-1  \n", "# Mass\n", "m        = 0.4   #  kg\n", "# Angular frequency\n", "omega    = np.sqrt(k_spring / m)   # s-1\n", "# Initial position\n", "x0       = 1   # m\n", "# Initial momentum\n", "p0       = 0   # kg m s-1\n", "# Initial and arrival time\n", "tmin     = 0    # s    \n", "tmax     = 10   # s   \n", "# Integrator timestep\n", "dt       = 0.1   # s\n", "# Array with time values\n", "t        = np.arange(tmin, tmax + dt, dt)   # s\n", "# Number of timestpes\n", "Nt       = len(t)\n", "# Number of timestpes\n", "# Nt       = 10\n", "# Array with time values\n", "# t        = np.linspace(tmin, tmax, Nt)   # s\n", "# Integrator timestep\n", "# dt       = t[1] - t[0]   # s"]}, {"cell_type": "markdown", "id": "5aa45db0", "metadata": {}, "source": ["## Exact solution\n", "\n", "The second equation of the motion of the harmonic oscillator can be written as\n", "\\begin{equation}\n", "m\\ddot{x}  + k x = 0.\n", "\\end{equation}\n", "\n", "The exact solution is \n", "\n", "\\begin{equation}\n", "x(t) = x(0) \\cos(\\omega t) + \\frac{p(0)}{m \\omega} \\sin (\\omega t) \\, ,\n", "\\end{equation}\n", "\n", "where we introduced the angular frequency of the oscillation (units: $\\mathrm{s^{-1}}$)\n", "\n", "\\begin{equation}\n", "\\omega = \\sqrt{\\frac{k}{m}} \\, .\n", "\\end{equation}\n", "\n", "The exact solution for the momentum is\n", "\n", "\\begin{equation}\n", "p(t) = - m \\omega x(0) \\sin( \\omega t ) + p(0) \\cos( \\omega t ) \\, .\n", "\\end{equation}"]}, {"cell_type": "code", "execution_count": 3, "id": "ce0b3ef0", "metadata": {}, "outputs": [], "source": ["## In this cell we write the exact solution\n", "\n", "# Position solution\n", "x        =  x0 * np.cos( omega * t ) + p0 / m / omega * np.sin( omega * t )\n", "\n", "# Momentum solution\n", "p        = - m * omega * x0 * np.sin( omega * t ) + p0 * np.cos( omega * t )"]}, {"cell_type": "markdown", "id": "68f1af4a", "metadata": {}, "source": ["## Euler integrator\n", "\n", "The semi-implicit Euler integrator is written as\n", "\n", "\\begin{equation}\n", "\\begin{cases}\n", "F(t_k) = - \\frac{d}{dx} V(x(t_k)) \\cr\n", "x(t_{k+1}) =  x(t_k) + \\frac{p(t_k)}{m}\\Delta t + \\frac{F(t_k)}{m} \\Delta t^2 \\cr\n", "p(t_{k+1}) =  p(t_k) + F(t_k)\\Delta t \n", "\\end{cases} \\, ,\n", "\\end{equation}\n", "\n", "where $\\Delta$ is the timestep."]}, {"cell_type": "code", "execution_count": 4, "id": "a6739b05", "metadata": {}, "outputs": [], "source": ["## In this cell we implement the Euler integrator\n", "\n", "# First, we create two vectors of zeros for the position and the momentum with the number of timesteps Nt\n", "x_euler    = np.zeros(Nt)\n", "p_euler    = np.zeros(Nt)\n", "\n", "# We replace the first entry of the two vectors with the initial and the \n", "x_euler[0] = x0\n", "p_euler[0] = p0\n", "\n", "for k in range(Nt-1):\n", "  F       =  - k_spring * x_euler[k]\n", "  x_euler[k+1]  =  x_euler[k]  +  p_euler[k] * dt / m + F / m * dt ** 2 \n", "  p_euler[k+1]  =  p_euler[k]  +  F * dt "]}, {"cell_type": "code", "execution_count": 5, "id": "b78b6c2f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# These two commands are used to define the font size\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "\n", "\n", "# This command is used to create three graphs in a row in the same figure\n", "fig, (ax1, ax2, ax3)= plt.subplots(1, 3, figsize=(15,5)) \n", "\n", "# The option \"label = \" is used to create a legend \n", "# Plot the position\n", "ax1.plot( t, x,       'r',   linewidth = 2,  label = \"Exact\")\n", "ax1.plot( t, x_euler, 'ks',  linewidth = 2,  label = \"Euler\")\n", "ax1.set_title('Position')\n", "ax1.set_xlabel('t')\n", "ax1.set_ylabel('x')\n", "ax1.legend(loc=\"upper right\")\n", "\n", "# Plot the momentum\n", "ax2.plot( t, p,       'b',   linewidth = 2,  label = \"Exact\")\n", "ax2.plot( t, p_euler, 'ks',  linewidth = 2,  label = \"Euler\")\n", "ax2.set_title('Momentum')\n", "ax2.set_xlabel('t')\n", "ax2.set_ylabel('p')\n", "ax2.legend(loc=\"upper right\")\n", "\n", "# Plot the solution in the phase space\n", "ax3.plot( x,       p,       'g',  linewidth = 2,  label = \"Exact\")\n", "ax3.plot( x_euler, p_euler, 'ks', linewidth = 2,  label = \"Euler\")\n", "ax3.set_title('Phase space')\n", "ax3.set_xlabel('x')\n", "ax3.set_ylabel('p')\n", "ax3.axis(\"equal\")\n", "ax3.legend(loc=\"upper right\");\n", "\n", "# This command is used to fix the margins\n", "# fig.tight_layout()"]}, {"cell_type": "markdown", "id": "317713ff", "metadata": {}, "source": ["## Velocity Verlet integrator\n", "\n", "The velocity Verlet scheme is written as\n", "\n", "\\begin{equation}\n", "\\begin{cases}\n", "F(t) &=& - \\nabla_r V(r(t)) \\cr\n", "r(t+\\Delta t) &=&  r(t) + \\frac{p(t)}{M}\\Delta t + \\frac{1}{2} \\frac{F(t)}{M} \\Delta t^2 \\cr\n", "F(t+\\Delta t) &=& - \\nabla_r V(r(t+\\Delta t)) \\cr\n", "p(t+\\Delta t) &=&  p(t) + F(t)\\Delta t\n", "\\end{cases} \\, .\n", "\\end{equation}\n", "\n", "where $\\Delta t$ is the integrator timestep."]}, {"cell_type": "code", "execution_count": 6, "id": "dc6dd865", "metadata": {}, "outputs": [], "source": ["## In this cell we implement the Velocity Verlet integrator\n", "\n", "# First, we create two vectors of zeros for the position and the momentum with the number of timesteps Nt\n", "x_vverlet    = np.zeros(Nt)\n", "p_vverlet    = np.zeros(Nt)\n", "\n", "# We replace the first entry of the two vectors with the initial and the \n", "x_vverlet[0] = x0\n", "p_vverlet[0] = p0\n", "\n", "# We replace the first entry of the two vectors with the initial and the \n", "x_vverlet[0] = x0\n", "\n", "for k in range(Nt-1):\n", "  F       =  - k_spring * x_vverlet[k]\n", "  x_vverlet[k+1]  =  x_vverlet[k]  +  p_vverlet[k] * dt / m + 0.5 * F / m * dt ** 2\n", "  F1      =  - k_spring * x_vverlet[k+1]\n", "  p_vverlet[k+1]  =  p_vverlet[k]  +  0.5 * ( F + F1 ) * dt "]}, {"cell_type": "code", "execution_count": 7, "id": "3ac9ed84", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# These two commands are used to define the font size\n", "font = {'size'   : 12}\n", "plt.rc('font', **font)\n", "\n", "# This command is used to create three graphs in a row in the same figure\n", "fig, (ax1, ax2, ax3)= plt.subplots(1, 3, figsize=(15,5)) \n", "\n", "# Plot the position\n", "ax1.plot( t, x,         'r',   linewidth = 2,  label = \"Exact\")\n", "ax1.plot( t, x_vverlet, 'ks',  linewidth = 2,  label = \"Vel. Verlet\")\n", "ax1.set_title('Position')\n", "ax1.set_xlabel('t')\n", "ax1.set_ylabel('x')\n", "ax1.legend(loc=\"upper right\")\n", "\n", "# Plot the momentum\n", "ax2.plot( t, p,         'b',   linewidth = 2,  label = \"Exact\")\n", "ax2.plot( t, p_vverlet, 'ks',  linewidth = 2,  label = \"Vel. Verlet\")\n", "ax2.set_title('Momentum')\n", "ax2.set_xlabel('t')\n", "ax2.set_ylabel('p')\n", "ax2.legend(loc=\"upper right\")\n", "\n", "# Plot the solution in the phase space\n", "ax3.plot( x,         p,         'g',  linewidth = 2,  label = \"Exact\")\n", "ax3.plot( x_vverlet, p_vverlet, 'ks', linewidth = 2,  label = \"Vel. Verlet\")\n", "ax3.set_title('Phase space')\n", "ax3.set_xlabel('x')\n", "ax3.set_ylabel('p')\n", "ax3.axis(\"equal\")\n", "ax3.legend(loc=\"upper right\");"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}