{"cells": [{"cell_type": "code", "execution_count": 1, "id": "98db341d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "# import sympy as sp\n", "import matplotlib.pyplot as plt\n", "# import scipy.linalg\n", "# import scipy.sparse\n", "# import scipy.sparse.linalg\n", "# from sympy import *\n", "from tqdm import tqdm\n", "# from integrator_functions import euler_integrator_step, vel_verlet_integrator_step, leap_frog_integrator_step, acf\n", "\n", "np.random.seed(0)\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "b82a2782", "metadata": {}, "outputs": [], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "beta  = 1 / kB / T  # kJ-1 mol\n", "M     = 1           # amu"]}, {"cell_type": "markdown", "id": "a048b617", "metadata": {}, "source": ["# Initial positions and momenta of the oscillators\n", "\n", "We draw the initial positions and the initial momenta from the <PERSON><PERSON>mann distributions:\n", "\n", "$$\n", "\\pi_q (q_i) = \\frac{1}{Z_q} \\exp\\left( - \\beta \\frac{k_i}{2}(q_i - Q(0))^2 \\right) \\,; \\quad\\quad \\pi_p (p_i) = \\frac{1}{Z_p} \\exp\\left( - \\beta \\frac{p_i^2}{2m_i}\\right) \\, .\n", "$$\n", "\n", "Comparing with the Gaussian function\n", "\n", "$$\n", "f(x) = \\frac{1}{\\sqrt{2\\pi \\sigma^2}} \\exp\\left( -  \\frac{1}{2}\\frac{(x - x(0))^2}{\\sigma^2} \\right) \\, ,\n", "$$\n", "\n", "we obtain that the initial positions and momenta can be estimated as\n", "\n", "$$\n", "q_i(0) = Q(0) + \\xi_i \\sqrt{\\frac{1}{\\beta k_i}} \\\\\n", "p_i(0) = \\eta_i \\sqrt{\\frac{m_i}{\\beta}} \\, ,\n", "$$\n", "\n", "with $\\xi, \\eta_i \\in \\mathcal{N}(0,1)$.\n", "\n", "The noise term is \n", "\n", "\\begin{equation}\n", "R(t) = \n", "\\sqrt{\\frac{1}{\\beta}}\n", "\\sum_{i=1}^N \n", "\\sqrt{k_i}\n", "\\left[\n", "\\sqrt{k_i \\beta}\n", "\\left(\n", "q_i(0) - Q(0)\n", "\\right) \\cos(\\omega_i t)\n", "+ \n", "p_i(0)\n", "\\sqrt{\\frac{\\beta}{m_i}}\n", "\\sin(\\omega_i t)\n", "\\right]\n", "\\end{equation}\n", "\n", "or\n", "\n", "\\begin{equation}\n", "R(t) = \n", "\\sqrt{\\frac{1}{\\beta}}\n", "\\sum_{i=1}^N \n", "\\sqrt{k_i}\n", "\\left[\n", "\\xi_i \\cos(\\omega_i t)\n", "+ \n", "\\eta_i\n", "\\sin(\\omega_i t)\n", "\\right]\n", "\\end{equation}"]}, {"cell_type": "code", "execution_count": 3, "id": "2660db31", "metadata": {}, "outputs": [], "source": ["a       = 0.3333\n", "gamma   = 1.5\n", "alpha   = 5\n", "\n", "# Angular frequencies\n", "def ang_freqs(omega, N):\n", "  domega = N**a / N\n", "  return ( 2 / np.pi ) * ( alpha ** 2 * M * gamma ) / ( alpha ** 2  + omega ** 2) * domega\n", "\n", "# Memory kernel\n", "def memory_kernel1( t, omega, k_spring ):\n", "  return np.sum(  k_spring * np.cos( omega * t ) )\n", "\n", "def f(t):\n", "  return alpha * gamma * np.exp( - alpha * t )\n", "\n", "# noise term\n", "def noise_term( t, omega, k_spring, xi, eta ):\n", "  return np.sqrt( 1 / beta ) * np.sum( np.sqrt( k_spring ) * ( xi * np.cos( omega * t ) + eta * np.sin( omega * t ) ) )      "]}, {"cell_type": "code", "execution_count": 5, "id": "7676f421", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.05005005005005005\n"]}, {"name": "stderr", "output_type": "stream", "text": ["1000it [00:00, 139068.44it/s]\n", "1000it [00:00, 23923.98it/s]\n", "1000it [00:03, 318.61it/s]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1cAAAGMCAYAAAA2pFg1AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsXQWYHFXWvS3TPZKZyWTi7hAhWHAJLj8Oi7O4LizO7uJOWBZYXBdbWILLLu4QQoIkQBKSQIi7jkv7/91X9apuvXpVXd3TPfrO903S3tUl793zzrn3+lKpVAoUFBQUFBQUFBQUFBQUWgR/y96uoKCgoKCgoKCgoKCggFDkSkFBQUFBQUFBQUFBIQdQ5EpBQUFBQUFBQUFBQSEHUORKQUFBQUFBQUFBQUEhB1DkSkFBQUFBQUFBQUFBIQdQ5EpBQUFBQUFBQUFBQSEHUORKQUFBQUFBQUFBQUEhB1DkSkFBQUFBQUFBQUFBIQdQ5EpBQUFBQUFBQUFBQSEHUORKQUFBQaFTYK+99mJ/CgoKCgoKbQVFrhQUFBQUWg3PPvss+Hw+KCwshFWrVtmeR3I0fvx46Ci444474K233mrrzVBQUFBQaCdQ5EpBQUFBodURiUTgzjvvzOlnfvTRR+yvNaHIlYKCgoIChSJXCgoKCgqtjm222QaefPJJWL16dc4+MxQKsb+OjubmZkgmk229GQoKCgoKWUCRKwUFBQWFVsc111wDiUTCk3oVj8fh1ltvhREjRkA4HIahQ4ey96P6lS7n6sEHH4Rx48ZBcXExVFRUwMSJE+HFF1+0vAbtiWeeeSb06dOHfT6+/umnn067XWhvbGhogOeee47dxr/TTz89o8/94osv2PteeukluO6662DAgAFsW2tra9lndevWDZYvXw6HHnoou43PP/zww+y9c+bMgX322QdKSkpgyJAhtt+loKCgoND6CLbBdyooKCgodHEMGzYMTj31VKZe/e1vf4P+/fs7vvbss89mBOYPf/gDXHHFFfDtt9/C5MmTYf78+fDmm286vg8/++KLL2bvu+SSS5giNHv2bPb+k046ib1m3bp1sPPOOzOCc9FFF0GvXr3g/fffh7POOosRnEsvvdTx859//nm2bTvuuCOce+657DEkgNl8LpJHVN2uvPJKRhq5AocE9OCDD4Y999wT7rrrLvjPf/7DPg8J1bXXXgsnn3wyHH300fDYY4+x/bnLLruwfaugoKCg0EZIKSgoKCgotBKeeeaZFE4933//fWrRokWpYDCYuvjii43nJ02alBo3bpxx/6effmKvP/vssy2fc+WVV7LHP/vsM8t78Y/jiCOOsHyWDGeddVaqX79+qY0bN1oeP+GEE1Ll5eWpxsZG1/eXlJSkTjvttKw/9/PPP2e/Y/jw4bbvws/F5+644w7jsaqqqlRRUVHK5/OlXnrpJePxBQsWsNfeeOONrturoKCgoJBfKFuggoKCgkKbYPjw4fDHP/4RnnjiCVizZo30Ne+99x77//LLL7c8jgoW4t1333X8/O7du8PKlSvh+++/lz6fSqXg9ddfh8MOO4zd3rhxo/F34IEHQk1NDcyaNSvj35XN55522mlQVFQk/TxUx+hv2mKLLZhyddxxxxmP42P43OLFizPeXgUFBQWF3EGRKwUFBQWFNgPmGWFOlVPu1bJly8Dv98PIkSMtj/ft25eRCXzeCX/9619ZnhLa9kaNGgUXXnghTJs2zXh+w4YNUF1dzcgd2vbo3xlnnMFes379+ox/Uzaf62Tlw5L1+D6K8vJyGDhwILMcio9XVVVlvL0KCgoKCrmDyrlSUFBQUGhT9eqUU05hRARzr5wgEgkvGDNmDPz666/wzjvvwAcffMDUpEceeQRuuOEGuPnmm42KfPj9qBzJMGHChIy/N5vPdVKtAoFARo+jUqagoKCg0HZQ5EpBQUFBoc3VqxdeeAH+/ve/257DKnhIVhYuXMjIEgcWjEB1CJ93A9rnjj/+ePYXjUZZ8Yfbb78drr76aqYIlZaWsqIR++23X1bbLiN9ufhcBQUFBYWOCWULVFBQUFBoU2CFPVR5Hn/8cVi7dq3luf/7v/9j/993332Wx++99172/yGHHOL4uZs2bbLcxwp8Y8eOZepOLBZj6s8xxxzDFK25c+dK7X3pgOQNSR5FLj5XQUFBQaFjQilXCgoKCgptDiwrjqXN0caH/aA4tt56a2atQ9sgkphJkybBd999x0qzH3nkkbD33ns7fuYBBxzAcrN222031msKS7c/9NBDjJChsoTAXK/PP/8cdtppJzjnnHMY+dq8eTMrOPHJJ5+w227Yfvvt2euQ7GE5ecydws9q6ecqKCgoKHRMKHKloKCgoNDmwIIVqF4haRLxr3/9i+VmPfvss6yvFRImtPXdeOONrp953nnnsb5QSHzq6+tZEQjse4U2RA4kXUjWbrnlFnjjjTdYTlZlZSUjeDKbogj8bOxxhZ/Z1NTEiCASqpZ+roKCgoJCx4QP67G39UYoKCgoKCgoKCgoKCh0dKicKwUFBQUFBQUFBQUFhRxAkSsFBQUFBQUFBQUFBYUcQJErBQUFBQUFBQUFBQWFrkquvvrqKzjssMNYZSbsMfLWW29Znj/99NPZ4/TvoIMOarPtVVBQUFBQUFBQUFDo/OiQ5KqhoYGV53344YcdX4Nkas2aNcbflClTWnUbFRQUFBQUFBQUFBS6FjpkKfaDDz6Y/bkhHA6zcr0KCgoKCgoKCgoKCgqtgQ5Jrrzgiy++gN69e0NFRQXss88+cNttt7EeI06IRCLsjyOZTLImj/getBUqKCgoKCgoKCgoKHRNpFIpqKurY2lJfr+/a5ErtAQeffTRMGzYMFi0aBFcc801TOmaPn06BAIB6XsmT54MN998c6tvq4KCgoKCgoKCgoJCx8CKFStYU/pO20QYVaU333wTjjzySMfXLF68GEaMGAGffPIJ7Lvvvp6Uq5qaGhg8eDDbgWVlZXnZdgUFBQUFBQUFBQWF9o/a2loYNGgQVFdXQ3l5eddSrkQMHz4cevbsCb///rsjucIcLfwTgcRKkSsFBQUFBQUFBQUFBV+adKEOWS0wU6xcuRI2bdoE/fr1a+tNUVBQUFBQUFBQUFDopOiQylV9fT1ToTiWLFkCP/30E/To0YP9Ye7UMcccw6oFYs7VX/7yFxg5ciQceOCBbbrdCgoKCgoKCgoKCgqdFx2SXP3www+w9957G/cvv/xy9v9pp50Gjz76KMyePRuee+455onEih4HHHAA3HrrrVLbn4KCgoKCgoKCgoKCQi7Q4Qta5DNpDZPVsLCFyrlSUFBQUFBQUMgtMASNx+OQSCTaelMUFAArigeDQcecKq/coEMqVwoKCgoKCgoKCh0X0WgU1qxZA42NjW29KQoKBoqLi1mNhlAoBNlCkSsFBQUFBQUFBYVWQzKZZPnyqBRg+gYGsukqsCko5FtFRcK/YcMGdm6OGjXKtVGwGxS5UlBQUFBQUFBQaDVgEIsEC3sGoVKgoNAeUFRUBAUFBbBs2TJ2jhYWFmb1OV2iFLuCgoKCgoKCgkL7QrbKgIJCez4n1VmtoKCgoKCgoKCgoKCQAyhypaCgoKCgoKCgoKCgkAMocqWgoKCgoKCgoKCgoJADKHKloKCgoKCgoKCgoKCQAyhypaCgoKCgoJARrr0W4OCDAZYubestUVBon9i0aRP07t0blqa5SE444QS45557Wm27FPIPRa4UFBQUFBQUPGPWLIA77gD44AOAY45p661RUGh9TJo0ifXlwj/s0TVmzBh48cUXLa+5/fbb4YgjjoChQ4daHr/sssvg6KOPNu5fd9117LU1NTUt2pYpU6ZYHn/wwQdZD7FcYfLkybDDDjtAaWkpI41HHnkk/Prrr7bXPfzww+w3YxnznXbaCb777jvoalDkSkFBQUFBQcEzli2zEi0Fha7WbPbHH3+Eu+++G9asWcMIxkEHHQSnnnoqaz6LaGxshKeeegrOOuss2/uRbEycONG4P378eBgxYgS88MILWW9Lv3794PXXX7c8N3PmTNhuu+0gV/jyyy/hwgsvhBkzZsDHH38MsVgMDjjgAGhoaDBe8/LLL8Pll18ON954I8yaNQu23nprOPDAA2H9+vXQlaDIlYKCgoKCgoJnVFS09RYoKLQdFi5cCHV1dYxQ9e3bF4YNG8ZIVCKRMJSc9957D8LhMOy8887G+7ApLTao/eabb+Daa69lahN//rDDDoOXXnop621B9ev9999npI4Dyc32228PucIHH3wAp59+OowbN46RpmeffRaWL1/OSBzHvffeC+eccw6cccYZMHbsWHjsscdYk+inn34auhKCbb0BCgoKCgoKCgoKCoCKztq1rfudffsC/PCD55cjmaioqGDkAbFy5UpGlpBMTZgwgT02depUG7EJBoMwbdo0ZpX76aefoE+fPsw6h9hxxx2ZNTASibDPyWRb8DPOPvtsuPXWWxnBOuaYY6C5uRnmz5/PHhNxxx13sD83zJs3DwYPHuz6Gm5j7NGjh0EecXuuvvpqS0Pe/fbbD6ZPnw5dCYpcKSgoKCgoKHhGNGq9n0oB+HxttTUKnQpIrFatgvYMVISQWGDuEapVSGSKioqYSsNznJYtW2bLd0KisXr1aqisrGTKDwW+FsnJ2rVrYciQIRltCxI6zPs66qij4LXXXmPk6ueff4Z4PC61BZ5//vlw3HHHuX5uulytZDIJl156Key2227M1ojYuHEj2x99+vSxvBbvL1iwALoSFLlSUFBQUFBQ8IxYzHq/qgpXr9tqaxQ6FVBFauffiYQGc48uvvhiqK6uhiuvvJKRDLTMcTQ1NRmqFAXmR4nECoHkDEFtfV63hRMoLJKBf6h+4eO9evWCQYMG2d6DShNXm7IF/v65c+fC119/3aLP6axQ5EpBQUFBQUEha3KFYoMiVwo5QQb2vLYCEhfMKxo5ciS7/8gjjzD1CB/jlQF79uwJVbjqIADtgDJytXnzZvY/EqJMt+XEE09kt/faay+W0/Xhhx+6FrNoqS3woosugnfeeQe++uorGDhwoPE4/uZAIADr1q2zvB7vY25aV4IqaKGgoKCgoKCQNblas6attkRBoXWxePFiplZxKxwCc6+w2h8txb7tttsygiJizpw5sM0229geRxUIiQoSlEy3hZMozOk6/PDDWdVAt2IWaAtEkuf2J7MFYmVCJFZvvvkmfPbZZ6yQBwVaE/E7P/30U4t9EO/vsssu0JWglCsFBQUFBQWFrMlVlu15FBQ6HFARQnVo9OjRlsf33XdfRjquueYadh/Lj2NhB1SvsPgFJRtYURBzr0pKSqC8vNwogIFlzTPdFiQ0lOhhvtUf//hHZi/EIhsyZGsLRCsgEsi3336b5ZthfhgCfwO3NWIZ9tNOO42VmsciHffddx8r1Y7VA7sSlHKloKCgoKCgkHVBC9LmRkGhUwMVoVGjRjFSQ4EV8ZDsYOVAxFZbbcUUpVdeecXyuttuu42VMB8wYAC7jcCCGG+99RazFXLga7BUe7ptQWJFt2X//fdnRSWwOEYue1whHn30UVbIA+2H2FeL/2FvK47jjz+e9f+64YYbmEKHKhiWcBeLXHR2+FKo8ynYUFtby9g4nkhlZWVtvTkKCgoKCgrtAk8+CXDuueb9Rx9Fq1FbbpFCRwMSCmy4i9YyWeGHzoB3330XrrrqKmb5w0qBbqQFVa+PPvrIeAyb8GLT3i+++KKVtlbBy7nplRsoW6CCgoKCgoJC1rZApVwpKNhxyCGHsCa/q1atklbt40Cb4YMPPmh5DPtVPfTQQ62wlQr5gCJXCgoKCgoKClmTqwyrRysodBlgL6h0wAbAIr777rs8bZFCa0DlXCkoKCgoKCh4hlKuFBQUFJyhyJWCgoKCgoKCZ6iCFgoKCgrOUORKQUFBQUFBwTOUcqWgoKDgDEWuFBQUFBQUFDxDkSsFBQUFZyhypaCgoKCgoOAZilwpKCgoOEORKwUFBQUFBQXPUNUCFRQUFJyhyJWCgoKCgoKCZ6iCFgoKCgrOUORKQUFBQUFBwTOULVBBQUHBGYpcKSgoKCgoKHRqcrWhYQPUR+vbejMUFBS6ABS5UlBQUFBQUOi05Grm6pkw8J8DYdA/B8Gq2lVtvTkKCgqdHIpcKSgoKCgoKHTaghaTv54M0UQUqpur4S+f/KWtN0ehi2DTpk3Qu3dvWLp0qevrTjjhBLjnnntabbsU8g9FrhQUFBQUFBSyLmiB5CqVgnaLquYq4/aHv3/Yptui0DkwadIk8Pl87C8UCsGYMWPgxRdftLzm9ttvhyOOOAKGDh1qefyyyy6Do48+2rh/3XXXsdfW1NS0aFumTJliefzBBx+E/v37Q64wefJk2GGHHaC0tJSRxiOPPBJ+/fVX2+sefvhh9psLCwthp512gu+++y5vr2mvUORKQUFBQUFBIWvlColVUxO0W/Qq7mXc3tS0CZKpZJtuj0LHRiqVgh9//BHuvvtuWLNmDSMYBx10EJx66qmwZMkS9prGxkZ46qmn4KyzzrK9H0nCxIkTjfvjx4+HESNGwAsvvJD1tvTr1w9ef/11y3MzZ86E7bbbDnKFL7/8Ei688EKYMWMGfPzxxxCLxeCAAw6ABuILfvnll+Hyyy+HG2+8EWbNmgVbb701HHjggbB+/fqcv6Y9Q5ErBQUFBQUFhazJVXvPu9rYuNFyvyHajjdWod1j4cKFUFdXxwhV3759YdiwYYxEJRIJQ8l57733IBwOw84772y8LxqNQkFBAXzzzTdw7bXXMrWJP3/YYYfBSy+9lPW2oPr1/vvvM1LHgaRk++23h1zhgw8+gNNPPx3GjRvHyM6zzz4Ly5cvZySO495774VzzjkHzjjjDBg7diw89thjUFxcDE8//XTOX9OeEWzrDVBQUFBQUFDo+OSqlykQtWtyhVUDS8OlbbY9Cs6Y+MREWFu/tlW/s2+3vvDDuT94fj2SiYqKChb0I1auXMnIEpKpCRMmsMemTp1qIzbBYBCmTZvGLG4//fQT9OnTh1neEDvuuCOzBkYiEfY5mWwLfsbZZ58Nt956KyNYxxxzDDQ3N8P8+fPZYyLuuOMO9ueGefPmweDBg11fw22MPXr0MMgjbs/VV19tvMbv98N+++0H06dPz+lr2jsUuVJQUFBQUFDoMsqVKsnefoHEalVd+67oiIoQEgvMPUK1ColMUVERU1d4jtOyZcts+U5IEFavXg2VlZVM+aHA1yKpWLt2LQwZMiSjbUFCh3lfRx11FLz22muMXP38888Qj8eltsDzzz8fjjvuONfPTZerlUwm4dJLL4XddtuN2RoRGzduZPujT58+ltfi/QULFuT0Ne0dilwpKCgoKCgoZF3Qoj1XDMSclA2NGyyPKXLVfoEqUnv/TiQ0mHt08cUXQ3V1NVx55ZWMZKBljqOpqclQpSgwP0okVggkZwhq6/O6LZxAYZEM/EP1Cx/v1asXDBo0yPYeVJq42pQt8PfPnTsXvv766xZ9TmeFIlcKCgoKCgoKnVK5QiKFZdjFxxSyB8b/6Ajr1y/3n52JPa+tgMQF84FGjhzJ7j/yyCNMPcLHeGXAnj17QlWVWaWSA+2AMnK1efNm9j8Soky35cQTT2S399prL5bT9eGHH7oWs2ipLfCiiy6Cd955B7766isYOHCg8Tj+5kAgAOvWrbO8Hu9jblouX9Pe0SELWuABxeQ/lC0xIfCtt96yrVTdcMMNrHoKrgagTxOT/hTswMIraGt955223hIFBYXOFoBfeCFaUAAikbbeGoWuSq5E1QqhyFX2QFI1YgQACiKffQZdDosXL2ZqFbfCITD3Cqv90VLs2267LSMoIubMmQPbbLON7XFUgZCoILHIdFs4icKcrsMPP5xVDXQrZoG2QCR5bn8yWyDG1kis3nzzTfjss89YIQ8KtCbid3766acW+yDe32WXXXL6mvaODkmusOwjMn+sgS/DXXfdBQ888ADzv3777bdQUlLCSjiiL1bBClSx77wTK9Vgw7u23hoFBYXOgieewBVdgMcfx8pPbb01Cl2VXNU023sHKXKVPbDX7dq1AIkEKhjQ5YCKEKpDo0ePtjy+7777MtLBgTHnL7/8YlOvkCRgRUHMvaJ9rbAABpY1z3RbkIhQoof5Vv/973/ZdzspV2gJRNXN7Q+JmswKiOXikURivhnmh+EfWiA5sHz6k08+Cc899xwrqHHBBRewmB2r/uX6Ne0aqQ4O/AlvvvmmcT+ZTKb69u2b+sc//mE8Vl1dnQqHw6kpU6Z4/tyamhr22fh/Z4bWoUT7mzGjrbdGQUGhs2Dffc2xpV+/tt4ahVxixAjr3IF/Tz+dapeYtnxaCm4Cy9/Ts9rpxnYAHHpobq7rpqam1Lx589j/HQl/+9vfUmPHjrU9/vrrr6d8Pl9qxYoVxmM77rhj6rHHHrO87vnnn0/179+fxZdXXnklewz3QXl5eWr69OnG65555hn2mnTbst1221kea25uTpWWlrL3Ll68OJVL4GfK/nBbKR588MHU4MGDU6FQiO2DGZLgMlevyQfczk2v3MCH/0AHBtoCcbUAO0VzmRTlWUwapNIrdrDG+/fff7/0czABEP84amtrWSIgriyUlZVBZ4XPZ97++WcAvYqogoKCQouAaji1G6NC/te/tuUWKeQKmIqxYoX1sYce0myg7Q2fLv4U9nt+P8tjDxz0APx5pz+32TZ1ZIwZA8ALtu25JzaWze5z0EmEDXfRWiYr/NAZ8O6778JVV13FLH9YKdAJjz76KItjP/roI+MxbJ6LTXu/+OKLVtpaBS/nJnKD8vLytNygQ9oC3YASJUJWwpE/J8PkyZPZDuN/sgornR2hUFtvgYKCQmeBGC/97W9ttSUKrVEtsL3aApvipmWJQ9kCs8fvv5u307RB6vI45JBD4Nxzz4VVq9xLy6PN8MEHH7Q8hv2qMMVFoWNCVQvUgc3K0OMpKlddCclkW2+BgoJCZ4FeWVihE6KurgORq5giV7lEPG7e7ti+p9YB9oJKB2wALOK7777L0xYptAY6nXLFyzRmWsIRO2KjxEf/uhowQVVBQUEh10GYQuc6rrJWPO2WXCnlqlUVTAUFhU5IrtAjiSSKlnBEFQqrBnaUEo5tBUWuFBQUcoXa2rbeAoV8oJ7wEtqSp92SK6Vc5S1GUORKQaET2QLr6+vhd2L8xcQzrMuP5SWx6RnKsLfddhuMGjWKka3rr7+e1eznRS8U5JK+IlcKXs6Zv/8dGx4C3HorKr5tvUUKHYlcYc0gdc50HksgpjZv2NABlauYIlfZQOxXp8iVgkInIlc//PAD7L333sZ9nit12mmnwbPPPgt/+ctfWD18TCTEBmu77747fPDBB522Ik22EAdGRa4U0gELF2HTaQ6Vb6uQCbnCwFyRq85DrtBpP3eudltmFWwPaIzZN6wuIkkaU0gLRa4UFDoxudprr71Yp2i38uy33HIL+1PwZu9AKHKlkA4ff2ze/sc/FLlSyJxc9ezZFlujkC9yxdGRbIHN8eY22ZaODkWuFBS6aM6VgncocqWQKUaObOstUOjo5Eqh8xzXDkGuJLbASEJgCQqeoMiVgoI3KHLVhaHIlUKmEC1dqhSvggx4Xihy1TlBjyGqkIFAx1OuInFFrnJBrsT7CgoKGhS56sJQ5EqhpeW1XfpyK3RhYNAVi9kfV+Sq44Mew9JSgJKSdk6ulHKVMyjlSkHBGxS56sJQ5EqhpeSKVwpTUPBCovJNrlAxa7LH0gqtQK7aa0ELKblSylVWUORKQcEbFLnqwhADHUWuFDIlVzJ1QkGhubn1yRWem7vvrvVe+uST/H1PVwe1eyK5Ki7uOLZAH/jY/6qgRXZQ5EpBwRsUuerCECdDRa4UMiVX4n0FBZFc+f3Oanku8fbbAN98o41rp56av+/pSkgmNaI6f76cIJeVdSxbYPfC7l3GFvjppwB77gnwwgu5+0xFrjLDpk2boHfv3rB06VLX151wwglwzz33tNp2KeQfilx1YYgDpSJXCumglCuFTMcWWno9n8rVzz+bt9esyd/3dCW8/DLA/vsDbLstwPr17rZAPObtcQ6hfa4qiiq6jC1wv/0Apk4F+OMfc/eZilyZmDRpEmv7g3+hUAjGjBkDL774ouU1t99+OxxxxBEwdOhQy+OXXXYZHH300cb96667jr22pqamRdsyZcoUy+MPPvgg9O/fH3KFyZMnww477AClpaWMNB555JHw66+/2l738MMPs9+MvWV32mkn+O677/Lymlx9Tz6gyFUXhhgYt8eJUaF9QZErhUyDsB49Wke5WrLEvM2tagotw0knmcfz0Ue12zSnrajIJFftVb3itkC/zw/dQt26jHKVj6quqlqgBuyz+uOPP8Ldd98Na9asYQTjoIMOglNPPRWW6ANRY2MjPPXUU3DWWWfZ3o8B/sSJE43748ePhxEjRsALWciMfFv69esHr7/+uuW5mTNnwnbbbQe5wpdffgkXXnghzJgxAz7++GOIxWJwwAEHQAO58F9++WW4/PLL4cYbb4RZs2bB1ltvDQceeCCs56szOXpNrr4nX1DkqgtDkSuFTKFsgQpeQIOu7poTyzUXKxegzhthoVghh/MFnTdCoQ5ArnRbYFGwCMKBsKFcYVDaVZCr604pVxoWLlwIdXV1jFD17dsXhg0bxkhUIpEwlJz33nsPwuEw7Lzzzsb7otEoFBQUwDfffAPXXnstU5v484cddhi89NJLWW8Lql/vv/8+I3UcSCi23357yBU++OADOP3002HcuHGMqDz77LOwfPlyRuI47r33XjjnnHPgjDPOgLFjx8Jjjz0GxcXF8PTTT+f0Nbn6nnxBkasuDEWuFDKFUq4UMg3Cystbh1ytXClXy9zwr38B/OlPAOvW5W2zOg34tU4DaiRXVCVsjxUDuXJVVFAE4aBGrlKQgniy864MiXN5ro6LeP3iOZFrjoqCzsCBrftHRCRPQDJRUVHBAnbEypUrGVlCMjVhwgT22NSpU23EJhgMwrRp09jtn376ialeSFgQO+64I1O0IhnKgbgtaHk7++yzoaysjBEsRHNzM8yfP1+qXN1xxx3QrVs31z8kTenAbYw99AEXySNuz37oSdXh9/vZ/enTp+fsNbn6nnwimPdvUGi3UORKIVMocqXQXskV/U4v3/PbbwDnnKPdXrVKK4ih4AxOqug1X1DQMZUrbg0sCBRAZ4TYwDtX7QlkcT+eD0iycwXsnYjXY3sGKkJILDD3CNUqJDJFRUVMGeE5TsuWLbPlO2Fwv3r1aqisrGTKDwW+FgnB2rVrYciQIRltCxI6zPs66qij4LXXXoNjjjkGfv75Z4jH41Jydf7558Nxxx3n+rnpcrWSySRceumlsNtuuzFbI2Ljxo1sf/Tp0weSqSSz4iLw/oIFC2yvocjkNbn4jHxDkatOgl83/gr3TL8HjtryKDh41MGe3qPIlUKmULZAhZbYAvPZg4qOZ16+Z9Ys8/Z//5ufberoCATMecFJuWr35EqiXHFrIM/B6myorm49coXnQy7JVd++ufusfH0nEhrMPbr44ouhuroarrzySkYy0DLH0dTUxBQlEZgfJRIrBJIzBLX1ed0WTqCwSAb+ofqFj/fq1QsGDRpkew8qTVxtyhb4++fOnQtff/217bnlNcshtDYEw7oPgx5FLfuejgpFrjoANjVugi+WfgH7Dd8PygvLLTaYyy8HwOv02+FXwf9++x88OetJWHflOuhd0jvt5ypy1fnx1lsAF10EcMYZALfe2vLPU8qVQntVrmjQ7yWYzGVA2FmBypRIrlpLuapqqoLq5moYVjEsJ8pVcUExFAbNYLcz97pqbXKVS/zwA7R7IHHBXJ6RI0ey+4888ghTj/AxXhmwZ8+eUFVVZXsv2gFl5Grz5s3sfyREmW7LiSeeyG7vtddeLKfrww8/dC1mgbZA/HPDvHnzYPDgwdLnLrroInjnnXfgq6++goHoq9SBvzkQCMCK1StgxIQRsLhqMZSFy2DdunUsN42+Bh+jyOQ1ufiMfEPlXHUAHPXyUfCHV/8Ap79troogzjwT4NVXsYwnwP8+X208/tSspzx9riJXnR9HHaVZLG67LTeVnTo7ucLg/6mnAFrBkt0llSu03uXL8qPIVe6B5ImjNZUrJFbbPL4NjHxwJLz6y6tZf04imYBoIupoC+ysaE1y1dUqBi5evJipVdwKh8DcK6z2R0uxb7vttoygiJgzZw5ss802tsdRBUKigqQg023hJApzug4//HBWNdCtmAXaApHkuf3JbIFYBAaJ1ZtvvgmfffYZK+RBgdbECdtOgO+//t54rKqxCj799FPYZZddjNfgduFj1GKYyWty8Rn5hiJX7RxoXZi6fCq7/daCtyyrbR9/TF64zlwJ+XSJeTK5QZGrroVcBD6dnVw9/DDA2WcD7Lpr+/f9Z4PVqwEef1z7vy2UKwRp79Km5IoSB0qusRGxsrvaCagX5SpXhRPeXPAmsxZh3sZxrx3H/m9pA2GZLbCzoiMrV+0dqAihOjR69GjL4/vuuy8jHRxY8vuXX36xqVcY4GNFQcy9on2tsAAGljXPdFuQRFCih/lW//3vf9l3OylXaAlE1c3tD4mazAqI5eKRRGK+GeaH4R9aIDlOP/90eOvFt+CdV96BJQuXwGV/voyVaseKfRxYHv3JJ5+E5557jhXduOCCCzJ+TS4+I59Q5KqdY2m1tbP3D6s1zZxUvtTgM5nR+gZvNfwVuepayAe56mxB6JVXWhuodjYgsTn/fIDjj8/v99AgrKwMwOcz72MPx1yPNfh5yWRmwaRsG1Dp3W03DCJyu30dFZSA8iCaBtMYf9FqgbnqY/bxYrpyCPDNim9alG/V1ZWrXJFeRa40G96oUaMYqaHAKnRIdrByIGKrrbZi5OaVV16xvO62225jJcwHDBjAbiOwIMZbb73FbIUc+Bos1Z5uW5BY0W3Zf//9WSEHLI6Ryx5XiEcffZQRQrQfYl8t/of9pDj2OWwfuOT6S+Dxux+Hkw84GebOnssqItLCEscffzzrEXbDDTcwFQ+Vskxfk4vPyCdUzlU7x6KqRbZJZrdBu8OOOwov9JmRxYbGDS0mV1iF86uvAI44AqC0NIsNV2h3yEXg09mVK6fS3m0BtDOd/MbJUBepg5f+8BJ0LyT+uizx7bfa/5Ic5JyCBmHhMADmdVPCs2FDbhPXxQAPvx/Jlt9l+VAWFOpVkeGJJzSFr6tDZgvk/+NzGPvROMVD9WZP+HLpl5b78zfMh90H795y5YqSK6VcZQxFrgAmT57M/kRgIQlUpSgwqL/qqqsYacJKgYhTTjmF/VE888wzrBQ77YmFzYgnTZqU8bZgOfhasVxkjuClNxy2ODjujOPYH1/UGNd7nO11aC/EPzeke00uPiNfUMpVO8fvm3+33F9WvcwIHCxIBYybGxs3eroInMgVvnWPPQD++EeAP/+5BRuv0K6gyFV60FzitiZXT858El6b9xp8uOhDuOmLm1r8ebYxoxXJFf5RrFmT2++TBXjpimeI72nN/dNRQBfnp07V5ga+3/hzY8aYr5GkmGQMJD1r6q0nyMLNC3OjXAW7pnKlyFXb4JBDDoFzzz0XVqXxmKPN8MEHH7Q8hv2q7rrrLuhIQPuu2D+uOd7cpRp2cyhy1c6xaLNVuaqOVMuD5KhpfMeTG6ssZUuucADlK5DPPZfNVit0FXLV2WyBpPARrFjRllsCjFRxvDD7hRZ/3u/WdZpWJVeiuyXXOV8ykn/NNQBXX+28ACAGhfmsZNgZlKuNGwEeeMCqXCEwp52T5zlzWv6dK2vtqxpZkyuiXGG1wK6iXIlkSpGrtgP2gpKVQ6fABsBbbLGF5TFsKIxqVkeCrDF3ClIQS3ayVVgPUOSqg9kCkTTV1UleGLX26/BiDXQiV51NjaCrefvvj6tJ+e23016hlKv0GDDA2mS2vQAnqJZg/XoAYe7O67GjRAUDb/G7WkO5uv9+gDvvBPjHP7T7OG4edBDAoYdqOSjie6TjaheHWPTj0kvtyhXmXfHc/oULAe65p2XfuaJ2RVoHh1d0VeVKJEGKXCm0BmKJWEaPd2YoctXO8ecd/wy37HWLcb+mucYbuWpIT67EQbGzk6ubbgL45BOA994DuP126HJQ5Co9aIEkbDvy0Udtty20ME221dI4MIdIRD4XGETlSjxPcq1cuQV4vL/b3/4G8OGHAO++C4AOHPE9qMwopIeoXCFoxWdsDYI5ddliRY2cXGVjLXLLuerMfa7Ec1uVYldoK+UKoZQrhXaHA0ceCNdPup5ZGlyVq1hJzpSr1lqJmj8fYO+9tcm4NYA5AxxffAFdDrlYme/stkDx92AT5rYCtUKx6z6S/QGU9XTKpw2uPZEr/jtJlWRWsVB8T0sIQWeFbPGE7zdKrm680bq/X3ght8oVEqFNTZsy/qzGWKOzctUJbYGYN4hjmEh4VLVAhdYAJVFFwSLzcaVcKbRX8EphGGTJc64yV67amlwdfrhGclBFmj07/99XZF7ryhaYJTq7ciX+vrbKw2mINsDmps1pg06v6NevbZUrsVhErlUiL2MWKpEcPXrY34PWSQUrxH2EVR/5NU8J+9ChAJ9/bt5fuzY3ytWEPhOM26tqV7XMFihWC+xktkBsmbTllgBDhmj2zI5iC+yKxQ66gnJVrAsCHVG5ysU5qchVByRXXmyBVc3WxnXtMeeKJtgvWZL/71PkquWf0dXIFf4+DFowf2fGjNbbjtqIvZQuWoJzqVy1JrlyIzq5QLoAD5+n21RRkV65Un3/7Nc3knSZciWeYy1RtNc1rDNuT+w30bXQRUa2wGARFAYL86pc4WLMMcdoC4e0Gjbuj3zn9OEiJZIqVIXFVgvtgVzNmmUl3VghD9GYK1lNoc1BFapiQq6wrUhHAj8n+TmaDVSfqw5GrhpiDVBdg7O+WXpdRq5kwVl7U65auwyyIlct/4yuZgvEa+SKKwCeekpbtUfFpcTqwM0L6qP2g1UTyZ5cyYhCVyJXYll9PIbpyBUe+4AwzHY1iHMEVn2UKVdivmJLxgWaa7htv20BfmoBuRKVqzwXtMCesG+8od3GYip33AHw668Au+6qnfMXXADwyCOQF7hVN21rcvX66wB/+IPWMxMrEXfvjtdWALp37w7rdcm4uLg4bdNchfaNaCQKoF/7/oTfuN3c1AzNhc0dQrFCYoXnJJ6beI5mC0WuOghoA9GN1XiSljiWYve6yt3WypUbuUK1AKt8jR0LcNJJufkORa5a/hldUblCYsVXpRctAphgOpXyhrpoXU6VK1mw++qrWnXE3r0h56BBGJJSEZsyT59xRboATzxPZUGiaAvEz5Rte1eCuF8bGuQFLfJBrsrD5TCiYkTOlCtcSQ8FQnnNA8FiSWKOLxZR4YsJjz6qzWv5WKCRqdPthVwhsUKgevevfwFceaV2v6/eSZwTLIWODcz1b4xqqk+oLgQbazX/d22gFlLVHcf+icSKn5vZQpGrDgKcaDg2VMnIVbeMV7m9KldIfPTm4nmDaHHlagFihx0ARo1q+XcoctXyz+iK5IpCpsLkA7LiFV5612WiXE2eDPDOOwA//2zvQ9XZlCvxekeiLI5pMnLV1SGe/9TqJgbzdJG3JeSKF2PqVdILBpaZjedW1mVGrl58EeDRBw4DGP8ywKAZzBYY8AfyalWi+6eszLmfWmuTq1zljuaiWiC1R6JS1a9fP+jduzfEOttk0gUx+X+T4atlX7HbM86eAed8dQ6z+eK1PPUMUlGsHQOtgC1RrDgUueqAytXmGsmkkEdyhYMnJSatQa44sUJ8803uyVVXaBgqqoHKFpge4u8R0wFaK+BuDVsgb/qKxKPYtMe3CrnC/YrXYK6UoXTHReyrhd9NlRbZaxS5ss8RlKTmQ7lCwsMXEXqX9LaSqwyVq5NPxn9HAHw7HeAmH7MFit/VGuRKvPbyxSHc0kNy9Z25KGghm3sxmM1FQKvQtlhUtwiWNSxjtytLKyEVTLH7KxpXQDAUhKC/61AOVdCiAypX1bWSmSvazULA0lmIMLl05kxvtsDW6GPhlnNVbv70FoEGeV0hcBIDHKVcZf77xEIrrfV7W8MWmK+GvuKY4bSifvbZufu+dNezeO5jgCe+R5ErO9z2QT5yrmiVWyRXOKfxks6ZkCvbfJLSCloU+AsyJlevvALw7397ywtGO7s4b4nkKl/nlZtylatxS0aMvPweqhJ3RddIVwHP9S/Cay1QAP1K+xl9GmkuZVeAIlcdBCUh00dQK6s6lAhDr+JexspAuoIWe+0l+QgH5QpzM/INt8pcubJQdDaVJR3yQZK7GrlavLhtAm6ZLTAfyhVi5EiA336DnCISTRkBNwZWaNES8Z//5C7QSndcRAUyH+QKg+/21oh46dLs+3fh73EjFPlQrmh/RpzP0DbG1atMSrHbxqWG3ky5ojlXXsgV5ksdfzzAaacB/Pe/6b+XnjNcuWqtMdONXOVq3MpWuaIKtSJXnRc87iwLayd//279jedW1+W4uWE7hyJXHQS0IVt9nc9udUsGoDRcapzU6QIxWVlYJ3J17rlahZ98wi3wz1UbjK5GrvLRs6mrkSsRnc0WyPHnP0PO+pzs8cwe8POqeZaA74QTNHvv7rvnZ39mmnM1ZQrAM8+4Xx+ZbBuSkJ12wgR9rTJaewC2DhgxQut7JFZL9IJ017abcpVtGXu6uo3KFYKTK1RzvVTBlV7Hm0azOTRTcoXlzTluuQUyAp+bW8sW2BrKVbbkqqtZ8rsqbOSq1CRXa+ryYJFox1DkqoOA9gxoJoFCt24680gGoVuom2EfzMZC5FYtMN89ftzIVa4mhs5GBDL9vfkgV52NsKb7PZ3RFoioSt8WzxO+Xv41+0vGtUi7IKSNT1gwY5ddAAYOzGy78qVc5eIzKaZNA/jhB20M5ZXR2hpYkQ1JHxLLq6/O/e/PtXKFDeXPPWQiwNS/SskVtQbO3zAf9nluHzh8yuFSu6DtOkVylYVyRRchuRKV6Xyqcq5UMamuACxjzslVeWG5jVxlolx1hh6Dilx1ENBk3CgZKAuLdN9GKqCRK/2kxlXuTLtMu/W5akEvtYwH7XypI+LntkZvrbaE+Hu92AIxCMU+LbTZo9tndjbC2lWVq1xZbxdsXKB/oT5gBKw77NJL24ZcZRPQZWKjpYUM2gvo9mdj+2yJcpXNcd17b4Blv/YA+PROgJhmc5eRK7w29n9+f/h86efwv9/+Bwe+cCAkkgmmzuEiIE57tu+vHWgrxR5NRjM6rtijKRfkqqPmXOF8KfscL9cJtQUq5apzojHWyHKrqHLVT8+5yoRcffWV1h7k//4vd66ltoAiVx3QFkgH51ChzhCSAYtyhfac5nhzzpSrfJMrOuCKZZpzFYCJvysXBR46m3KFJfCPOQbggAPkA1tXJ1etplxFcqtcpSNXuaoWiIqC9oVapOcLWHcYWue23rrzKVftcaW1v7loDKu8pyt5PtdzqVzZxppEWKpcLa9ZDq/+8iqsqjN/0LwN8+D1Hz+BMWM0dfTllyXbngx6sgWiUoXNf7FFAb/vlVyJ5wvfB601ZroV2+Pfie0Gnn3W3nbAC+jvo4sxSrlSEBf/yji56maSq7X1Diu2AiZN0mLA998H+PJL6LBQ5KoD2gJjZDArCOkjdzIIJQUlhnKVzUq3m3KV76CSrn6JDUbzpVx19kE+m5yrxx6zluhO95ldzRbYagUtJLZAr/km2fyuXJGrBZu4chWSKleILbf0vl1tqVxlcqzzdR2gGoPFQLJZCGopuUr3+3OpXNm2L1FgkKuRPUYaD/+26Tf49+x/295/x0MrjX104omS708U2GyBsibCDz6oWSgPO0xT+zIhV6K1lm9Da9kC3Qg+/04sznHGGQAnndSyOZruC1XQQkGcn8okytWa+sxzrnLdbL41ochVB7QFxuI+YzLzBUxbIBIwflJns9LtRq7yPSC2Brly69nSGdHSghbVkp61XV25agtbIF9YkRGu9mYL/HXjr9qNpCZrJP12z1Auqsq5HZdtt+0c5AptWPvvr/VruuCCzN/fUrdBaypXuJhjQTLIGo8itqjcwnh4xsoZRpPS4RXDoUdRD3b7t40L3bc9GWJl2NMpV9dea95+4QXrmNnN2krSBnHeao/kCvPaEJ9+2rI5muafeblOaBsUZQtsW+BCBlaAznVlUwu5CmknCFp7/T5/1uQq183tWxOKXHVA5Soe9Rkrh36/PqKmAlAYKLL0w8pWuZIN/qKtBvO5Pln8CbNk5AJ04BbzF/JlC+zsg7z4e7EHy+zZ3t+vyFX7KGjBrRUyq2B7I1frGtbpX6gFsUlfc6uTK1yZb21ylQ9bIPYiXLDADPQzhZNNrT3mXNlywpJB6Fnck90cVD7IsMVPXT7VyOs4fPThsPfQvdntpkSD5e3i9wehkJV1T0eu6HXwq75O4JVcOdnZWyvnKh25amleYEuUK2r77OzzbnsGVmzF1hvHHQdw8MH5I1fluoMq4A8YCnQ21QIVuVJo1ZyreMxvTm6cXKH0HiixkKtMbURuypVIrp6Y+QRLKt7pXzvB4iqhGVAWoAOumCCbL1tgRx3kvRYqkQU4u+3mfZJFmwt+1/9+/R/cO/1eWLR5kbIFtpJy1RQz2QCfnCKJiNTKlIvflQtyhQnNRp6nTq7ivsZWJ1fjxwPcd1/r5lzl4zrgeT/ZQtz+TAlmptUCac5PpvtDVH26h3obPRtx5XuLnqZ6xXHQyINgn2H7aHd8Sdc5A8kVIh25opZVkVylC/ScyJW4L35Y8TO0BbnK1BqK7VcwCMcctJaSK3o8Gqw8WKEV8eabZtyD1U3zbQuki4O48MYXRrxCkSuFVrUFxuN+c3Ij5CrsK7bmXDnYAp1ic6+2QAy4z3/3fMO+9PB3D0NLQQdu8fuVLdDElDlToMddPeCS9y9J+1rZfsO8BKzG41W5uu2r2+Dwlw6HKz66AnZ4cgeIxBKdTrnCwBvVDuznlm7ib63fy0kKBoP0ms7WGtgaBS02NeoRcsq0BcagodXJFS46XXIJwP33d2xbIFWZs7H4tZRc0XOdFiRwUq5aQq5ElbxHWFtQ4JjQZ4LlPi4i7jV0L9h54M6eyFUgVeSJXJWbl5qtt6PbNYS/99575a9viFhX8d6c10LW3Erk6qKLNPsY5qB9+23uyJWsx6ZC6yCfTc4dyVVpP6PI2sbGzDZAkSuFVrUFJuJy5arAV2TNuXKwBTpNfF5tgT+u/dHy3JsL3oRMIZZBdyNXuQpcOrpyhYPTSW+cBNXN1fDAdw+w/11f77DfnJLjxdd/PHUz3PKV2TmzqrkKGoSd1hnI1Z/+pFXQevLJ9KVfW025imuRcGGwEEpDZiSTrTWwNarZbWraZC3Djjf9TTa1LR/kio4fPOin3+OmXFVUtPxYZ1K23SvoteWmLL79NsC4cVoxhnyRK0o6nAgfBkKcYGV6XMViEBUhK7k6buxxlvtHjTkKwsEwjOs1TlO4BHIlfn8gpSX9cDXMiVzRfeT3e7+GkISIlc34NqyqsVZJm7/u94xX8PNBrtK1Ivnf/6x93JzIlZdzn55LrTWG5gOReB4u9FZEPgtE0MX8MolylY01sD1WYe3S5Oqmm25i/mr6tyXV+zu4LTARC5hBhM+cRcL+YmvOlYNy5TS4eVWuvlnxjeW5pdVLWeCfCcSgnA7Q+bIFdvScK8xxo1i4yZrELcIpwMHcKxlE1eb+v/eA+KrxlseSCX+nsgUimXruOe+vb63AgCtXeN1byFWWylW645SLnm+GcsUrBSL8MZs9OVtyhcdq+nT56itW1ePo1cv+PW7EAsfRsWNbdqzzoYLTwMKN9B95JMC8eQAXX2x9XS5tgTJyRSvAcfB93lLlqncRKXUIAAeMOAAGlw9mt7sXdoe/7fY3dhsJ1vjeOEal0ihXGrmieVcyckVJuEjI3a6Rv2p9jy3g+6C60XrNNr1+P0x+MPP8k3Rw2+cycpXJvLphQ+6Uq466IHf9Z9dDyR0lcM2n10BnIVe57PWZzhaYTVGLm27quC1zOiW5QowbNw7WrFlj/H399dfQWZSrZCJAbIHmiBoSbIFOOVdOK01elavvVn1neS4FKVjfkFnjDHFARqLz45of4dHvH4X6pmirKFcdzRb4w2qrSRrLErvBaRJzahAstcRNvRp84IMXjtIz6pMkYu3AEyVHplaZ1rYFonJFJ6p8KVc5IVdNEnIViNoU9GzJFbYJ2HVXLadKHMN+/90kSoMG2b/H7VpHkiCzvWVCrvKxUEOPmdfjQwORfCtXstLk2ZArXCTauDnmSq4KAgXw8R8/hgcPfhDmXjDXkoO1Td9tkDZZXi9+vz9plqvDqoFeyJW4v9yuoT597I/xbWiMCN8TLYPrLhmQUXGhXChXa9ZkNpbRawIXNHJJrjpac1hsUn3b1NsgkUrA5K8nQ0O0oVOQq1wqQ5aCFmFzwKDl2L32uqJVRK+/HjokOi25CgaD0LdvX+OvZ0+t8lBnyLlKxoNGIJFyU64i+VGuvl/9ve35TOVeWyW7hibY/Znd4U/v/Qlem/1f19d2VeVK9CunI1dOAY44ybqSK38cdhu8G5w84WQ4estjjFyadN+RS8yfr6lL+ThemRY6aO2CFswWGG65ctUq5IorV/QcQXIlKOiU9GRyTNG+iVi3DmDqVOu2L1qk3R4+3LSm0RwgN2KBZaVbSq7ysVBDj4lTMCp+L1X12pJceQ3aMF93i4e2gB/4AdTRq9BKrhCjK0fDRTteBAPKBlgf7zHaYkUVtx3hB5PwuylXbvvI7TcNGWJ/jI+NzbQxJcHzz0OrkitRHUx3ftOQSVSuMEeTX19erhM6T+C5jNuKn3n55QBTpkC7x4KNetlOHZ8t+Qw6IkTVP1/kqixHtkCEWJyoo6DTkquFCxdC//79Yfjw4XDyySfDcjE7VUAkEoHa2lrLX3sCVkzik0IqXiAlVyEx58rBFkgHyUMOyaxaIH6m0cuGIFO5V/yOhetXsGpjiGlLreRNVQt0IFebs1OuVq/OgFyVL4e9huzFbv5933+0uicaz9Xddwc4/XSAK67I/ednem6le73XSo6ebYEFgi2wwylXMVflar/9AP7v/zJfyabFFPB85kHxiBHy73Ej0UgS2rty5bR/xGuZrkyL6l5LbIHdu+deuVpVuwouev8i5nyIN1rrnPcs7Ot5O5F0Wc45yff7iHLl1RaYyTUky4nj29AUlQ8auTbTuG0fnj9iNcN05ze318qUK+xbxa/BTJUrfv+qqwD++U+tobHArdsdxAXlT5dk0SisDYEtc6744Cqoqkrlj1xF5eSqbzfzWs6m11VHRackVzvttBM8++yz8MEHH8Cjjz4KS5YsgT322APqXMrUTJ48GcrLy42/Qdxb0t6sgSmAlL5Kh7bAlC9mKWhhqRboQbmiVcLcbIF8Yp65ZiabDMXk4ExXJMQBeXMd8bPEw63S56otbYGLF2deNckIXnWsrCWJJvlSrlIB2GPIHuxm38JhrU6ucFt5UPDII/bE95YiUyXK7fUvznkRev6jJ/zp3T+1KGEdCRqWXZcpV5m2V2jznKs0yhXi/fe9V7CUNSX985/N29jDRfY99Fo/9li7ckU/ryORK9HW2prKlazvUybkavrK6eadZmtVkcqwd3I1qnKUjVyJY70vGWoxuXK7RmTv48cv4rAis3JVbotapBuLRdUi3UKRm3KVC3JFc12xYEZ7BqYsUKyqy9BP3oaYv2EBbH32I3DvHeWQTPpaX7kqzT7nqiOjU5Krgw8+GI499liYMGECHHjggfDee+9BdXU1vPLKK47vufrqq6Gmpsb4W7FiBbTLohYk5wUHt6SgXHnpc0UHQ7pi60W5+n6VuYJzxBZH5Ey5ao6Q6CHNRNnRlSuswoQr7BgIZmJLE5WrdGVNnfab14IW2ocUw04DdmJBXF9JvBON5dcXKAaE773XtuSqttH5pMGy9ZubNsOjPzwK983I3stg9IrKYUGL1lCuqiO674hatNLkXHGIq+rpQM/td981bx9/vPx76HV21lnWSnBIrmTkJZMKgPkuaOF0fNqSXLVUucJCSNqLCwBiVumnh1At0A0je4xMq1z5UwVpyRXuY7c5we0aku1bwxYYlb+xoTnSpuQq3dhHyTO+l84PGDdwcpVptUDZ/fbe+0qMb9bV683SOwDue2EBxP/7EMDU62zP5ZJcOVUL7EuVqyxsgR0VnZJciejevTuMHj0afudZzxKEw2EoKyuz/LXLvCsyiWi2QHOEDPqsq9xOyhUdDL2SKz55fLbU9BofvsXhOcu5gjgpPZUId+o+V4fru239eoAXX8wfuXIKcDbXN8COT+4Iz/z4TNoJrpuvN1NDzztP/nyTQz5BriAeo1yveWR6bj355nzY97D18NFH1scxwXn+xvnG/dfmvWZZ9d15Z4B99/V2zlFyZcu50m2BWJ3z9LdOh/3+vZ+nJOF0k2i2k+wnnwDccYemKBpJ3kK1wHTKVTbgYxSSIn4MBwwA2GUX+ffQ/c4WpZLuJCFTxTwfCzVecq7cbIG5rBaYD1vgsupl2o2InbkVBRwOioOjo1uw0v26pvOmA7lKt3/crhHZIhnuA1ShnRagnOyCOF5g4ZYDD8zsvMo1uaKfh9f3Mv1wIQYObLlyRYFzC/5urHiJPeraW7n2DY0bLPczLeDVlvjoNWuOYr6VK1wQLAgUWOawisIKT4vwTuNcezsfvKBLkKv6+npYtGgR9OtnypMdEcwWSFaF0RaYtNgCC1luFl81cOqDRE9UWk43XbXAjxd9DB8t0qLKYd2HwZ5D9jSe39yc2dKz7WKhVsA0q5AdXbnKdnXcsF3pQJUEqxg5wWm/baqrZR7ySz64xGJfk5Gn8oC26vSNtfq+gVgsmbM8Iy9Bi5OlMVtkPGiv3RY+e6c3C3woZq+zlv6as36OsW//9jetCednnwHcdZf3HlfSPle6cvWvWf+C535+jnn/vahk+bAFogK6//4A116rBUXYUFxmC3Qrxe4V4inGjxvd7qFDwfF76PvF/kxOylVbk6v2rly11Ba4rEaP1pvtzC0e9xnjI6rV6ezApQErubJ9PymywslVLBnLO7nCBc5UUh5mRaIp6dh52WVaywFcwLn7bvdt8rp9CDGNPN3CkmhLRSs7x7BhuSVXWGxjwgStV9sDDwC89Ra0K2xosJKrdQ2tr1zh/P9///k/OPH1E438dC+oTaxvVXJFVSvRGoiL8G7xgtO5hAvRHQ2dklxdeeWV8OWXX8LSpUvhm2++gaOOOgoCgQCceOKJ0JHBbIGCcpWkylVKY0o9i3u6Khv0BEbvNK/646ZcLdu4AQ544QDj/qlbn+rJgugE23dQtcrFFohBKuZYYInOzlAtkFY0cwOusoqWMAze3RoJO06eOpHFz1tRs8KVXJX4tHPJqb4LBg6/b3ZWhFsKMeBpc3LlMDH9tPYny3P1kXr4dr5WRIdWtsOgKSNbIBa0kChXj8983HgMbYiZbGuuyNUCUkDrhRcAGmIS5cqjLTAdxGuVL0rQ3yU2fXX6HloMw61BbyaqZqbEBStg/uUvAD9aUzksoL/NiayI5CqXyhX9/UikxP3bYuWKk6sYSfwF6/txrMeiS1j0xA0lgQr3RStqp3dQrtJZtL3kXNHcPTxnl66tYnmrMqTiAYvazUHHi5kzwTMyDZQzUa4QS5ZYyRX/rV4WCMXzQfxunM9pi5ClumO0vSpXOO/KcvbyCVxMe//39+GluS/BDZ/f4Ok96HCojq9re3LVrZ+xcOgWKzrFZFghtqOhU5KrlStXMiK1xRZbwHHHHQeVlZUwY8YM6EXL33RY5SpkVa6AKFd+K7mqaqqSNvelgyEGGiK5opMqX+XdVGtG3ti08dKdL8040R6bDx/y4iFw1MtHwfLNa5yVK6GgxZNPAvzjH9rqGdqrHnpI+98rMCcDV8PEAhLtoc+VV3IlqlZerIGOAQ4hsrTErIxchZIV7oNwKmBrKt2plSsCWtrYCBQ53ngedh03FG691aoOewlELLbAgF25QlJNK3bKJrPWIFciSeG2QJ/esNVrQQv2HmuetQ1OZaTp7xKvJadrS1SumL26lZWrPfbQxjS3cUxUDmTHKJ/kir4f9xEtfuRESvk+z8QW2L9wlO05/n4c+xEzZrhfO8V+q/plazyasJMrvI6o8p+OXHlRrqia9913ALuMHQywfHf5m5JBeH/h+7aH6XmbyXXZWuQKC13g7+TXkRcFTLy+xPfQJuDZ5GC2BKikuBUgwudk829rWwM/+P0D4/Y90+9xda1woFKUDNTnnVzhPvSiXCHcbOxO17h4fnQEdEpy9dJLL8Hq1atZeXUkWnh/BK3R20HBcq6ovSEEkKDKFYQt5Aqr+iHBylS5ooGCYQeJaRHipCGT4Nuzv4Xuhd1ZtUCmpnksEX3+O+fDewvfg7cWvAWPffe0NOdqaPehNuUKgau8b75p3vdaCAJXiA89VPNxiwFQe1CuxNVgJ6AFkKG2H0BTdym5wgEOA5ZYIuZJuRLJlSz4CiQk3h+KZMDWVLotlatPF38Kez27Fzz3EylF5YKW5PPRAEC0jcCcU9h/N9xgJVdeAlze48qpzxUec2odxKqRbgpmvmyB4vVT16B9SZGv3HMpdq+TvBO5otudrXIlkq1syFWmxIWTILcAVzwmsu0RyRU9JrlUrnAfUXJF+xxlo1zhGMXPi+6B/p6uSzdrUJGvzJVc8Qq7lFwhqPrQEnLF9y3uF0sft6YAQFxS5x+RLIBvVtoXpuh+zST4zTRQzsQWSANcVK0QXsmV7Hl8jOZ7i+dxa5ErLKoy6J+DYOvHtobFVcT3SIBjKzYPbmtyZcQAGRQRY1UNg815J1c4H/F9JCNXfUu8lWN3ismoatpR0CnJVWeFzBaYAEqurMqVk7IhKlfihEgnmR499Bv6BPHQ/z2kKWg6+IWUTrnCgBHzUDhmrhB8fYkwsxlu03cbW0ELjqeegozx2GPOzz3zDMBPVjdXu1WuWD7L6u0A/rkc4N6VAHV9bMf3nzP+CUPvHwq7Pr0rW21zDHBSQQA9D4DaUmSBXirmEBiQz/o1TTPjXJMrtxSvaz67Br5c9iWc/vbp8MXSL/KqXFGVwGIbEcrdUnLlhdDbbIFCnyvsDyQiXUPpfChX4m+pXauNO2EozTjnKt1xEMmVzBYoXkteyVU+lKtcBC3iZ4jbg9ssFrSggWxL+1yJ5IoGw05FQLySK7oYUAyk5jc4v9+NXIXAGtDZ2lwkA2nJVS5yrnAfeba9pgLww8pZbUauMlWuOLbbznodaYU7MiNXTzxh3d+i7YuOrfnEU7OeYgRk7vq5MOnZSdJ8INvCWRtUDMT5XLSQUku/E1i7lqCz5JvNOIUL9i/PfRnmrDNjOOpOoO2ApOXYXYqfOSlXilwptLotkJIrHwbNSK6K3MmVaPfgkwG/0PhEgUGhsVoZL4QCf4HWsJHAK7kSB4aqBmH2S4RhZI9R2rZLlCsEVhPKFG5eXRxHt9229QZyGbwGcIxcvfWMRoywbPHnt1iOL+7/Kz7Suuz+sPoHtgrn+tn6Pl5Ru8J1so02B9KuTC7cmL8OkOJqMt63WX4IqIr2xMwn8kqu6Oqq5ToTFgdaQq5QuSoJlViUK1mPlXSVI/NBrsRgtG6tVj477CdqZyBqS76WBZ/p7JJi+wCZcuWVXMlsgTK0hFy5XTNeFfN05ArHLfH8pd+bT1ugrJhFtuQqnLLmS2WjXIVS1g0Sx4ikxBaYqXLlJedKVK7SYXnVKtu1SxXYlpIr0cqZC3LF+8rR68jtfJc9h9Z+N+RKucI5frmW+ioFXfBFIiKrsEwXznzgS1uNOR9AIiWOo8trXH6YDm0hLpUzcoVq3ZYPbwknvH4C7PivHS3xB4dbzlW2yhUtptJRoMhVh1OuBFsgmGdjIqFd+L1KejkmYspsgU7KFXrqw2F9RkmEYXSPLS0TE4JbljDoc6sCgytDFkgI1IiyMVBZXGnLuWpJvo24sitDrnsn5aNaICsWQKtqNVqLlqDVkgJzcuik9uqrAHvtSw68vo+p/9mpv1k6UosBAiUEuYQsIHQ6D4xA6YvrAZ6aCr/+QlhNvm2B+nWG6muRzxos0qDfS2BNLX94zWMF0G6hbq7KVbZl+Vuygmmz2TaE7cqVP2YWumgBuRKPuSznqiW2QBlZyIRcicG823tFoui078XHxfuilYqez7REfVsoV+nISFWzaVcP63mdFFhZ8/TTvS+UBVNFrsoVFo9IR67SXZtOxwmPNT8fMyVXmHclFsPJpXLlRIKzsQXybRs3ruXkKh1yRa5OOw1gyBAtrUCGhZsXplWDaL7V8IrhLW7ong1k1Qm9kCumXDksVmcz7qPtntshcc7ni5kWchVyz7lSypVCu1euMEiIE+WKXygtsQViEMMLG+BEkQyaM/KYim1sn8VXKbBwhluAPW/DPOsDtNGojqElW0KPoh6Og0Gm5AoDjF9+Sf+6dMn0+YTXVWymXFF5X89R48dXrNj366ZfbSvPqUCTTV2h1gYncpW2wWPK7+hXbylkq8lO5wEjirX9Ab64BWDF7vDTHQ+2unLVu6Q3DCoe5fgbsulzheDWQCflyqngSWvaAhNRbSAJAal0EIia/a9aQK7mzs2dLRCDQlp9bocdAP7+d/vrMyFXmZS5FsmV02vT5VzJmoHzz5J9ZkvJ1SziYKu0Vj43QPeh2/6jucChpL0UO/b/e+45u3L19dfaQpF4PvuShe7KFSmHTnvwUHKVDdkQ92vm5KrAbKach4IWTpUws1WuaDVEukjh9lnZtFLJBbnC7X/+ee02Fo+xbVcyDgs3CeSKODk4aJXegWUD24RciflWTtsqgs0VYjzlj2VNrsRzlcd1uVKuIhFn5SpX/U5bC4pcdSCITYRxwotblCs7ucJVgqs+ugqu+fQaiMQjrsoVyuf9+5vBK7MUBM3IcFCR1RIoXkhug43tgiKFOTgGdxsFlUWVjjlX4kCQrr0SXpBeBum2JFeelSsMUGliKidXTRul1eowB4cGGLiCmQzQ9+vkqmGdUSnJqXl0WgKSDMBXM9fCMccAPJq+KnjelCu2IhY1o4lkxCWyyCG5wuR8bnPCa29AsZ7xrYMeBzdLo1NBC4tCnKVy1Rrkip9TIbDaAr0oV+mOw+zZmRe0cMpnxKAQcz5OOknrI4Q5JMOHa+WfsdUDh9fJHMehTMiVU3GOTG2BsoUZ/r1ujeC9QlycOfJI8z72NWsJuaK2wGAifbVL3sYAqywed5ydeMVjflflKuFBucqWXNHFE6dCH45IBm0KRGspV9mQK0qo2rtyJVN2RaIg9jqTKVc0rhlQNqDdkCtPtkBGroR4qrAqa3IlxhnzN8z3Rq5KW2YLxLELq292JGTRcUShTW2BQrXAOERskxmunnPc9OVNls+4Y987HJUrEUy5CpgzR4+C/mnJVZ9uWqEFt+APA8Zm0neEY1DJSEgWL3OVsSlwYHBbJZStVnVUcqUpV2Tk0QdMvl/FgRbJ1dg6wcbjJ1+m72NcvcNVZLRjWibbUB1AtJQFDmkJSCoAt/xpG1izBOCNNwAOOwxgoLnA13rkig3a1oOJvw+rWjqhJathPFePnttoyS2JW8kVVf5wf+IEQvOw0hW0EJUr2eTUUltgTsiVfk4WWJSrWE6UK7GvnZdS7G7K1YABAP/5j/VxfIxa37yuuOMxFfdfZyBXonKF1irsRYSq3+GHt1C5IrbAQNzBYyiANpY96yyAM88074u/t64uZRkLEnGTfIX82ZErp2uE7lc8fzKaTxIFeSVXbspVNmSSkiuvylU2Y6yXcTId0lnJZPY0qXJFKiEPLG0/ypVbSXMOZuET46mCxtwpVxvtypWsoEVpqJQ5rzBvLJtS7IgPPwTYbTfoMFDKVUe3BaYitgtli8otpO+f/PVkFuhkQq7ifjMwqigwVx84xB48TuAVdzAhdO+he1uaOnL0Lx6mK1feyJXbhfj99wCPm31W2w25EtW2yNLV3nOuAtQW6E6ucFCtE8hVitoKyWoWH+wsE2RhtbG9adWWZADWLOmRl+TTTGyBbLIU7KbLN27Mm3JVVWUnNliQpW/hEMvrRFtlOiuo1BaoK1eoMooTHFUw27KgBT8ng6ninBa0wHNAzLfhr8+moIVTAQvxPV7JlazBtqtqU+0t+GwTcoUXPH5wKmUjV7vsoqlH11/v/PZsbIH+eHqFOR1s5KrBuvOSej6ym3KVbV6iqFx5XTBzUq6oAttSW2CulStqC8ynciW7TnJNrmT56B3JFijbfk/kyp/IGbni6Qi0uIdMufL5fNC3W9+0OVduc+NHH0GHgiJXHdgWGAymLAUt+OSAKgRVr8QETlp1CUutOwUhuOoV85kDS1mgd9a2QD4Q4LZt1287ac5VIXTXcq4cClpkMjn8avZYzVmvqVxAnHgj/34Z4NxzXWdRZjtqarQoV/5ksRHYY8At2hlwZVgkV0m/GV31IQTAjVw55XZYAtRUIG/9w2QBIa6ey8B+h2A3nbuwJqtzyOjv5gL+O+nEUlFUAb3CA13JlfideN3Sw08LWog5V3RCo9ee55wrH95I5lW5EskV/h7apDNTciU7n1pS0MLNtpUrcpUL5SpdzlVOyRUOMljCbdAgTX4ZNQqi8xZ6IqRO++/6T26Gm764Sdqg1dKXLeZS0s4D8NzBJsMUDY3WnRWP+fJmC6TjI5KZjMa/ZIHNapWtciU7X3Odc9VatsCWvM8zuZKUWF9dt9qzLbA1qwXKxnenEvEcaFdnpEwkVz3nZ5UPx/poCucqxh94DaWzBdK8K4xNnPLzZfMA2rb33Rfg2GPTp4K0Jyhy1eGUK3NE8wfjjqsQ43rpJX0oIiXwz3v98DTp34tzqZtyFfWbk2Cpr0/W5Iqv7vcq7gVb99laqlzhhcWqBeZAuaL7Ys893T8nm8AyWzS9+5nlfgQbPz/5JMD990tfjxM1Di4Pn/hXgGrTbhYg5AqLUojecQxe3MjVoJIRtkpElsk2XONKriwroqSHDPtNmazc5tAWyAI2gbTP+70hqwm8uz3H3gb+O6ltBElQT4Fcicof3c9IFLG/+dChJmm02AL1Jt20kTA/1thwmz/vOecKyZUvT+TKUK6Ity6g/ViqXnnJuUKF5PXXte2WtgjIshQ7BoduSjUNGNuaXLWqcnX55VqNbZ6osmgRxL6anrbRsgi6zx/59nG4+cubWeN4N1tg2l56aXDPPfbHGpqSjscjl+QKz9NddzXv9+qV4fiXKGALY7TSLj2PMwl+W6NaYDYFLbIlSS3tFbciTb0HmfJDFdV2pVw1m8oVz6nH7eJ59DIYcwJZrB5y8CsAPRdktY8xNUFGijD+8ESuSk3nk5M1UDamoQvpk08ArriibVM4MoUiVx24iTAEo+ipsFwoOEZjwCElV5/fCv/+xwQjGMATFfMMamKbHMlVE5iRwFH7DbRN0LxEtBhkUuAFyXKG9IGBNQp2IFdatcCWK1c06MLE9Z9/zu5zcop4HBqvsHpqInrjZ7juOmnN80ce0RodN1aVA1SZhMiXKDQIhczKgMFsXV3SsoKZ8JsBbv+iYa7KVUFxk2vwaFkRzaNyJbMFOiU7s3NMUK6WrXSPdJyOfSbkSpxYegj2WTG4oN+JVeqwkAwGAhdemL5aIEWfkj7aYoSnnCs9eMPxQkKuclGKnV+3AUqu9MpUNO8qnXL1++9awPqHP2hV42THSFYt0ItylU6BySawbaktEH+fzL3aEnIlC/AdydWUKQD33Wfe30KzlcegoEXkio/x364kVUIkylUi0oLEGgC49tr0vzWZ9BlzAiVXdFEqm5wrXE2n6NnTfaHCFhwmg2wb6BiSrS1w8aal7bKghdu1cOedmX1/JkhX5ZaXFHci/bK4BtUX3uuqrWyBNO3Dbdw3fh+JGSce91nWtkDZvkFgDnAmypWbNVA2brWmsyiX6KCb3TUh2gLBF9VtPmbQOWGCNjceNfBC6F/aHwK+AOw7bF/tBTMus3xenz4Aa5uWw/LaRc7kKmWNZB97zPqakoISqZ2JQkz4H9FjBHQLmjk69MJiE59HcuW2QiiuaON+eeABreKV2Fix1cjVq69C4zLrgB4ZNc5kERL1yrHikV4tEPHLenm9+erapKWCVdxnzjb9ikxbIC/HHomaq6cl3czbspwrq3IVbBVbIB9knY47W2EUlKv6NBvjdOzRLutZuSIrm6gwhaHM83fSPmxffmmvFigWtIB5RwG8+yBAzUBm/a0orJBaVLDP2dD7hsKez+zJAtnmqB79+POvXPni5AIraPKkXNFj+vDD5u1TT5Uf72wLWtCVdxnwHOPnWb6UKzHoO+gggN69tYWUXJErGZGSkiuUpS+91LyPZRQXLIDqyY/CGjCDoVC9t/JtlmOgjwuz1wulHoVrJh7xyNwyQKTJHtrw/ZfLnCtxfEblygkDB6ZYERx0gRvQF4PoHEnPW6/B77LqZbCubmOr2gLp7WxtgW7jbDYl3CnEcUMc42TNgWXKlUgcuIugrcjVqMpRWZOr/t0rLTFjRuRKsm+4lTJdzpXXcuziMTv5ZOiwUOSqo9kCycp8iilXCcsKHvaDwZXfB2/cElZctgKarm2CV499Vfp5WNHtoe8esqhf4sDcmLJeUGJiOQ/+EGLiuswbjLZAbIq6be8dHC8sXw6UK9mKNrpe3nxTI1qy7807Hn8cmsBqgYmM2spcAvzXv2wzitOqTSoWdm7QbKmYZTb8jJHiJH0IuVrboClX9U3mDi0r8zmWNW5NWyBVrsrK3I87C9gE5aohYo1AcUKcsXIGJJIJ18+qsPc19aRcIQmKRNy9C/Q7sfWBWH1QplwxhaqhEuCVNwC+vwjgtSlMuepe2N14D30fFq9Bf/zU5VPhkg8ugUb+pcwWmMpPQQt+3UbJOa73yaPl2NPZAmkwhq/N1hYoy61KR67o9uWLXIm/Z+VKzXHAlctWJVdY9Ycn4R59NMA558BNNwH0uv58+BL2Ml5W8I87IFvlavY6O7niTgZEpLllRYsHD7Y/Fo0EHPdNrnOuvJKrYNDHxhWLCqjvo01Nm1rU5+qrZV/ZHATpFPhcFrTI1haIY7pTDmRLlStxHhK3g8Yloyu1NjO4ECXmCNKFAHTqcPLQFuQK5xdKUtyKWpjkyjxo/corc6JcYRxHVShKvJj7SAIvjYTpmIZO5WeegQ4LRa46si3Qb7UF0kIVSLKQxGDDREyyZ8RMAOZbse7wDuSqsCgJzVDluhJGP9eJXNHVFVYNEBt39t3F+cLymHPl1RYoDt603HK6z8kZcInzq6+gEazHIRIo1mqXc+aK9UY9kKtk3JzZ5m4wydWgskHG7bo6n5VcgRnQ9AwNsNkCGwxylYSK0gJX5crNFphLcsXPCZzU+cTuqFyhfUNQ0ZpipJpmMgG7PLUL+8NcELeJv6J5TVY5Vzjxpvv99HzjhNGyzZKCFqzSUtVw80UrdmdtDzi5QtQ0a6uHGBw897PZCOjfP/8bGiP6l7JrPZUn5Urb1hRRVblylYktUKxSl0tbYCbkyi0oxO+99Vatcp7MpupGzLzmoLSkoIXMTmsjV7hjUc7nfrXJk5mqdvPN9u8qeP5pTx3ZZeQKK4yJcwMnV0h0Is0tC0NGWXt2MyRiBRkrV/kmV3wesuwjXWl3UiC8Br9fLvvStsiVbnuixKnQVgUtcB5xuibzTq50YoLnw+ByjaGnIGUjTXx8R5dOwB9oE3LFrxf8bkps3Ipa8FxqGk/1694za+WKqmdje421qFB8cQBjzkyVq1RKEwRwvKPHDItYeLUjt0coctWBS7EnsW8RuVDcvN30guRAK8rK2pWO5MofajZWno1tKM6cXNFBiAeDJUH7khpeWHihpTxWC/Ra0EIMusTeGdmQgWuuARg7lvElb3j1VfbjbMpVRPc+yZq5uJSJT8SCUuVqTK8x2o0UQGOD30Kuoj7Si6KgFxT4C0xb4HvvQcMqfTAORKGitNhVubIGUfacK+wvdftXt8O90++1JGtnCmO1OWROwq7KlWALbCQHF1fQeUf5W7+61fWzerz3vGNXe47mSEpqC0xni6TfKb4Wz1tZQQs2MQmKE1WuaB6LTCmoba5vNVtgMkrJVaMn5cqNXLnZAjMtaJEr5QqLbdxwA8BttzFO0iLlygli4IN5aLQYUabkSq+wbuKVV0xfGzauGj3aMU+lAKLuCTKyfU4WXcT8Fh4sohIg29ZM4LWSYTrlKhe94LyQK5lyRckV3Q6vwS+75iXKFeaAOWHhBnuOVj76XLk915rkStwOvs8xDxwXoKWVLMn4zu2AnDzgOSyrhJkP8MWpklCJUdAiU1sgXpv9Svtkr1wRdYqSK7QFcuKFNnUkWJkUtLjsMm2B5I9/tI5pLelx1h6gyFWHy7kitkB/syMxEoG5TiLw5GXkKuAQCWBgRBvXZkmuxODTaSLDwTATn3W2ypV40WaqXGG1Ogyo5s8HmDTJ45teeon9JyVX++9vbtR771kiIC/JnLR87JaVW2o3EiGjcSa38EWgzkLOeMPntWsWAhxyCDTV67NRIAqVWPrdQblCUo6FUAwIkzoGS498/whc9/l1cMVHV8DHiz827FMLzerOGQVEGJCkU65kBS2ayMGdvtKsfobAc9+RXIEgRxTYo84mvSKZ3Rbo9ous2y+qCag+y2yBvEcIxbCKYVJyhfkXIhL8uvJgC8Tf88TMJxztpukKWiSJZdWwBeZYucq2FHuuyNULL5i3JXVoclKaWhb4YPNc/n66//lvd7MFiu+xNALEUlwuY2EIyRUWvkhT39pHAjeqIosr7LkkV173Zz6UK75w5SWHiJ9X1oWpgpyQq1V1q6TKlRu5+m390jbvc4XkyimIzrdyxckCEgKeu0of5+DjO897pXnmTjFPrsEXp/C7aSznZgsUlSs8dmzO97XcFmhTrvRS8bzAkgx0DqPKFU81x+JFdHzyMla3Zyhy1YFtgUy5opOZi+Ih7XsVjGjEx4GgbaxC5coaQYkBByVXNBGfQlZJRjbg4mAoTu49+0TzrlxlSq5481jPwNXhb7WKWdGhW9h/AzLWffYxKxxgeUAPPXkgYX/SUK4ipbYAoDlVa/lePthtgEbAHpsRn35uBaLQ86svpcoVbuY33wirxcKkXl+fYnk+HG8veJsRNFydGj1aU/3uvdfbfqTkin+nqy1QVK6iEUdyNXXZVGdb4BZC24GQ3RvZLClokaktUAyCcXKR2QLpqh/H8IrhUnIlTRbWA13M+wgdeIMrubr6k6vhvHfOg32e28c1eHBqIhyPUO9Qo7dS7A3yIBePe21Dk6NqmOuCFvw705GrdH3Q3N6brXJFSxOL+59f47xirBNhMd6D5Sm//lq7PWYMwO67u25bAcS0D5fVPSeoj8v7V4lBICVX2dqy+f7xuiBnjCWBgrzZAt3G63wpV9jPiOWwZKhc1TU1t2tbYK4LWtDzDEuY83EWVSs6jlraBKRShi2Qxy5eFpRzCTxH0QkCK3eE2mknQgn0zky50sdlPHZszs+BcjWqxygI+oPGIiUvaOGUb4VAxY2/xynniub0Y5yG3xm6NQSlk0vh+NeOh44ERa46sC0w4ctAuZLYAiM82Hb4DDb46DkTTgOWZaCJOyhXQh8gp4ETA0vL5498D57/+jO4667cKlfi/bwXtMAmDfy7trPmmhkrNYccYj747rvelCshNw0nCMPXHJWQK6ixkquQtsqU9ANsxJ5mIf1YBqLQu7ZOqlydeabWl8myD4VJ/a67fADNpZYVNExM5TmBqPrhQjktUpaJcpVJQYvmmPniBRvN/h6IJdVLnAta3GSSQydyFeG2wEj2tkCRoOBxsdgC9YIxODH5U9bjjdVAZeRK2kNEP0bhUAAq93gD4NDzoPvxl0kn2Ud+eMQIiL9Y+kVmytW68fDtFz3sOVfpbIFffw9w1FHsJBELWsxcYc/1Wbpxle06F68VvC8uMmWrXGFOAArMuDDgpVR/NsEm3S/MHu3gpv30U/v+pwoKfr6TcmU8/vLL5oMnnmjsKNm2BQIp8PEky2eftdeSJ6BtPYLvPylVrjBg5Uomkqtsg2hOIDMlV7lSrvA+XXg65RT393vNuaLb4UWVw+sdc4VkypXbeVrfFGsVW2B7zLmi1j835QrH4kQqYXHdoDWPgyry+QL7jsYKgH99C4uevwpefGiEN+VKrwIMyZBxvJilkNjCs1Wu8HPQlo7gVvt05Mrv8xvv4QuA4hyyjJgu8LzA/Y+tCnAxxq2nV3uEIlcduFpgEm2BDjlXXsiV0cNKQq6wqtEuh/8CEIh4I1exMLx7zZWsEp9YnlamXDnZAi2DdCDKvLxOtoHmZudcHregSwy4Ml05zbiRHSVXW24t35+UXKE10Mt3CeRqSPkQs9ls1JwAuC2wOWUlV30Wm0H42hMPhViqwFSuiOJDAwg+oVpLLkuWa7+92Lg5Z/0caUn5f/8bslKu8NyxJfsn4xopEZSrZnJwxbwPDEqcjn1hsXDShOyTaFSvCpipLTCdcsXJFZYH5nlxODFVhKzXMD4mVa7IquD2/ba3HKOScCFUlHQDmPgERMeYeWV8f7IVUoJPFpvnrqecq2eEJERd+U5bih2baWO+4e67Q4yoWHjMZ66cY3v9inkD4el/N7oqV7LHsiVXmA+AlzEuDMyYkUZRzjLnio5zbvk9PC3TiVxh3hTNzaIFfIzzTbcpM5xwguu2hUI+gNNOk3+4gNLRM43bvuqh0iAQF+0YGdDJVbZNZvmiT65sgZnmXNFFp8pKjXe6Qa5cudsCvSz8MUsgQqJcyQrmcDQ2x1vFFhhd70zGQ8t/b5OcK0oUULmiOVf0OXFsRxQHW1e5YgtTy8z8g8fuL/dU0ILPd/5koTGWonKEc0BLyRXuL1zcE+cMN3JFHRi4bVhgilfIdSJXkYR5EMPBjuUTVOSqA/e5ivuaPNsCexXbbYFNSTm5wjgfT/JUKfq4C7yRq2+uhA3zxsGcOVoJzWxtgZbPD0aYl9dp8P1i0TeZ2wLr6sD37YwWkauMBn1cfubkqqQEIgPNVSeE8XuHDAHYckvT96NnlrsqV8KxGdJ9iNnUmTyHgyoGEUnSRDjaGIO+M38z7q8783hIxLTJORhIQiFEpEEEn1BtypVQ+AS+Mpsl/775d1i/IbuZUqZcyY6ZUdrZplzFjNVykVyhouYUFNiqFEmUq2jUZ7EFYtCGE0BLyBW+l9tr8Xr3kQu5Z5gmummg5Or9KcNYhaX5s806+Wdte5bFftQtXGS8pzFeZwscF1cttnw+lq2X4eOPAWbNkihXzWaQEirEhsV2y3DwS116EatmIhYuhNiPcyzH4TeHxPuzTiuG774z78sIj0jksiVXSKg4Zs+WV9D02sDU6Zyj44rbGIP7HXMXncgVbzxOA3/L+bZ0KcBMnQRtv72l3J5s29i1cLG5WAIPPui4gakt3taq2ArXIg0CaRl2qlxlmsDOj0FbKVe0BP9ee6Un3PKcq6BNScmUXLG8afZZgYyaCEeiCWYpzKtytWABxG51LoTS89CdoTBRnxdyJW6ThVwRdUpUruixEC3fNuWKKPL5AiNwwrHlhY6cbIE43/GcK97aho99ZUXF9ubyHkD3C3PJSKzqvBq0E7izBguB4HwsNk/HthQchQ/+AyLrzFXZcECRK4U8AVcd/EnzBEswcuVtZqkI9ZGsFsttgWglwMmaVYAhvnl+8tPGpwa5Wm32rdLTizIuaPHQQwBHHEEeCERZiU+ngGjqYj35wKstEEfrY44B329We1jkN3sBADdkRMaw6sVaXSHac0+IJKzRnmXy3GMPMHaOHjW6Tdg+fUWKKlcGuSIKDk7mzFoQMDc8Mvs36LPBDHgXBmogpZd3DxX6IFxq7vS6moRtQrUFCEJuHiAx50+nkrBmfSyn5EoMOgxrnqhcxeJGQEftdtw24Zhj4oFcxWN+dp7x7+Yrm7myBfJ8K44Ltr3EmVxFi+Htew+Azz4DmHrdPYbydc7258DThz8NAT2frjBUAOVhfeWT2EP49TJ/w3x5UrRe1Ql7CmHwfsABkh8mVPkMFybtK7z19RD825X2fTJklFYpBY/5+irLcaiqdw5gXnvNvC1biMgVuRLtaLIKmhRuVUSdzjmv5Arx88/O5Oq665yLLLDzTVfGkz6Ar47cxkJ8ZNvGrgXsTI/djkEnZ++8I92u+RvnA/TWCqGkSEBIlStKrjBBn+9nsUVGOvBj0B7IlZtC5KZc+fT5nAaudDs8KVe1zsoVXhMWghUkSksiJLcQ5yrnClcA9twTYtVy8jQNdoVQ3SYI/zbH8/fnyhZoUWEKK0zHh2DzlqU0ZJpzhQvVsnYNIrArQr9+Wh9vCjZ3C8eWF7VwsgViDpRxbuvkih+70kKTHNZHHPzDEoj7gpZW96xcdbOWYxeVK4owkqvDTUePIlcKeUUwRVYdfI2ebYHdUvZqY4ZNTCBXwQLtMxm5Kl9ueQ5zZ4YNA5in22yNAJCQMLGioFflCjkIBg2iLdApIPr58UuhoSnuXbl68km25O4T+vxEp33nbfTTkZGNhVgCYb/9bAO+5b6eVM6gJ5u72QJ7FfZ3JlekUhcjV7jCFjS/LDL3N+hL5rzvVn9Hqgr5IbyvuS31VXFX5aocc7cE+yiUWX2A6zdkV7JWZguUBYEGgReVK/2FomrFiUO0ttlbeWdJtUC+HUY1KX2CbrFypSdai+RqaJnW6JLCIEq0txSZgHFB5oxtz4CQr9g4bgYhk5Ar7ElEwfcbLqjcd59WB0FsdmtAaP6dxCop/HfyIh233grBtStsb91UU6CtrrB+bHRhIAXV9d6sN7KFCPE45qLPFapSMnKF4x4nCELRTwucPhfPC/6edGW/mX202ZtCYVOudHJ18ySASYmnYOendjYIvdwWqN+g6hUv8UWANp9fN/5qVJ9NxH1pyVVLlCt+nXklVy21BbqRK1rgZAdznTEtuSoJaG/kBQHE7cDbiQ8/0RL+cOLFkvlUrqXqhcyeLRK/QvN7cLynlWZzagvEiwSbUm/YYLmeKXadpM83sdwrV18v/xpqG5s9KVc4HtLeTDResdgCw/ZqgelyrnAax8OGxhRRpRFxySVaDHTeedbH2dwt9G/kaR7o7JGVg6fznbFoqp/25UXmYLG5gZwPacDnWCQ5WBSG2wIzUq5KreXY3chVITRDpN7cPmULVMgrAoRcxaDRUbkSg/JikORcOZCrOGiD0ubmzQCDvwbo94NtkHr0Uf2tPr8mUbuQK4ty5VLQwoZAhClXbpPu3U/YAzWpcoUzMXb9ZCv6Kfug66GHi+X1XjFtmnl7773de29IyJVbkDWmYmsXW2DQMglqyhUhV4tXW8jV1GVfG+SqOByA8P/tazxXFw275lxVhHvay/kLZMRtEHUCBpp8/6RTroyATVCuEokUS4SlCgxVrmIb5CUL8fv69nXPueLbwc9vPkFnolyJVd2ocsWtH7L3cRhEKeV3XSXk11s6ciX2eMH9iquzntYeYkU2ZY+D2QJR8bj3XgiC/eLH86Ph4D8A7LyzJRgLFCTde+iQ4FimXImlsvOpXPXpwzbfaNdAg2/LNnvoCZQusKTkivaAk8GiXNVEgcmbAHDLXqYV9LV5r7nbAhEHHqiV+0R8/rnmjxQKxLAcCX0+SaV8WrUcPQh0Ilf8OzNVrvgY4HWxq6XKlTgW19TICQzWCqGtC90KWhT5S12VK0TkoMO1RTq8fv73P22ewL6JOngDV5lyZZuPw5RcFbRIuXK1Bf75zwBzNQWzqfdA+Re8+SazwoeJBZ0i20InONcd8uIhkIgGvSlXRRUWckXjFVnsQpWrdLbAY47R5jG0sN59d/a/RyRXvNcVFtsQx2xLMQusc+JCrqqb0kjwBPy65SRzRIU1xQExtLuZZ5m2HHvdGlfCGd5vT4iQnx0GSbJuO4YiVx0MgZTJNGK+BsecKxGhhF2ubUhWyckVKmJ8dQdJ2pGnuw7YLBeMBFZOyhWuovPVeG/kKuqac4VYvH6tN+Xq7bcNP6Nv8CB7Mj1q8em8PtmQK6xbzssibbWVO7nCJa7+/c33xeOu+2lMxQTL/a16b2WuqhEFR6ZcRRN+6EPmhSWb0Bqp98UqCkF4gLyGr0y5YuRKDO5JtUJE1aY0CQlpjqEYQIrHwFhBFJQrDDhwgpQpVzjBRqoaHH8nxqDnngvQ+/xTLJZKivrGmEGG+OTbUuXKyRboSq4EUimuEvJ9ieeCaQuEtOQKgfvOUwArHPNY1G9Vrv7+d3ZOy8gVYvkKH8CVV1rIVcoXsRVuoWiMN7gqV6JdK5/kCr+Lkjknku22L/m5k45csfNE/3xcfLLZWJ2Uqx9+YSedKKq9/MvL7rZAPpBS9Qp9TAQzV8+0zSfFEmXGSblyU99yoVzJyBXNOcrUFkjz6ngxRT6MP/ccwNY71EnPK3qsCnVyVdNcYzRbF3+PcT3QZmZYYeXHH9ld3sDVolwVVsEO5z5tVLo00JNY4hMh870CnKpVerIFYkImWlwQpaWw6egjbZ9TEEpoVbOef96S35sL5erLZV9CbVM9QMoajNNzW6wW6KRcUSucLOcqnS2Q9sBLt+jmROTZ3C04Eyy9riRFLYz5jpAyPvZ1LzIHqepGhxUgCfi+4Au4oyvtToqRPUZmZgv8mSRZCQi9/SpERpsErvA7s1hOR4AiVx0MvPILIgYNnm2B4aSMXG12JVfGwEuCcln/DLaSEytJS65wcOIJ+p6CtWDE1RbIPjtgTcB3VK6eesq47xs3zvLaKIS0ZUiyGpgTWyAmqPEMzZ12YrOrK7nCfcPVK1zqmjPHNXAYWW428kNStUXPLVjgwHpJiLZAUbmCsEW5ogFsaXGh4z6XKVfd0RYorKzRPluIxvrMV53Efkd0Yhf3ozHJiSQjGWDnn4xcIaJx+fWD34Xtf1if1REfO5KrzSQfKBe2QNbnSi/+4IVclRfqREnc/0mfsUqI7+PXA+5H4z0sXrP2i6qOyFdBPbUrEMhVEpN6dDTVbjKqzIW6yU+u5ehAPvxwiJFqXAkkT4LdkIKW580VueJBI+8ZhaDquRu5clNXvSzOZEOuUPFxI1cW5QrJFVpyBCKDRWects1irURJhu/U//zH4nWaunyqdsNvXrhlBdqXV1XHjX1pyblavcn4rcXQADfc4L0aK9/WXNkCMyVX9PjK3BX+YMKzcoUKBB/DxN/D5qerrtIGC161Eb/8jDPYRtmUq16/APy1B1TsQcrtc4x6zzLmUwXH7bdy0PObnhfGvsNtpL62e++FTSHyg8uXAvT4DS5/4m3t/sSJEN5yaE7J1Ye/f2jL/7Rso1jQoqgCSv3m4nBt1VrXfHGLcpXGFkgJqmtxKgFU9WbfQar/InoWmQGYrKiF2EAYwccIC7lCEpqpcqUvIorkCl1M6ZSrfmTBb03daqh6y+ynKZ5nvuIiiFxltgsJz/jB2Q7QDqHIVQdDgJCraKresy0QovZlwfqENigXBK0vjkGThVwVFwVdByqNXJkDjmjvEBP+M1Ku0tgCV9Z4UK7qa83mMEOGgK9/P/vkJZYnzoVyNZ00rd1lF2nAhUGvZRLZdVfz9vffu+6niX13ZWoV2sfeP/l99hiSV7ayJNoCY0JBCwhDaXF3onyYg3B5cZFjECpTrrqHkVwFXAPtuF6JsCXkSha44mtOPx3g1ou21npr2ZQrP1sVpqt7tKF2zCffLhqsMrLjRK7qCLnyWNCCngMiuWpsShh9VXiPK7fzDom0eLy1LykzVglpedtBg6w5Az5/0pNy5Vb9zgsaF8wxfkDoonNZED18uLWADdvOggKIdTdXZaObN7gqV9FEs2vw0hLlCmEE/mTBCPeFrFogfhcdq2QLKS++CPCbWaTTBv6eTHKuMlKu5i1h//8qCNPcHiYjGGh3NIDS3Fl6BUrcAMxj1fHVMr2KRyBhJVe/7w9rbvyJrS/h76LkqvifZvZ+cPYsuHn9BbDkt5hjb8N82gIzzbmi17mUXAXiaXOuwn5i0dKvPfH3RHfaU7OtI5tBhwX2O0FggvKUKablkpMrvKZ9pnqBuTzaRlYBjHvFHMuSBRaS4fZb0ylXxtiEeZNLtHMMJk1i50o977aOOOJMgIu3gPBgM7k6vP34jLYhHWasmiFdkHEsxb5yI5TuYpY6r/1uKsBFF7EDISvFTsfPTEqxuxWnElVC2kqOfQdZvLYpV5KiFoYtkCw28uNVUWLOzTUCuaILShRYbp3nzXLlChfp6Fw6uHxw2ryoflS5mv89RNbJzz9+PUUGmXld4Qsv9lY5pp1AkasOBl/KPHmr4xs82wIbG+3LgXUJbcWjKGwNXqKpBssA1L2kKCNyldqwEeCjjwCqtPeLCf8Z5VylsQWuwuArXZ+rb6ebI/Uf/mBjnhFuCUACxjvd5oJccUsgIU2y1WzL502caN7+/nvXwAEtoj+d/xNU/60a9hiiVxrkg5+sWiAtaAFh8O27H0zoo0/UJIAtKSrIULnqYff7R0phQKleOpwoGLlSrvg+w7kcLTjffjwQ4Iub7cpVSlOuqP2Frrh5Ilc4qZDVeIoqQq64baQlylUDaeyZTrnikyAjyCK5aq4wlCse63DLEl159enKVTpyla70eDo0rdQ3Ak+syy6Dm28GWLQI4PzzzddwEhgrNcsiN9fVu5Ir2gdFFryIOVdeiiZQcsXHKUquUOT2olyJJBt/88knu393JsoVfy1eF57J1XJtfPt9K2tZfzzuaEeVjW1YIdICDDz5GPrww+xC/WbFN6yfHaKclHouC1YCvPARQLyIdZhAx1j9WrNIUhHJi2F20ccegyG3nwtbjU+1ii0wmsw+5yodufIF5eSKnl9hn0muuHVSLI8d/dsN5soBHmysLMNxyy3EFqh9cFD/Hk66MNX46juXA5y+F0BRDfg5+U2EpNe72/lHA2+bLRDn+zvu0H+8TxucfT5oaCY7Vs/N5U1kEYVl4ZyRK7RW/rbpt/TKFSVXJ50JwV/mQ7F+KtSG9fP6vPOgTlKMK5OcKwo35Uo89yi5Yt8hLI7TvqUy5cpwapB5mZ9/FcWmc4HZJ3XgeDx0KMDWW9tbSVCFjsZxRvzgYBMU0aebuVKzZslsc2FbAB9HLX2uetmrE7ZnKHLVweAnKzKbo2s9l2KXrabX6uSqOGydmaNQzwYpPmhXUEM5f01UJFcmAWue9oOW/DxwICT/cZdtxcPzamMgyiacQIHzb1xXWyPt1WGxBf5AasMfeSRzWFh+S0Vf800erIGebYFUudIz3dOSq223NUfhH35wDRzwfSjF02ABISoZOJmz1S/BFohNkSb0tpMrtwR5x2qBYnAfLYWt++oFN+IZZqpnoFxxQZLht0PtypVuC6STKfWFO5ErTuTw3GJNEh2Uq6ofzYZCmeZcYaAikqt6Uv0yXUELHnzIyVV3Y5VQJFc0Z8DnayVylYqbDWv1kuuIgSTXnTeajpJjGI2mpIESB51886Fc8euPDoFYhVw8bl6UK8zfT4dMyBWtpOmVXNWktB2ycQtr3qlbbh2qnRag5HjYYdrtVaug+tXn4fS3zLzc4T1NNlZaYBJlRMPqGqh/6F7jfhEJEgu4xf3ZZ6Hp/S+gvdsC05IrociPLOcq5CuxK1fN1i+KjrEWLsLCSKyxFmLhQthUq6sUunsgqDtR+PyNCwz7HPsbQF+tAElArwacjS2Qnvd0sYuto2JOJWcFaB8drylSTbRZsT6O0u91uiazKWiBThe2H9MpV7Ra4Grtdpk+htbxtz7zDNTN/sFeLTCDnCuv5Eoc2y3kSmYL1AtaOOVcGbZA4ijhc3Zlidkbce5HO7LeiFOnalVgMYsBe5XioaSg9kgaxz148INwwIgD2Jx61a5CYCUBXnt829eEounJFbF9q1LsCvkF6XO1KbJWWiVMZguUkauUXxspiwXlKpKqZ4SITzw9Su3kik5ERYEigLi5mtMM+kzT2AhN1/7VeJyu+NCB03HQ0ZWWSMrZZ5uMBW2NT222wB9nmhHSTjuxgldUVIqUkujj9dchJ8oV7nDeZRWbA+uJD2nJFS6R85ywuXMhTicmAU7BALMtyGyBtKAFDmr7yZWrTMlVZWFvuy0wEYbxPbbTN1RIwssBuZJWVcPgzINyNaqH2TQ1Du7KlVFCXKyGqKP6o09tk6/XaoGyc4G2FkinXPH7UnLV1APWzt2CWdBs5MpiC0xPrjAQaqktsKmAqB4E2NeFAyvsiQFcDCtEkXNTLHqQj5wrSq4yabuAQaybckVJTkvJFX42H0Nxe73mXG0GPQdqiL2pPFoDZWMb4cImDK8ZwN8/vB4Wbl7Ibo/vPR6G9TDZWLegGcgh/HffBfUNZmBbeIvWk439jq3NHNLgc2aOrFdbYLpcLYtyhaf9e/fDF7f9lbUXoM/ni1zJlSvzWkT7MiIek1SzFXHTTdpzAYD6VLP2e3SVgtv8cczn1wcd/woK9M9PFLSIXNFzDnNT19yn53jhRXDLLeZ7InSy1/YJtds5XZPZKFdMtUKkUa74OBeKAxTh41tuCWX9tHyh2gpzvqqd/oV7tcA0OVc5U64ytAXKClrw866imFRGXD6cFW7ac0+NVNH2nBTUykvTO7bsuSV8eMqHsPDPC2G/4fuBF/Qr0Raz13TDRd6Quy2QKleqFLtCPuEjwePGyBpb+WMnSAM+PdguEUa3ZrAGoz3LSp0Hg1QKQnOsTXibe/TXEmGQX5EB2Ilc0R4hFuirXI3gUgc6HoYFG61NgW3K1aZ1ZpNefUbAFCj+vVF/oRZ18hLoaZbpnRQEC2bONHeSnm/liVxRayBWC1y/KWOSpylXsibCRLnCinEjRsC+w/fVgniP5Cq0fqUtQOgRRnJlz8sb2W3bnJIrWUELK7lK2rcDc64iNZYggpKrRDpypReXcFKuauebCTSNq4azHHOhDY28N1YtwJln2p9raPROrvj+kZKrWWfBxSdMwAKVlqbeKDrQ65CXY3clV01VWStXhf6QOQ6gekttr3rgz48r77VNy9NjbUFLsZXSzGyBuVKuvBT0wLUbt4IWXkqNe825wte1hFxV9y7zTK5o8SKLejJ+PDQFAR4asNogLW+f8DaECsywosRvJVeBebOhnlzHhb3MBPgg5lccdxy7fVDty7BN3zVsn+oCSFpboGz/0rnFUPr8BQCLDgD47mJY//N2xnWYac4VPb6y8yrlQK7osSoA81pk197ChRAXLNbScR6j4fHjYTP/zWSRNRg0b/Nxj87noZDPVK6ackOuEE9FdM8rSiDES9ocpcpVzEIk3ay6CWonREHzp2fhgW8fkPZ14li4SSP5MuWqOWK+r0q3TFY060VTn3sOSvVCP7WpZkgdrimzdYkm1z5XjfHc5Fy1RLlyK2hRhpZ94fsLyPlBIeaVUtCqiVS5ygZ9a7XjgI7gDUL+u3g98cq57DGlXCnkFTzQ8MVhU/N6afNQz+RKD7ZLhCWV5mSdZdCtLLZOkJbBYPJkCC+y9hBqGjhaK8X6979byFUJqQJGA2dncqVtX13cpRlCQk6uLMoV6IMqt1Lo4EFdNOoDOOggc8Owhwt4X2WSrnhKillkTK7ws9c6E0unlVaZLdBW0KKsF1vqHV4xHGac9S3s5vuLZb84TXgFX3xsmyjK0Poj6bGyRan+O4SJIW/KFeYfOtgCeXCBVjva/DDhWbmSk6saYjF67q9Ho6MpLXDb//1vlotuQ0NTwrMt0FW5mnuS8ZovvrAWJ6C2FiC2QAxaeNDTp8T0xmMFwWzIFRboLEJbH+7HoF21Yl/vM/uJceWKjlUauQo7KldJvfhHPvpcse+Pe1er8Zqh14045npRwfB7cOg45JDcKFfocraRq9GjoQpL3Mv6vkm2cYS9nY124C67DL4fAFCv79Pjxx7HxhO6Ld0KBOUKklBfaB6osN88F9n7sHCD3w8FEIeZye1g46oI43FebIEycoXVvqW2wLVb2/q85zrnSszTlClXBWBuNC4CJZ5+DlJCWPb7BjNHzbL/zznHJFfEOUCDZz6PU3IVpuQqQ+WK7iOxQTfrH4kX2F/MuUQkNTLlSrxGjW34QSs1j/jvr/+FM94+Ay754BJ4+LuH5W+gTdAlylVVg0kQqmo1ZacCh3dMhNxxRyOnCsfBxuv+ZrUIOuVc5Ui5EscX2kNNlnNVWeisXGFKx4oaTY7tU9zfdv45kTxKrsT+i07KVSaoqwO48IIkbHjpBE1pxeb0fSvdlStqC1TKlUI+keLkiq+KFTRlbQvkAWM3Yam9MVFjGYyxVKl0kMXSV9deC6FISP5dV14JjdttZTxevMosFkEDA8f+Jvr2La4TNGqKeBjmb5zvrlyBPlMIs7Th68Xrl5MrxAcfOH+fS5CbD3IV37A5S+VK0kQYLWD65BYhhPnnjybAtIfO9KZcffKebYBO4aqphFyVwyDw4bqgi3LlZv0QJ3KZKuDVFsiDDDyXe4bN355Ik3NlKFcOBS3qyGraxtXeKhnhcbP0niFoIvkWFoXJjVzh75EohzKgEmApaKErV7gfcXUypc96wyqGWQK0TG2B228PcPr+q6CoUdtvTWE/wLHHSl/LrYHYE4aSBrZd2OPHRbmivzufypUXcoXBvZty5YVc4Xuw9o1oyxFBgx+nnCt0ZmH9HkowNkElq+ImC6pxtVv8nccfz+JOOU49FabtYJLwvZf5bfuv2G89ADgWN+yxk3G/kJAr9j50EGDnVbzk1q+Fwjen2IJ4uq8wb5EfIxnB6d7dwRYoKQSV3haYyoxcEacAtYXSYxWk5Kq5GuL/sZdPf3i6WVHRglNOgZpSfeeQ8TfEK1oQUiUlVx6rBXLSiuTg8svBEd2hWisPbykvicoVnYyjNnLltLia+Gqacfv2qbcbty/78DKjJ5iI1XWaiiorgrOuRpOD4nU1UOfTtqMi4jOKcFgaCY8ZzlY4WHELsVqgkHP14IMAl15qVZtaMtd5qRZY7K9g+dYy5QrJFlf0+xWbFl1+XTqRK5pXKpIrp5yrTHD99QCPPOaHn36+HuDmFMBjM2FlVdh7QQulXCnkFQl+heiz4LBPwV/iYpvzYAssFa7sxlS1ZTDuUWTvkRVdttqw/oViDuTK74fGSy4wHi/+eZ5xm38lXvCSehmW3zhzPUmQEpEImz5rN+UKo7Lt9Bwgm3KlEy8+673/vrweaSbkinux8HuxYZLLa22PYaldfSSMbyRLWOne51YtkFc10if8KBkgeesUTzlXX37CRl46QDttR329T5uIXMiVW9BKn3OqFpipcoXncuUis1JV0tcy5ao24E05psBtp6cXd6QiGpvNQCRdKXZX5UoCXMTAAInaWniQiYsRtNHrwLKBEND3DQbibspVWXd7VIprFf7HHoEiPfBtKgnbl7oleVc8/4UjkXLPuaLP5aqgBQ1+c0muvCToe+onJth2nJQrbufDbSopiJjK1Z57Su2f+BidCrDvOnancMxlCgZh+iRT1tr17pdYY1sLuVprPXECxx8L9QPMVfcQmAfUeB+N4B94wFGVw31Fr3+v5KogUCDtD5nrUuxJPYAXz3MruTLfWL10AcRX6OSAYOoS4uul6NEDavfa2aZchQrs5MrohcXIlT+jaoH/938A06ZpOTlDhjgXOon4CtmCqogoIVcV3Upt5MqpunZiwW8AixezhcEfVpuFJbBVxaKqRdL3rK7n5Mp+oW+o04hk9TOPmtuDKQy6hdHWSPiqq6BOH17CSb923gi2wFVzh7O+2vffD3DjjdpjL7ygKc+iPdzrXIfYvFlUrqxBUjTih8qiSmlBi+U1ptLZr2Sgq3Lq1RZoUa5ItcBMcP/9wgNrt4O6JVtIX8sJt1KuFFoNSZFc+VMw9C9/SPu+yGpJ7o4eaJdqrmMDDY3roKpho6WDuYjYZ18bs1Vo2HjHSadx63FW5WrBApty5UiudPI3Y6280Zz2QWEjmdpVuUL1SBhVLOQKSdBuu2kPYAUArEuarS0QS+7w0me47EuYiCflCmdqTJbBn1fTkBtbIB8cjSIhzuWtXZUrnHQ+/dQTuUIbQPD3IwDeeMHxu9wmHC/VAm05Vzblys+SezlJwnO5YsbP3A0HSYnihuC/z8y5ku/seqFSoxfgb6a5C1gqmaOJkCtUmBYu1NrZ8PeJn4MINvUHeFbvMeQCTkyccq5E6wfvgYYBmBu5Ki6175swKn2PPw7F+lNNQefFCm4LRCzVXT0ciTQ5V/S5ts65yoUt0Cu5osfDiVzRAho9/NUmuULlSqJYoCWUnmNe9tW8pGYJ7xYBGL06yqoIBjebNvGSZ63VV1Onn2E5z0I+81w0fgPNzfvxRwjVyNtt4LZSQiQjOPT489fiir9fcq4Yxycod4MkEr7MlKtgs/Q8p+cXttPgqPltNsRQqRXgSxY65hnV7KbvJzKOhdMoV4WF+vOJEFMGjDGOgI6reF2hmjrWrDdi7NuL9taaUiMi4ydKPaSs4qeOAeW9DSWE/yYn5YpZgp9+mjW4Fn8/b3otYlWtNu/6E/YFvY11tWygq3rWJFfdR5ulxG3kao89oK5EO1hlTUlj5QdzYZkjA9Wwn7Y33vPAA9o59sc/Arz3nrVlpbYf5L9T9hzPP0Uw14mgXOG5x4taiMoVtwQi+pYM8Kxc0cq7NuUqhzlXXjBA32ylXCm0GpLxoM2mVCJp8mtZbUwmofm/H9k/TA+0y046xfJwg78eNv3zdlflKsYntcMOg8BOB1ieo4FjI1/5xyAMN/n55y0THU6o6ZSr36sW2iwWBhJhNrjQyUOqXO2wg+2tFlsgYv/9zSexhE62yhWtIICdMwk8kSuEHlywamket8O1WuCmtZZ9GkE7hAPw9TgAi4OwD5IQwH353nuW55wCwk2bAKqffgGgdnDGvwG5KfbLTFfQwpIHgavRtibCAdOHr5/LgW+mQ3c97kk5kCt+/RjKlUPLg8buujyQvi2PZdvpNUJX15uazA9qWNuXFZrcZhtt5djpvPvw4f/z9L2cmFBbS0q3zOJ+pPkDeA5xO3C6ghZFGFkLCM/7kZ0ArBIX/q5Es6OVh1aj42sSHEkMNkn+RKbkqlevlpMr3OxslCvMd/r11/yQKy/KlUGuIhHogW07dHKVGjjIsAVydRKByiX9nU52PA5sUbCsRitmNCJSrIWbq1ZBwetmM/aieusxjyf9BrnCAJUqN5a1L9IQLDTfbDjrVNTDKeeKKp10/1NS45VcIegpTI+VjFwV9VwvJVf0WAVo38oVv2uEQvzOeNBsCiugZuxwiXIVdCVXxWH9tThWpqxl0Z3IlRSpFPzf0keMu5GdzUa8FFGy3yu7mQSGnweOyhXmwz7zDCzcYM+pXlJFSqBKbIHdC6zWRMTajVGAL7+E6nWmslPRb7g0l4iRK78fanXbZWlEl6TY3OAzFqgiTQHH61JUOr0uJPL1WYtyFSm1kSte1AKfpwSZKle9i7znXFHkPOcqlcEECQD9+9uVK7HAU6cmV7FYDFasWAG//vorbKY6pkL+yRWxKZUUppkFH38cmldJikLon1Hew5pUmArGYGm1WQGwYq3dOsBKee+8M6y4+2V48uwrnZUr0gfCIFfJpEdbIJm9HMlVyFolSNZEGAmBUKVMVK7YtY8NHzgsDZTagFxh0gpfvctKuRJsgRtWWfajWxDH94sYMIRA39B334WAXsLbcfuF/kpOcHrv0UezRcvMC1qIylXSSq4YYfj+e6jUT8tU0n0INCYth0a2NZXDABp7uPZiEoHbTq8RSq6aI+Z+/fSZPY3zGFMZnM67OdPMCbQlyhVtiImvsShXDc6TY7jE7jkOfaNV0eC2QLHyEwU9z7CKIkUSz2NXW2CBqy0QiWlLyRWeY15iA5FcobsYRRienJ5P5Ur2u4wqf99/Dz1SmnMhBiGor08ZdjCaW4fkSlSL3bCydqXWAw6rUG69l5GcxZoB6yiMWj8E9ycP1JDkUzXIQq6waqC+whGao7fScOn15USuKBm3kKu4SwVchzxmcbyh17Bs/xf3Xy61BdLf6SfkqiZaJ1Wu8PynATNFbZKvEpkRcyEhV2K1QFw0CXFbIPtsed6VJ3I1fTqEl5jJgZFK+TgUi5nHuLzEPEjcGuiYc4XkavVqWDhDy/OlWFJtn1ywJyEvQV5RQORwHT+/uR+cf14Sqsh5Ql05NuUKFRt9Ua0Ux9r//Md4no+h0eag5fwTSUm2yhVdZGKLXpEy27lPGwnTohZ8wQPRu6hfTshVi3Ou/ve/7MhVVyrFXldXB48++ihMmjQJysrKYOjQoTBmzBjo1asXDBkyBM455xz4HluxK+QFyTi/QsyrsVtRyP0quflms/cUhR5olxcL7MYfg9+JWNXjj+fa3hqr7McKP1x4ZZF0kuKDs41cobQ+fbonW6BRMpa90JzJLIODHtSK1kCbLVBCrviEiIHTMS8dBw1bbWkuo6Fy5VAPWQyS2opceS1owchV9QarLdADuaKJ8IiCgL4/VqyAwBpzac3ps0SLl9ffgIGj6Ff3ZguUK1d04umZKgZYvhwqefwk9ucSYJy/DlU5f3z7EoB7VgEs3wOytQVSchUh5CoVD1lWRJ1Ksfs8DuOcmAT9QaORasonUa4aKmHWa/uDf5VWiAWLXNTWOmdjh4rtwWh4jRb8FJWYkZOhAoqvDTuTqxQen4SbcmU+Jwsa8DG8lPE7MLdN7+WdUZ8rr4RHtAXyxPQ33jA/K1/KlaxcuaFcffUVVIJpC1+2pt6wWQ0uH+xoC0ynXNH+gsP7jtE6kT74IAQHmgFd4QVXWN6D1ysnVzhO0X1iIVcYXekFiAo2mTmSmdoC6fli6a0Yt8slxvM4t/riGZEr2XeHey9Nq1z5iT27utBhvE+ELAEzhZEnScaxcKjAUblC5d5yXB0qBnoiV488AmGIpJ2P4pRckcQeTmBclSuc2+d+6YlcYUEWXpCnvEDWPwDg8YX7wgYSslQWmwvLloIWkTrW5zOajJrK1S+/sFL5VP2PRczjhT8tl+SKhx/1kUaAaJndFkjIFbUG0urJ/Uu8F7TwrFxlmnOVTFr6nnlBZe/mrmULvPfeexmZeuaZZ2C//faDt956C3766Sf47bffYPr06XDjjTdCPB6HAw44AA466CBYqJ+ICrlDwiBX5qzUrTDsbAtEb9W6dXJypasP5UUCuwlE4fdh5sXMypUKiG27A1tycqpqxYMEy2o43+T33/dkC+xFR13SANeyeq0HV7yoBU7Wjz6KZa7NINXfs9I08RLQSebNue/CVZ9fzfIRGDZutHbVI3AKchnwh/2gJ99i9q9eOQlVGKy8JasSJx10MecqGGyBciXYAhurrQUtXAZ6vl/EpqehsDlJBn6elTYgxFLc6XD22QCHHmr1mMvOKU99rlhBCyEiFJps99qozRo9+eSBBRNcYBACt5YHiUKAl96C3JArfR+nANYvM5+QWdP4fV41Kh1ooMmTsi22QH6tfnoHfPLEfjDrzrsAYtrvbiRVDEVse7DdtsWDrqJho43HZLkd6cgVO4/1Xn4BiEO3klRGBS0QGKOjsQLPK0eV3EW58tQ0XKJccXBTRz6VK1w7EgN84/r98kvoA6atbOEyc0zGYJvbfDK1BVrIVcVw7Q0XXQQFp2ltABCF2+zqqFzhOEUJj00p03teGYp5FrZAJ+XKL+Sw4PVlPI+We4ccy0zIFZStklpU6fmViPsNFaTGhVyhSiiD0S+KKFdFJHpGUoV2XFdylY1ytX49871SciU7d5HEJ+LmvNGdLOTybXdUrrppTyyuW+7JFoh92jjKgs4du1cXmicKJSiickXzjMr4b3vzTfYfP2ZxUikZyZVbVVVx/6D17+OPtbFXHBvwvN7wwUzA3h4N1faxl9oCxaIWvHoyXtcV4V5ZKVfiNrUo5+qVV6B+pt3a6YZulXVdq6AFKlJfffUVfPfdd3D99dfDgQceCFtttRWMHDkSdtxxRzjzzDMZ8VqzZg0ceeSRMBVXshRyChyMReWqtMgh8MMr8O9/127KyJXejK4oLMxqgRgsDphRTo8Dj4SzhurNQHREY37XC5UHjzblCvH++xZboFMp9tF9hkptgZbXx63kCknMn/6k967ib91qrLTklTjJPPrDo5DcZ++0eVeutsC5c81lH32ZHInDWWexMcbT5zFglDZ+vNwmkmm1wLoqaEhq+8+fgS1QbB5aUGx+ZuCnmdLtoETBC3AXv/suwJ//bD6GC4TpyBU/f8ScK5rDoL3AeoL2XKkFGcwWmCTnREBuWTMIQTrbnxCsZWILpMFFlB+XqVfD8oXeyFUq5Zw/R0GvGx4ccOUKgynjWp2lKdXxSCHAptG2QhsUzz0HUDnSDLI5WEA8fDgUDx3ZIuWKncdxLRgqhGYIVQu5J+Q8dwsaMPDxYglsKbmSBdlVVd7Jldd+YiK5wmuDdHwwtof9gGnTYBCYSe5Ll8ctY0W53jwVg91MbIHLiHV8aPeh0v0XghLP5MpWxQxXXTIgVzIy6ESufLFuzhZDJFYOOZZ0vElnC2xONAKMepfd3nb7hLSJMH5neajMUK5iA80cIAMuVf1qo7W2ca6QNElCUoXXNaownFxZjmuyQPrZackVrpxFo2nJFTvW/Br1JaF7sZDXJJQAp4hvrblNVnXTF4HD5TCgVFskXVNvVzOpelMccGqeCTA7bvYWoASFKjK4bZZeXFErueKLU4kMyBUdR/Dc2XZbgAMO0Fq7ycaYVYecw/zgDZvl5IoXtKC/HY81vy7H9BoDSTLHZUKueEEqjvpYljlXkQjANdfAOrDnwLmhuEd111KupkyZAuPGadXfdt11V6i1zYQaCgsL4fzzz2dkSyF3YKtrnDSQghalhcXyVdvXXjMkgea+pN4zwZDyIfZJjRA3THgum/IG/HP2frwWBQMlRzLwicdCrgbpE8esWRCPJY33jzTjLwu26jdaqlxZBmNUDYgt8K9/tX+Of4LEMyNOiPoK+OY9JqbNu3K1BQqWQBTAaFK7DI7B2/bbZ2ULZJYFagv89RejmXMgGDeCBKfSw07kKoSFU7bQSqcGf5snnVQzJVf0VE1HrsSgBAk8clkDviT4k8IALBSs6LVQW01mtkD6XEg+KxqEYJhJtEsq5b1hslGugsEUFNeZhMFYFPjsDuvPcCVXkLlypdtaUnp+THJzFTRcbm/yC9VD7Y1ACY44AiAesudzsqDr2muhiJQtpmOB5bVhefNMbcOChmpYBE0QWrEoY+UqU9DzDPexV3KFxKqlyhUWgfECmZ0OS2bbMGsWi/gGgql8LF9unjDFwWIWtGajXKENi4M25raQK1INENEcjRuBvqstEIFug223dSRXuK0iGRQ/gy4oWMY7gVxhKGM874+DL5DeFsjHPTzmsnL1bOw44kyAQ8+DV19NOpL37noedU0YoHm3fSVfGrIE+umUq8K1a4xqdkiuxLYqLbYF4pOPPab9dnJs+P6g5zDbbv0a9QfjxrlmPOdy3Sa23o6Z/Fbr49aAsgEGGUKlRiyQQ9WbEhdy9cJPXwCs10ofUoJiU65InlFpse6RnzGD5YEZeaukzUgmOVeLF2vmGMS111qdDBxISPAXNiXtZMYp5+rXjb8a1sgxPcdYjmMmtkAEDe+zVq4mT2bJ1xtBbtO0QM//hb4/QkHZ5q6lXFHMmDEDmiXNk5Bw/VUW4Sq0GFpitc9e0CKESarW17LB/mGzk3nzQDmDQc+9bVLrZsrrmNSO1XEwMDvlFHMgzIZclWxn5h/FdJsRToi4giPDhP5bmncImaQTZqGv3FCucLCVBZpMuZLAss90ZWLdwAqzfNmXX0ojIldbICFXF39zArOCnH++/Pc5fZ6B7bbLjS1w/mxo4OSqwBxtndQrJ1sgCzqxgQerchWTbr+Yp+UVNDihFkG6TWKfq6uuEl60+ACIrdjWXbmarQXnldi8kz5XYCVXiWTCqlwN+hbOvW4Oa8Gz13mZJeeKiEaS0LxBm7mK4vXg798XgqDtz9haeRDlRq68QqZcJTm5iie1nKuEMPNu1korRxy+CwPLKJb5rrD6XcO9u7OaxLRXV1a2wJRpC0RyFVjye0bVArMB3U+oEHm16hX5IxBeZVfxqtbHPJMrHnBlQwb13rtWfKWV6KfK1aqVPsvcwZUrHKsjpCdROuWK2rD6lPSRvo/lzBE0NBMHQjpbIOLQQ6FAvzbSKVeyZsrUWW7Z/4LSjOddLKZPHv4YBIJJz7ZAJ0WUne/d1kNgh6dgxDBzw2zKVZ12cdWHAWp3kORuJkKWHnTpcq6CK5cbxWiQOKUlV5naArHO+HLNqhfeexfL8cDxERfl/vIXQv70PFh/IGkJzGkejwyJHr2gdveJ0Khvb/9UqUGGYsmYhfyIylWhP4268vktmdkCh5KF3g8+MCuuRrtZ7fcelStxYVNmkmmu6AdNN11ry7dyswXSfmDje4+3fE+myhUdi8WcK7cFPQyBTjwR4KMnlhoNmmv97oEBLrBf9MI/AU45EODM3aBOV2RpEaROrVwh/vCHP8Cdd97JAu716LsV0NDQAHfffXeutk8BHCYHSq4KSmyTiq+5UVtlQUyYAM36BGpgqxeclauylY5l2Pn38IHC6UKVKle77GncjusTGX632D+DY5ch2xuTRJj8QNrsLgxlxsVPJ3sK/5ZkYCQIEqLBg7R1jesB9tnHjKx4/hSBa5DL93kwCA++piV26629Wk25slUL/OVnaNAnqGA4aXm/rGaHo3IVIuRKz9VB0OAzW3JFVy9lv0umXJG1A2eIOVerNbtBZZ9hgnJlnej5SiC1sh17xjq45x5sPCkPvLwiumgFNK3UlneLoNGwvCFiUfmQjMcpp8rVhmprtUDwQ0NlGUADqYuOqNLIVYz0qqFgAQXmav3xQMvj4XNOZU8WBYtaZgskAQzbR4sE8pLMPbmi+wmtMU7XWVmZdZ8UjR8BhccfbnvdmtensaY3sab07GqDvKWTK/j4jQU70CyC9x95xJlcrVlNkvALTOUK0dgcy1i5QpWEKgBiQRCKhqgZLKW1BSIOOcSzLRDfL36Gky1QbGyOhUeMyoWBzMiVNN+KBIZiCWlrk+oUdF9nEqdNI7bMSLnij/uIfTwQaYAeumKIxIo2ELaTq4LMlSsy8IbPPMVyPP75T+32P/5ByJ9uCwwEE5Y2EDQfWwbchlUnaPMNov/Kasc8I3afFC4q8jlUyRBAP89S0CJaZ7UFjjT7dWJag6FcNVdYfr9X5UpcsJG5WyLX3QYNV15iqxSIQKWLqsVLa7TiKV+v+Np4bLdBu0mPY1bKlU5kMb93zqwiVm8GXbuyuecPf9Cajx943lBI6Rdd7ZGnun4XnpODhyQBRn4EEGoyFg26VLXAwYMHwzvvvMNUgq233hp69+4N+++/P1x55ZXwwgsvwCOPPAL9eM3RNsbDDz/MCnCgTXGnnXZiuWIdGZZJniTc4gBhmwipNv+nP0FzM5EGzpkIcOTp7OaQ7hJyVWBOgLzXDQf/nnTKFU56+Jq1K8zgqhg91Dozwn4nNJdGtvrXu3sZ/HTeT/DhKR/CuD5jLM/x7w2lzJUjtAZKlath8j5LMV+DnVxhP5E0JdkdbYHoaeJMauutwSscydWECa45V16VK/+COdCkf0xBOOXYbyltzhV+xu67s4iFkquW5FxxpGtKLCNXniDaAvVDXjlkS6tyJdgC19StsaktnCjICsh4QUBfgY8mg9CEyhlbYY0CHHQQhEPacYmLOWM5tAUaiszdd0Pxb0ut5KpbGTRceA5AnVD4ZaMW7EVr7C4F3guNKV5lZvCOCO2zO/vfi3JFxy45uSozlCtocF7tzpUt0Cu5mlpkJZSFiXpLDgrHaujPKqQ6EdSWKld0DMZUGFwTuuACPTrV8577V5o/Yt3qkJVckYW3xua4Z3LFF7Nw/sEKlLLtEc0tVLkqC5WlJ1c77AChbmFPtkAZuXKyBaai1uoXFrU8VM/sul5zrpzIFV9MoNcA306OWFUDlNeY+2RjRFI45rPbYf5HO7vaArsFzWOIY3OPOu3HoipFFR1bzlWmtkCsyPThh9rtoUMhfJCZoyxTeBlB0Rf6ggUpR+UK86Rl27B6T9OJMGDOMugVNuMRsXmuRbkKpLGuhWvZojQ9NpRc/fLpNnDy7pMApmltZkqHb2muHH78MZTwRaMmc/EZ51KvypU4psgWVZp79Nd7XNnJVUN9Ckb2GGn0qZu/QStiMXXZVIPQb9dvu5zZAmme5L77+tj1grnSn5BUfJyH0OJIe3StggEAY8ZA3UHHun4XjjV0HOLnNbUFFvjTSOntDBlPR1gx8Ouvv4ZgMMisgU8++STstttu8Pvvv7NqgUiw7rrrLmhrvPzyy3D55ZezbZo1axYjgliAQ6a2dUxyZd7p062PXbmqrjKNwCedZFoYsPHMgJkAAe2qG9Z9mNQvzsETSDn496QjV/g81nOYcs7tAD9oCfLFReUs6xqnrYSuyPD301QlGuQg+TtgxAEQIJETTnCcjAWS5koYL2ohwh+Un+YJX6Ot6iALGNKQK0dbILYg4JGuUII9K3JVVATxcEmLS7EnEuYkVhBKWiZD2WTgWC0wpP9zwAGO5MpWKtsj6IAvI05iQQvPljhCoApSfqPqU8/R21qJl2AL5AnTVG3hE3FZsaQsmQcU+huMHnHNBdqOKho1kK2EhntqE2hcf1wEnlbiscqKXE2ZwvyUJXz/GcpVQCNJdUKvmuW7AzRjEGyf2Pg12PDrHICg9aCFC322nlpecq4c0ngN5crn0q25NZWrJ+Fs6LHOzDtEFI0bAeEj7YlPTcFSttXYYyrf5MqyPzEhESUZfGzSzsbCR221+QYMMKly1USCezdbIC6u8sa2OP9Q0PeJK/m1TY2WYDZtAQ2/H0LbjM25cpWKWJUrS5vOUJ3jvJaJcsUXE6h6a1OuNmw2Gpqz7aiVL0DMf+oqS9AqKgolwTIruVqrBaeYf0Mr61UWVbbMFqjnWjFccAGEiwOO5Ar3NwuS9YVLJFe8EIRIrlDxwsp56DjkwGO7Ommqev03NEPPZRsdyRVVrkK+NAWGfjoDSpYfbXmIFmqY9fDlULW+GOBjTYIrxcAfq08gamqgeFOd1jie2ALxfMiaXK2yM1P2eZIeV4jG739hSs6IHiOMCoGz1802StTvPHBn9nyubIHcIomxBf2NdA3/888NF6CBOUU7Abz1FtTy1V0H4Dkp6zNG1V90y3UkZL3Wh/a/HXbYAY444gi46aabWFn2RYsWwZIlS+Ckk8xSrG0FJIHYc+uMM86AsWPHwmOPPQbFxcXwNO1M2sFgCTpJDhL63W2rjHx0PPJINsPwga+oEE0cPsMSeODIA12tKPgap8a7CKdJ6KeftFxqhnceNwOtPfawWN34+1HoOflk62fQ30RXpSm58ukFLVzJlVPCrL/Jrlyh1QX9NfiH+OYbW4TgaAvE13KkIVd0nHAjCm7kykm9YRMYqaIWLTBH2AKhnLkbuZIqV4ijjnK0BabL02gN5Wr//ckdQqB6RYP6mQ8wZOK+zspVIGIqV4RccaJQ2S07Bhku1DY6UtoTmvTJvwk2w8lvnAyBAu25BBbkkPCHZDxhqyTH94FncrVpmeYbw3Mk5tBEuE5wHeCiw2+HQpz04+EI+aLMA1K/bKHjCn+mtkBbQQsCply5IF/KFT2/hw+YAo/0nARnwtM2uxo2uy18/H7b5yV69YPEd2brAg1J7+RqxIeM/PcYusJWEdBtDGY5oxx77mnsZ9rUFc9pGlTSwiVuyhXadrhlp2+3vo7bI5KrzfXEahUuTa9c4XZMnCB9fOZMVlvAcYzAMYWWZ8frBaf/ww8HaFhlrcqnc1AN4ToocFCuZAUtWqRcbaqFckKuqurkVUsRi4RaLpSgFAfMY4hjc0WV+Tm/b/7d2RaYSbVAlGZ4/IQn05lnSttj0OCc2gILBOXK6Kunn2v77We1lTNbYK1Zzr5/HUDP73+RkimRbIlVKmVY//i/LffxWpC2tYgVaoH/QQcZD5UsX6MV2iHzC5KhbG2B6zfbT372ebggJVOuPtNWo8f2GmuQkDu/vtN4/ugtNeKYK1sgP8/ESoHsesMJaPVqmPWAaUnkmHPiHQCjR7sumhnKFVnk4bZATq7EBYqOgKyno4Jso6hWQDQahZkzZ7JeXBx+v5/dx35cMkQiEVaMg/6175yrmKtyleJh5B//aFllKy4KwGOHPgbnbncuTD1jqr4iANJmvWKJXZly5XShyqpecXK1BvpJAyub+ubzQq5C6W2BAQ/kihe04BWweN4VjoiUNLnZAvX8BoY93JvKUvucK7kiK//ZVguMEXIVJrZAfL8buRJL5BoT6aGHQoCogVHS+DZb9cALufKqXO24ozznatwafabp3x8Gj94BAj5y8vnj0Pv0CwG2fAPgrF0MyxNVW/gA31Pvv5IpCiq172tq8hnbv7xhIbw450VY16wliCeiWHrcboOK1EWhrjbZIuWq6OG7jYGgePgWFnJllGLXy55bsHkEJCXkKtywGeD1141iKdLvbGlBC4JCh95DuVCu4sk4XPbBZXD+O+eDj+TficrV4uGL4NqzvoKm3SZC6KtPbeOVzN7MLNLjTXtTWY+ZAJdax1XXUuxDvgS4qjfsdOsF0p5AjuQKl5I5Jk0yto02dcWxgpagbmoyTya3KZ6rVmIxC3F7xPFlc6PJnjFg9UKuCnZ0qHgERqcR4/30M5BY0fs4J2FLjP/9z7lcPkOoDgqq5A4XHqziNZetcoXnKZ/b4rWNUE4C7ao65+opYn4s9pDiBIXa4JhyRS61RVWLcmMLxEQavqOwB1nPnmz/8rlZPHfxWqa2wIICnyXnSlbQgl7DzBZYZ7LnAbUAvX6Y56xc6TlYonPDK1AZoeqJgaYKqCissJCr4oXLAKLWCbIltsCEYF83lCsnW+DvqwHmzYNxvcxcsClzp7D/kSD+YewftM9tgS3wrbe08xxVaq6QipUCQ9f/VbO4DBgApW+TctI65ie1ecYTuSK2QK5cOS1QdDpytVyvEOMVq7DNdBtg48aNkEgkoI/ewJUD76+VlSJjFSMnQ3l5ufE3aJDZ2bq9wFJhhihXvUt621YZWXdzrHqnL+PTykbnbn8uPH7Y4zCofJAxTg4erE2m2/xF8xinI1fplCvZxcTI1c47w3P+M6QcxG0ydyJX8VjAqCKTsXJlsQVqO3B9gz6pulgDpbZASsIGDYLkYHnwJO1t5EIUYkIytNt2cGD+gx/MKC+qqyIIcaXRjVyJgaJxfMrLIbDj9ubnbDADJhy4nUrrt5ZyZQl2iDq1zSp9ptlhB7aPBpeS9gS+BBx6ZDPACccA9P/RCDgsOVf6AN+zNDtyFSyM2q/joHYORkGbvJJov5P0zGpKFUFtnfVEvvhiawn7dChcr/cl2mEHKNnrALPxMlWuiOJpIFYMKcnkz5WbhiJtu7qdfSQj5Lvuqv1lo1xZmkILKKp0XmhoKbl65sdn4L5v74PHZz4Od8283kKuvrmHLMgFolBVBPCvf5wIoe23sn2OLNDG402PeaJHA0B3a46aK5AAhxphdf1KS/U7DukYjAeUK1c9erD8TX5dx/UehTLlat1q7Qf07etC2oRKgaJyRa9TcXypaiTlrUOlnvpqhYYPdNyOadOcyRUeC/qZS/U0QxlE5SpEEukpoiu0343Hky9qyBQ+JD5c2ZMFhnw7Y6mgxRZYU+88GYhzKo5NvOx2ESFXQUhorSZ0/Lrp19xUCzSqpFiTpPj1K6rOeF+zBWoHIVTgsypXkoIWNnJVv9qqXJG3iAUtONnCCoCec3IFiAW8GJortNxzvCj00sYleB4QS2BLlSsZ3GyBDajMPfQQHDpa6wVHcdy446BfqbaALbMFetVF3n8f4IsvNPUIz2dE6VxrjFUw/2eDcJfqc5jsnK2zP+VqC+Q5V3z+FYvCdDpyhTbA8847jzUTdkJNTQ3Lwxo/fjy8/vrr0FFw9dVXs23nfytWZDD5tQm5Mu+gnCpeMIxcYT1MfRR3W2XDGhMLF2pN17fbscmVXHktaCFOBLiaEgqE2Jd91c2sAHT6kdUtIleRiM/wHaP9wedLeQ66omAvaGH0DeHKlYRciTZKNmiiR4VXh5g0CeK88lRLlStf5gUtEEHSNJraAgvDvrTVjZzIFZ2UA/vuZX7OalOmxPPhv/+FvFcLdNtnlu0mpGAEz6vYYQf239DyEWQDErDXUPM38YDDknOlE4VepdlV7QiGJZafAt7wSnsuFQ+hb9DzZx57rHflilXbw1XG116D4sJSuy0QJ3JSZdJARE4mwz1K2Gp2w0BtEav72JnM1vb11+bxpDlXXpQrNxT1Lc+JLRBVqpu+uAkufPdCQ5l8ce6LxvOfr3rbuP3kkwA3vE+8eAXa639YO0u63fiYON7w4j4czX79PNAbOKeFToBX1a3yTq5+NoMeHI9Y7hInV9GAxUJsKFfRIqjeoJ3jwyW9bJ16XLkpV+L4QsmVV+UqRMYsN4h9rkRyZcmrSqNchRzKv9dh/4dUKm2TXVpCWmZpMhwgUACFjUUsrxGxqcbZ+rq52lq/myo/lFwF+vWGPkQUouoPkoSsqgViMTBeORcJBrG98+tgmdlT2iRXzBaofWE4bCVXXpQrbgvEVIa+TX7oSc4nqlxh6wxech4LrHglV+LYyRQqEU09jKrFcPDB7L9i/HxhEQzPZTeFhhIqLznDbspVIxSz/NmdKrdmhS04cKH5+j3NxSHZeeqktMpw330AdfXm/N5to4QlDRzIFvFThx1he+qNNwAeeEDeXoWiy9sC582bByUlJaw6YN++feGQQw5heU1//vOf4ZRTToHtttuOVQ/EvCYsanExLq22AXr27AmBQADWrTMnAATex+2WIRwOQ1lZmeWvo5ArlLOlyhU2ptKRzsKA78eAnxOVltoCxUEGgyyekNhUYibzDFhtEnW3lVIncoW/a3SlVmodm1MmJZGmU9AV89XbCloY5VdR9RyvNx9G4qQvbaL1AQsmUbCBkloC99zTsUFvxuTKhaS5vS+YMg90hJIrvdCAWwIuP8biuUIDleBeu5vbIShXY8Z4D5jp+9x+F34ePcfx/HM6l+l39yvR1Fl/ygf7LRbIVRk5130J2HXQruZKdqTaUbnKjlwlwVcgWa7kNlzSJJuW9/UCr32YCrGS3YsvMpnasOeIOVfEZmt+gXwsDPerBDj+eGjQCQoG6nhMqJ2Xrtp7KWjhuv0De+ZEuXrg2wfg5i9vhkd+eARu/uJmlnPy1TJy/YZdllnLVxgBq9P30VYRPMChwV7C12RtmpkOOgnDYLKoxD6wSMdNagncW6voZipXAblyVW0quenIlZty5WYLrCbVHj3nXKWvA2K8XyRXoi3QE7kK10G4SL6oVTV7DsBzz6Xdbgu5kipX2jz1G4yGi+avA/jncoDNw2BdtXN0vpHkUYnkpNBvBvqB0SOgr0NRTXS5WG2BYXZd8sbOjkH5gw+aD1x0keUid7p+NeUKbYE6uQr5LQUtPClXOjFk233McdCLkqsmk1whseIqHrYF4K1e0kE8P6XKVVMPk3Tp1kBWEEiwBaY7x9xsgVkpV9XV4HvvPfjXYf+CrXpvxfbRf47+j5GH5WQLzIRc9e0Zg/ozzGT4UtG18/IbAChEfPQRRI48XvoZl1yiWQwzLWiBdsQuYwusrKxkhSLWrFkDDz30EIwaNYpZ8Bai7AFYkOBkluuEeU3/J20X3zoIhUKw/fbbw6dEcUgmk+z+LrKM4A4CGbnivQ5sylVBIeuTJE7u6S6so7Y8ip3kuAJy+z63W3ywLbEF0hXsWJF2EfmwRtlMszw+DUDECVUkV/x3YGA5qsLsY5XUm796U67q7coVbdbIrYHEYjNnjn21i203TR6fNCntyhklV27BcUyIpU4/1JTNJD28DQRISe9oyNyYoiJf1rZAi3JVbq5ARhL2AiVuRDmbaoFYXENUrpz2G76Oz/3N6wfC6b0egv/MGQUjeAA1cSL779gxJxjvGdN7C0uQKCpXaCPk5aaLC7PINw3EIOWXkSs90A6Q5xqFMo05QuH5pxsVr4zrUQ/w8ZyujzjYApvlihH2M8EJkAdJNJ+CI1NboBuKBvWEE0Jvtki5QnvLNZ9eY9y/e/rdrHwxt70whNzI1TKbGiBCzFUUlSvT0p3KSLlC+CTET6r4S8gV38/JeND4ajxmhpqgN4xGjLCusbnnXLlUCxTHl5qmhsxzroTfd2IPvRS4AHw/HaPw99L3uo2z1RtI1Biqg/BQeVpANVYEuvJKSGyscp1f6KKMzNJU4NfOtyiEoSlZqqnD318IG2qck3Y2VEW8kastR0nJFWvZEghZYwA9v1O0BlrIVVMdwCuvmBZTdMR4uH4xBqjG9/JtDPlZkMwLasmUK0uxj3jKqNrK4pzLLoNKQq421K+XFrdgtkAsOOEBFjuopPUMQ1OFqVxh/FherilXgi0wE3Ll2RbolHPFC3b8+98waegkmH3BbFh35To4Zqy1k7jMFpjJwueg796AuhnmwlO3nc1epYhYsDjjRT4Z8LrFxRZ+bmAcho2i+bjc6ZUrjqKiItZM+L777oM333wTPvjgA1aC/YorrmB2wPYALMOO9sTnnnsO5s+fDxdccAGrcIjVAzsq6IWy65AdYUzPMfD4oY9LSVOipNyIMOlJn45cjek1BlZethKq/loF1+xhBiEcxuqn7jl3mhBF/zVdsYoVaoNSAcTA94OpXNHtFAcAJ3KFGFa2hWtg7hR0RcBkgAV6vyxLs0aJNRDdNiKijTGTXKEyOmpUWuXKc84VX4Hr9QsccGQfeHr/lwzi4JY8S3OumsLmxpTo+THcJiMrnpk250q43UwsiJn6usX3Oe0PFBLFEs9Odjg8J/nnVW32w7MXXggV72xhLsljgIABZHeTlE8cuC0jHJxAiTlXdHBPt5KOqQnoHrLAH4MUJVA2WyB5jvROySWKLtB621muR6KeNEQbHWyBZTai+8ILADfdpKnFaLOzfGYLClq4obAkCP13HgyvwLFZK1dYBZI2psTJ+65vhNYhwQj4iTPAgu5WcnXrrdr+ePbZDMgVL8zhWbkyX5cI2S1ctjEYv5Ar6b16AYzTkt6tVeKCpnIVzp9yJdoCGyPNmedcCddb+eYl0LPSvu/we9GhRANdr+NQ1UJSHCFUD+EyebW5erS2b9oEiYcedSdXEjuxZVsTkpWxlB+q6txsgQlnchUg5KqyO/QdZG9G3K+blodjJVfaHVRv588HmPX7Svjw9w8hGjO/iy2A8kH57LOtJRjTKFfVZJIqDAdYegBf2KHVAmX7Es8VPrYwcrXjjlCwxySjuuLGtYulFkFmC5wpmai9kCuJLTAU7QsFgQLzJNt/f6ktMJ31NBvlyrFaYLgHzIVxcO3/dobfvnX+UqdqgV6vi9jcBVBPrr/SrSZanyfXb0vJld/nN8YiTM+g6m+nz7niWLZsGXz00UeOxSFW0/qobYTjjz8e7r77brjhhhtgm222gZ9++omRQLHIRUcCDdh3GLQdzLtwnpHQWFxsjTQTxdb+CxxeJGE8wZ1kWGt3eWfiIg4yFuXKHzbIFesNpcONBIrkio7vQ0r0wDnph3jMPmo4bWNzyiRSleH+Fjma5ipQciU75aO/LTOjCFRsfb7c2QL55/jjUBFcD74Z043gTVpZjL9ct2IgNg0yB+duxWbk87e/MTU/K3JFAx7eENcLudpmG3kvrHTkCgNYfA0/HG4JsvjdYsBzcvRpiyVQNvGgbZVPrqJyRa+HdBPT6adrfxb445DkuTYyWyAlXk15Uq6IainaAhENkSa5ckUmd8zxwsIAvG0CtfaIlaRyrlzhR+25J+wMM7ImV4urzICM4+vlQglhH4C/wMGeVbLOGCcwuL3uOi1X9bTTnG2BOF5Zzml/ZuSqIGj+sESBB3L144+mdWCvvYxFNgtJ0RULiy2w2RyUkJPlIufKtgBEqrh5zrkSyFUYmqGUK74EeF1SUohjtVcFvXoTGQzCdVAUlp9MDSFtYoo/9i/X7ZY1H7dsq8wiW7wRmhqdbeDVNUlvylUAoM8B1h5OCF7kQEau3n0vBWPHAkzcqhQOeuIUuPVLs2FRYLpeNQTPI9adGjyTq9pGsh/0fcrHiXQ5V/XN5nu5QwcbKfFG8Eyt0idBWtyi19INEF+9PityJbMFhmNCe4pDDtGVKzu5cpuXssq5IrZAf8A8/o09B8Mf4Xm4I3U17Lafs6ojswVmYg3Enox15PiKY3wuyRWCWwNxfJVZ8js1uZoyZQqMHDkSDjroIBg+fDg8//zzRiXBO++8E3baaScYjKXn2gEuuugiRgSxzPq3337Ltq0jw20iKokIsj7pJZUpuXKDWA7bqbqXG7mKRn0muUIirleVpNvpplzhd9LfMbB4lP7B8tVGJ3LVlCJlgYM9zfK2PGBEeYkH4/PmASxZIlXGYgtIA5JDNbKbzhZIlSs3IpaI65OtPw5F+JnTp2vNYNMpV6Qi2PoRFVJyJWtK6dUWSAnGShjkyRZY3HMji/skfZktkE08/PP497oRS6pccRi91VzIFbWF8N4vMuUqHbnCc9OmbgWikKSl/43HI67KFSXhLQW9ZkRbIKIxGnHIuTJPViT2VJmhq88yW2CmTYS9kKsgxLO2BcrIFV3ZN4IHJ2ug31zE4r3QxN6WonJls/DqypWPfJYb+hSa82k0aG+EZbvWJJZAhFgljm1rQQnUbagAmPImwBc3e54nuHKFq82oFHi1BVJylW3OFVapLE3YG6Lh+4cKRVo9K1cpcqGFkFzJN6Z+Ry0vM9HQ5F25EgPDpiYoiEgGsFiRvBWCjpoa6/nCy2Mjwv5iy/Z0O/oE6CYEu5ygyMjVP67VzrEUWoC/uRLmrDH7SQVq9cn8sMPsOzhdzhUhV1iKnY4T6XKuGojKOaB0gHZj112hZ1gfo8MpiF16sV25euV/rFCIF1hy7bhyJVyWBVFhQf6ggxxtgW7zEq7Z8rggG1tgeUXcOJ8bSvvBT6BVLtxYX+Q4l8tsga7Xd/lSgIMusZCr+j8cbtynzarzQa7K9aIWaAtMp/52OnJ16623sgIWc+bMYYUt0G53/fXXw4gRI+DZZ5+FiRMnwquvvpqfre3ioCeyjVytXJC2k3wuyJVYDtuJGIiDlkW50n8HI1cIXb3K1hZY6u+lBUUSmR4DH6fG3k0pc9mqbt7Oxm2LNfAIUgHnpZekvze6cKm1E2IawoSg9VKciBj+zjgnV4EoFOFnLl0K3diNNMoV2ZdrB5r7vqwkfXZ4psoVRWD9GsegZoeLtAargqskI8sE/163347fLQbaRsPXdORKV65wcMcKVJwQ0PPXLWDD34bnm+01wQjEpeSKJy82S3OuctkRgl4zpi3Q3AnReCytLVA87jRASmsLdFCucN97UZ3Y9u+yCwQcZq1slSuOg0ceDH11FSYpK2oxwdrHBav3ySAqVwheSJQqVwGP5Q0H9jb3a5PfviJvIyUffii1NluuZ51c4Xn9twv7A/x6ZEaElytXmN8S8Ac82wJF5cqLLVB8PAwRKG20Fqvi3zvE2vPe0lPKDfVA5PRwHRQ6kKuG3fZgX8QKRpHvyEi5+uwzCKYkgz4Sq5h58my7s5VA1gljHlV+wn7BPTB+PPSNFHi2BTbTwHjDWEuVVaNh/J//bN/mNDlXNeQE4MfRq3JFyZWhXOE5t4XZ92zzS88APPOMNedqZZVnciVVroQeWf4mQcbt2xeKR42RxhtuC540FouutzduTqdcdStNGgs3DUnrOfXRf4QSxjqcqlo6WtsvGwYwyOzrGSvvBXVHmfUTQsnuraJcNcYarcpsV7AFLlq0CC655BIYN24cPPzww9DY2AjTpk2D2bNnw4IFC9hjRx11VH62tovDcZWvuRlKFs9N20k+H+TKSbmyBr9JKbkyAt4MyRWuANHfgeXYWcVAiXLlFnBR5WrNDzsBbBxtJ1cnmEUPsPSpjAhFa5tNG6HueUunXFFy5UTELCQjENGUKxxk9UIcbgTDR2L1tUQlK+/mnVzZCIqDckURfOs19uMtz6MKcMFWUDJqVovJFf9cN/uFTLli5xr+oO22c514jMRlYfWMkgQ3qxH/bTLlyqgSJzzuZgt0KG6aF+WKNVxOYwsUr0uLciUjV8H0OVeyz3Xct926QWDr8dkrV9Umubpoh4sszx22xWHQu06vnhiSqGwHXma5KzYxdVOuLORKV67o9gZJRU8LKn6HXfc2T/Z6n91ybzkfMaLl+VbokRs92kG5CrOiRUiMpn9tHxPc5gm0TfOCFmK+lbg9tjFKD9qxsAJ+f3a2QCRXa6VjAw7BHJde6v65TijploKCoJyRNYSLmSfWUMKdbIFuytVbb5kLi6JyhX86nnl5I9zyzpPG/fpa6zbR4DMkKFfIKEeVDvGmXMVDUL2BrAhUDbP0B2Tkasstrb0fPSpX9U0R29jNxwnMqcEFLEdLaSQqJVc9e5m/awN+1JlnwsZXnzOfx17oHoNxaUELYQxM1trP8eLd95bGG25FpujzkZ/mp902fG09IVcYWhjkSiBxs/+7RPoZTteX61jJ5ySci/fYF+pTUU/kyovVMa1yRQqoGT1Hu4pyFYvFWEELxMCBA6GwsJDlNo3B+ssKeYXjRPTWW1AS3dwqypVoC0yn0DAUNLJgjr82G+WKBsuicmWUY3exVMjQlBSsJT+dbu11hRg2jDU+ZpgzB2Lr7OWAjFWyw035PBfKlWUlKBjRlCtWBrbK2P/S9+KDEd3b4I/BWkIiy7ulj2KdVrU8KVdrVgDccIP1/Kz8DaDPXC24Xr8ein/9MWs/Ot8Gt2azspwrDMhYjXjuqUxjC+Q+fp5QTQd3XAl3Cti4amEjn4EIxH0NLspVREpmsC1VXpQrSc6VRq5kHVELHI87DfCk1QI9KFdsM3zetz+wm6kyZ6pc0Sp/WLBnVA/NUnze9ufBEUMPgl4L9edDwrEa8D1AifXa59ZREdi73TXo0pWrIJHggiHJCT32VYBLRsE2I01bUp3PrpZZzsVPPjEHHz3/08kWSBe8MiG7WOwFK3nJKgWK27NYFAp1VQDLRmOOYzbVApktUNKwFN+P1ws2Pr39dmD5cLL3pwMGsU5jcmNzHOAvf7EqV5LcOUflCgedt9+WW1sF5WpIz97Qty9aL7Rzo74+4EyufPa81523NvtJIrbqNV5Orl4SKnBiYRNRufrLXxwv0qIiub21qjoB0WjKdv65NRKm13AT2pR1DCgbYClYwcE54YbVZn+UXtECiG3rLQVEdNhoypX1hIlW2y/o4n0OlNoC04EtsqRSEBWa8cqAY0YdFsvStwdTCfj8YisUM19oMpZGuXIdb41qpgDRXgMs9tNAvGW2QJ/D9/JrlPa6okVzukTOFeLFF19kKhUC+0lVVGTWl0UhO9CJyDJhPPssFENjq5MrvJi8kqumZePYKvwee5iruAU8QRObE6ZS2H7HAFlwZZgwwbyNzi6qfuDvG1I+RBoYOm1fLBGDOO1zhSjabFeuEKT0bHyO1X7JfclsZicqVybkyum1lsEqENF83jg51a11tyHMnAm+eMAkV2SQquiWfpDysoLsSK5wIr7zTghuXGMjEE0zv2Vl/4oOMfNAREK1ZIl9f5x1lvb/y3Nfhpr4Bk/bLyVXO+5oeczNFojgZYBlg7vT73dTrmLCNcof1/4nB5usXmcztN51F+O3lEeylUp6XB2VK5ktkMDNFigraIHqBC+v66ZcpbPT0H0b3GOXrJUrrjahcoKqy9w/zWUljB879DHwvfoq9N4csTQLNhC0HzuxfDXHzTfbyYmoXAV8AQgEzEjDXyCJ5vWgeljFMGMfVqWWuV+v771n3j7EGlyL5EpGhr3ME3Q8EYtZpCUzhFy5zmkEsmvZiVwhUL265hpzYSJT5aqiPGg9XiK5GjsWEnvvb27f8iXe+1xNn8660EuVq9XbA6zUe+0FIlBe1A1KQsUAYW0+aqwPuihXEnI1wdoOZ/c1QTm5+l1om4N2N6pcVZSbFWwkSIQ3Olc3JOOJoVyR806sGGglV3LlCq2oHJv+dBpjHRvJOkHPl96GWIVkhUMCbHL7z3+a4w/L7RKUq6bNFWxBl6J44q5SW2A6YKzS9MN0mO7WS4/M/7SlTfcyv6Fcic6NJuyBhn1ickGuqHIVtZ5nwYQ3chV0uOa22sqbLVBs99AlbIF77LEH3HjjjcwWiM16m5ub4f7774dXXnmFNRmOe4q2FbKBdJUP26J/9BGUQIMncpVpc1cR9P2eyVUgCl//80JWGvfrr80Bo4A3asTlo0WL4O9/10puIwnDzuAUV18NsNtuWrW5+++3ThA4ETI/uWzV3QFswCCDCM17sfS6Qpx0khHZxeb/LidXqFphSTsdmdgCvSpXxT01z3w3vWu9ozXwiy8gxSc1f9woK+6VXKVbXRJvS/tvkIYfPp/2A5v0/KUiaJL+1nvusZeAvvJKgLvv1lSkU986FZqTzk026bUhzbnKkFytIvtZXOV3+v2OylUwAlGRzLMvjmqBA1WuyOo1LXziFUceqQX4eK1w4P6gx1VWit3RFuhGrtLYAlGd4MGlU0ELr+D7NrD7LlkrV5satfOysqhSa74eCGmBPnqN778fevOfUyCwPb06HV01p9cVxYABWkVFvQK6NOcKG536yQHxB2U9JOKGVZV/76aEPZA35gKMADm5wvGKeuQkOVduypUbuaJBTzpbYDpyRVVqr02gQ/6EK7kSkZFyhaSmpMidXOFcfMY55vfOm63tewKq0loCw9df194jU67WmyuI/lCEnZ/smtLPvVjEn5FyhU3Re/q1BY+rpgEEHnrEfmzJWGMBVa4OPsC1/8T65HznAhxkPBFzrsTfQLcd0axPjAX+Ast1R29v3HtnVhZyw3ZaxWBspdF90kGWuATnFSdga5XLL2dFCBkGlw+2LTAlYkHYKPDH4sJSxwJabsBY7Iy3zoAZ/cLelCtiBa3oHjDIlRh3sYq92CA+x7ZAPAR1EfNa88e7OX4+jVdCDqcLqvp0/Ud8PVWuaEXSLmEL/PLLL6GmpoYpV9hI+LLLLoO5c+fC+eefz3pcdevWDSZQmUEhZ5BeKMg0UinP5KqlypVIrtzsWQZSfqhdY19JKqDFFWbOhP79seqk9ifmmmBghcQMq80hARNtgSxATRMYUrBBHYNJCl3mtylXSJr02tpiU1+DXF1xheWxTPpceVWuSrYYb8m5ciRXOHrxpFyhX0+PsuwHKS85V4t3Ook9mdJX2tkm6DaDJtykiRMhdMHZ0vcikaLA2iD/+IdWMe/Zn55lPZVshNijLdAruaI5VytqVziqMk6/3025igvXKH98bK+x1oIWxN6aDbniY4Pezkt6jjnnXIVcCzO4FrRwUEL4xOhmC/QCviAR6CmX89IF55grtKlJI1dihTv45hs2BpnkSiCC+v0RFSPS2gIROH5tsYWzcsUKQRDlylcQdWwejOcet9+tj9rJlXEu4gC5Zo158QjJjWIpdhkZ9rIIl065yoRc0dYWZG3KFeFh/aXkymtBDFeE6tjquRO5aopoxySxzfbGY4HqjbaIUWoLxItwyhRtm3zuE0S33hvNa0of82JRZ3JV4LP3GsTzZtqZ0+ClD0rhFiwgiYXGVqywzJ2BqEPNfapc7bWH67ZuTC107nUpU67IeeduC9TmDZzbsSqllFyhEl1YCBt1VwA+h6SULliefz7Af/5jtqKUgZOrcDAMvYtIszQdYjU+XJSBqKSnSBr8suI3eDn0m+tCMN9PLOeqzhwjysv80jHZ6DWJ55bQ/DFT5Yq1xOR9+LhyFTPPM1/MWyn2AodrDs+9gw8GEAt3y5SrLmkLRIwaNQpOOOEEuOuuu+CTTz6BzZs3s2IXWJr9UL0ctUKeyRWOXv/S+m2UCJNza5ErT8oVWQWjCJWRjZk5U3ss5G0yFMkV6+GRgXJFfcQGIqVycoVAA39JiSWRmSPWZxArEWt5jC5E95sJsK3ZF0Usse1ZuRq3TXpyhYrRtGmQSunbSQZKXNXzUi3QCfS44MqXLJitH7glAFqGSbJQQC853TR0AMuv8z3ycMbf987Cd/QvTiMJOhW0wGBG8CSky7laWWvOqN0KvJErQ10R9w3a/mTEMBBlzcCttsDinJMrEdKcKwyoSDAk60eWqXJFJ0Y3W6AImR3SIFcOJCqdLRCVM27XqiwWktlwkQqDfv5zxJwrvdnziB4j0ipXMpIh5lwhWQr4Cbmi5Np40CRXXCGKQj0EAin59+iBu60QTxY5V67KVYO7cuVuC9QOXu/i3obxgsNrB5fQ+NEZKVcZ2QLDGrmyVTnU0czJFe0fhCoU2i7SFbTAfLh169JfnGgFHae9TlOutLEhTtpreLEFIkYPmADH738pFOI8jZP1Aw9Yjm04KvRw4kiYE32g2F1lqfXr1XIF1NVaF2tkOVduylU8njRssRQiucJFE14tkD9H51T8vWg+2XNP15/BusIgBhTby82vMNfZGJDAiflHXvDU3dUAG7ZwjVW4nZuRq1rz5C0v9zlel0y5wosJbactIFf96+y2QKpciUqnE7kKhdzjR1t7BUlBCzrOdAlboBuGDRsGxx57LNzBlwEU8kuuHnnEMN+W7LVjq5Ara5U+j+QqGYRA0C5xFZSTCxXzrrLcDkauSvpmbgscMtVSiporV5bBhANltfvvl5Z4jeqkh8KyX4Z/DFC62mYdkr7WLedq/LZsFKIqpY1cMdUqCUliC+TAZqFFpJFspnAaEEX7Jnr7Ur3NoCugv7ApmVmtVv752Hts1ppZNrKYkXJVamft6WyBFnKVoXJlA05YMnIVjMCw7sOgIJRqVXKFK69Itrk6IrMFUuuqk6JBFylkOVdelSvsdcuB7XQusxbms2yPE4lKp1xx1YrbAi2REyZfYD5HqHtOlCtXchWIMcWHkqsUJdcc+rXbbfYCUyHyYTPopP17MMrhLVDwJCTFdXJd0MKiXKUpaOGkXPH3oX2S3RfcCG4Ib71lRuTK8Zp0Uq5CzspVcyRp7x+EeaaoGqL66aZc/fvfxmPxnu5lQEeP8tuUK7SnOV17BahcOF0Hf/qTefAffhgKN5l2Z3+zg3IVd/k8gkg8AjV+eXsDliNG5mSpciXkXFmOoX6uDO0+1JVc0UUTno/Fjw+OFR47HhghSP8SO8sXbYHss2OZK1effrEjwNNfW4oWOZErPAebGgosc4HTuczIlbjAkoUtcECte85VvKm4xcqVK7mitsD6LmYLVGgDYJJSPG69UBpqTC0bvdnHWZOX0QLOFeJWU66GOOjuySCEeR1x0RaIpAUxa5ZN0naDWNCiX20ybTI+BSNQ/iTABRPs5EqmaiHOOgtiEybaHo4G7Sv2FjUKg6T+mjLHCQitOOdVuSoprWQKGVWubIUA/vtf9l88qR8oErRh086WHH8xeKEDKCo2s2cDbK+7Zeih9Ot9cDJRLuiA+9um38wBXkZQhGpdspyrZKmdpaRTrtxsgemqBdoQdFaucLVuQI+eUnKVTRNhL+SKbSsG1y4FLbwoV3QhAs8vx+9Jk3P1xBMaocIYFU9hGank5AqvHVG9YZvP+/GkybeykauHHzZOhp6HHe9Q0EI7d5EIpytoIbU4CTlXSJb8FnIlUa78CcCaP+Ebb7EoRKFCSfnqjz4y8xyRndJqJg45V042Tr8/5UqQaKGXluRc4fjGXYyS3rSOCG81Gsr040HhFNBJdoXLh9ttgcGCpI1cWcYOft5hJRkn5WrDBoA39ap8lZUQK3G/sE86wexDxsfwBC9SpMNSaMAXdiZD6FG98EJ9w5qg8K5bjKdSjRUtIlfLa5YDFNkr6LLPTvkAmiqyzrniKie95mTkiva4EpWrTCyhvEjE0NJRjs9R+IT8I89o6gmwSajYJTlfWSn73yZaxj9H5cqnzxmvvGIJyjJWrjB3Xcy5IvFQpKGwReQqHHaoAKpsgQqtjRmvroApY26G5w55xdKTIfjmq6Zsce65UDTa3m2U59i2Rs5VMBwFmHSz/E3JABSWSMgVXmA8GseRZNEiz9shFrQoXrAIipszVK4QeiUmBt1DLVWudMQHCxUXHMiRhXQiuRr5PsAet0HJvvfCLbdYBxjPyhVOtEcd5WwLRC/LBx+wmwnMA0OQQgk4cLmt4l5yiZbT5hV0sMbiCU6VgAL6SE4raHkBH3BnrjaJKbHeG/AXNqRVrqLF9mAmbc5VjTO5cuqz5apc0aIV5HE8LoMr+kjJFX5eptXOvJIrtoLskHOFh0zWr8lGrsjEi8qoDHxixJw5sa8Nx6hRAPfeqxWtcZqcqZJG85U4mhIujd+EvlSGLRCvGWR2+pdWnnKufltuC8RCFHx1NRNboJhzheRi//3N3xAe8a39A3wJ6BYF8H30MfRZb25PQWHU/j0PPWQ+gB4oCcQ+V07KVSiccq0mRq+LQWWDsqoWiFXZqNVKbP7rhlC3EJSOsefFOF0nsvPY+cPt5Kq0zCTyEb20uMUWWKJPRm+/DTB/vly5euABcyI+7TSIJ5xDr11OfReO2Gkb8xrVg91UImipm8HnMFSgfdwG7kSGrr/eGBAKXzF7QsXr5dJ4MSnW4EaullYvBSiWkyuGxp7u1QKFnCvLotjabaTKFS5GYbVNg1w1kAbCxb1YhgEWqqDfSY0dvRzEOm4FPWYLu6VWOt5nUYpdltMmghZ1ikw73zL+Oc0vzd30/bx+vWY/zZZcbbuHtRQ7sQWiNa9BaAeQqS2wsDA7W6BSrhRyi4YGeODU7+Gk1ffA6R+dBKv+ZwaZwRlTzaXt226TkiZ+YeWLXOHnGvI7LrEKxRMMJINQVJxwJ1ck7yobWyDMnQvl9VnkXIXqbYMlTeAUIW0iLBEkLIQJrWxYDWzf66FhjysgltImXqpcLa5aDIe+eCjs8tQu8P7C9+XKFU60Rx9tJVd1KWslKp1txXj1KLIKhcGv2/HHCo20wpwIsRwthThYUuUqoM+YiVSClcD3Cv6ZCzaa5e/L/PaVcn9hbdqcq6gk8KcTD5/UqS2QroiK5AoXojMjV87KFQZzPUpLpAUt8HdktPJOziuZrS+tcqXbAnHfy84VV3LloFzRidErwRYDZdwPlkR8SWzSEK/1bAs0Vr9feMFsdnPCCVA5ZIyrLRAVL07AM7EFWhTmQJTZ4pBMhkZ9BbDlm1C41z/tH+CPM3KF6Pv6R+bDIStJD65aZiyqMAnIIefZVoq9oERqFgiFXS50oujieEKDIWN7gunzb3cfvLulSIDXfCs+B5XusKXn781UucLfRPvl7rQLDTYltsCJ25p3/vY3NvhZlCvM0+LkFye9yy5zrSb7wJ8PYTk9BhEhizJ0ruHkCsemRMLnToYwifG228y2FDoizfIgvzRs+taTsrLxlFzpLUzSkSsvOVeWwD9eDPDrITZyhcUt+OKITLmix05cuMRiCpj2Jh0/9Gu0JGhfiKMl0Q1kUS0wXZVG3C4cF2RwVa7IvAVPPpnWFuhErgZssQMA5kjrBVfwfOPVk3FRiRUpSdNEGD/b788u54oqV5gOwNHlc64UcoySEghtq1WIQzR9ZiYrGqVc8Urs2bNNyBW1BaKVxI1cBSUd79kFlUty1ZRhtUAHcuWmXMkmxWnTtGpEjq/zJ+DwLQ63WinIqhq+dvLUyfDuwndhxsoZcNH7FzkrVwMHQslYc7Kp/1Gv1IRREubgMXsUQDQZstkCceBqSSl+kVzRwMyNXPnJSJtJxbhQgfYhv202Gy6GEnY5JilU3tKUK2vUGCG2Ga+2QAq3nkAUjjlSTjlXaAsMl8P/jdnHfIxXetS3K6OcEUIUHS2K9DdZyJVZ0AL3oWysCAq5kxZbYBrlKpPjLwbKGFjQgEAWSNfH3MnV5qbN1maheJKiosBxySVsWxkZFAta6DY0fB8/R9AWiMn0Xn4DrYqHNiq0BaKQMOzicwFOOBrqYLU94PEnoJuuQPeZY+a1+HQVzfie118271x8saPUIMu5kuUWFcgaGuvA38tzEQeV21WrdOQqCIXw+Wmfs6psNGDNpJ8b/o7Svba3b3dB7pQr7H2EKiq2NbjoYnN8iUQlY8f2W2tJYwj0tL7xhmURofDfL4JhOznlFDaGu+Uq00URqlyJc4KVXJHtcRJFzjuPEW88zcLgvshREjCr+25udu4tuKR6iWdy5SXnCmG5pJbvAVv2tBNpvjiCxGpNnWlTxcqClABYch114HUmq3LMlSvZHC9TrpLRFqgpZAGNAveRk4rrplwxWyAvsYwKql6dw+m8cCI/Rj8x/ZxDwsQXkXBRyY1c8XPTLcYo1OcUcW6R5VxROC3ctWcoctXOgZWROBrBjJZYE8K//hXgjDPYfTdyRQfkfNkCA8GUc7GBVBCizQX5Va5mz4ayxgyUKx4UBuJmMKFXC3TMuXKx8OGcSRGJmqNar9JyGNdrnHW1jwQh+Jk/rfvJeB5VLJx0bNUCdRtP6ACzf038w0+12QitADNmsMcS47fR/O7s95kTM66W43fKgp8x+oK9G9xS4twGVJq4n0neVcG6lUbOFfsctIJE7JFSst5a+Q1/X6LZei5Go3ZyL5t4MLDiDVspnIo1eCZXLjlXzBZYaS8MwCfBTMkVvzbSkSt2PtEFEbSq6LZAJ3LVlKptkXLl9fjLyBWFLYD0xyAVdrEnofO42YwMmPr06acAv/yiPYCRtD4WsVVxB+WKkSt9lTiWjLmSRbqNvBIZQ7d1RkEHvs/qY3VQWJiy2wIHaPkmfckaUDJoXe0PvvdfM0mOd9yWwJZzVVAirYoXKEi4qn+cOMgsgelsgfHf94bSTXvZLM2ZECCmXE0kde5zrFzh9divn5b/h2lSPUoLbavzFlsgVtOjJP2CC6Cp2iQkRY88aW4gztlp+iBa7K/+gKUHmrNy5YHc4mCCvZB22w0K05CrIh9R8JvpyoAVbC4L1wAUr5e/oLFXRjlXRlEkHeFkpaVpMJ/vY9/8CeCXY1geJ3U2sKrBWYIrV7JjI1OukpEWkCsH1QuPnZOlzk25am72AZyttzjBk0GvIp2xLVAgV7FYyqiejGOmuB+cyFUq5T4GiXOazBZI4US62jMUuWrnoBNVY7nZpTx46UUAd97pGtS1pnLFlAIn5Qpf2xiU/zacxfAvw6IWloIWjUmAefOgWwbkig7qRSWJrJQrtxXa5ZvN1bQB3fvCkPIhLspVyjJBIOZvnG/vc6WrJ0HSRDW+aKnWY+uCC4zHIpdqE7j2YvND+mJFRcnAhueEXgcjL8oV98dnrFzNncVWyjm5Gl4xHJoaJcuyWOqeAPdr3WbrDGnZl5LfwycetJzIBniv5MqxAAVOVmIej/44BnOy4hF8u7xes9gnCPk1/y18Md0JbAXZQq6CaW2BkYCVwNBrxWkf0dwer42Eg7hYkwm56rkAGn9zrzhKm4OzyVovv85w6aXWYhcOOVeoWtG8PLeiFnR8WGUWaAMoWW9U/+M2GLTAFBVLbIE9+gKccw4MIQ7EqM+qFAR5FU60pLl4QcU+V3hcbAVx8PNCsazzrdj70+QIYo8brBJIvzsTAoQcobTcn9ecK4rioqBt/LfYAvFcPPZYs0Ljhg3Q9OWnxvNFeuNh+MtfjOZnbsqVOJcHSEENPo7huJixcoXAgeaTT6CwxL20ZhjMjdjQZM5lUnKFrpUzd4cLL68FGPYJQCk52ev7uOZcycjV4ceSRZDAAMMiSV1vC/9zIcCrrwGs3AF+WPODnRykwejRLVOucH8nY3owlKZnmRQkR44C9xH+XNnY66pc4fB0zjmmJIUulsZGZ1sg87fYgbmQDPpCOS/gwsc+r8pVMpmdciVeexxOj7dnKHLVzkEnxMY9DzJuB/e2Nm3AQIovXLRmQQsz58resJZCFhQbxHGiXhEHrROL5WVdXQtarKlmS3rdJOqYE+iKe0mJtqN80bK0yhUfTHAAdJsgF282G7gMqehvKVnMPeJ8sIvGkrZJZt6GeY7KVTBs7kvWdws9LLwYyK67QvTI48z3EbWEV/YSzwFc5Br5/+2dB5hU1fnGv+19F1h26VU6CoqKIHYUK8FGgiWI3YgNMXnU2EtIYjSWmMTYk2hsiRr924g1KvZgbBA7KCh12WWB7f/nu3fOnXPPnFtm5u7MnZn39zwLMzuzM3fu3Hvuec/7lRHkiap7/YcFFsQ4F3L+vRPFn39Cq5e/Y03IR9aOpOZm+4X2yKNbiXa8x/Y73q9Nm/M88+KcJiRy3lWg4kouniL9ni8cTnNivlb6PWe51Y7cnHHiRCNFz3jtp800PhvGJEcVVx5hga1F9hVqca6wO2WUdvdyrnyK67wC+8mlis+YCWT9h7RpCXdKdUbuX1e9egPRk5HeaYMGmfFfEbTOVdkGQ5DxZ5SPD7eiFvJkxuZcVawxCmOox1VVdUdsWCA/fsMNVDN2R6qLiJGtrXYnwYhiYOtZV7/eIyxQ51zlFzkPbMJ1jycskHevet5xc/BEnStGtxgRpHPltN/a2vL0YwdfEO65h2iMGcK2tSM6eBuFcvfem+iKK6TXcd4E9bwrlMI0xTjG7qHISeFwXN/iKvIGpb3dw6yKOqOPr9ni4VzxtWVoE93ymyoqO/kHRPtLi3ub+7nmXKkFLZhVLdEw8IquaHii4Kc/le7852R6Z1VUXPXjanc+4FRLjqIT67rxOle2RQm3sMg4EcewTkS5OVe8zR0DBhMddZT5C04su/1257DABr3Lbzl/kXnDtpbohVznXHHl92OOMYs+f/lldI54663u80cnccW5VUaDZgWIK9C94mqL+4WEV3Q4YTMdzpWx0hynuLI+mxwa6LPflS0s8DtzgpNQtUAewCsjA0ik+7hrtcB2fyVev1gXzdYe0muALbRBVC2zOrFzCKFYBXMQV3mFbVRSUBLz3duaGg8caFw1WuRmk1LOlZO4kvr9uhJPQQuZAqnEn5hcc2Xgjz+2omT0r0kt9NV90VGaS/LaBN4ei+j621fZ8+Yi+7VRSRiOS1yVeYurn/88XnHV4iCu2ozXdgonjCfnSg0D5Pke1zjhCt0HHeQnLDDqXPE+1B3jm8jeMFScK24x8bacK59hgV3KarAqPmPGv/oPqWHpG67Ot825+mukJ5Qokym9oOFcqTlX/d6zkuhl58qtqIW8jda5XLCNetQUWBMI+biqVMUVhwXy46wO/vlPGtFkfiHblOqYhZVlRA884HmgxBS0KNaHBeYrDellPlobCaMk0ubCGB+xwB52pKveydekRJ0rp0qWRQ4OQnzO1eaYiZx8HrRFwou1jgAnjr3yCtEhh5Cc/lt25A9NIS+9kNvCnBqyVRTJPWVEjpx8/YrLuYrglXtb0Bndad9vsfdotLalbatVlp/HZnaYDMFdLjWF2tzXNedK51yt2GJWXGRKO2u1Dr1Fc531GhzOreu7pmPXXU03mb8ugTgXdN+N6lzZxJVbtUQvSu2LM2IfOTlXbvM349i45JLoL371K+poaYs9TtesoYFr9GWB+Vgyjn8r50oSVyWxOVcMDz2ipYIYZw44wKzM+Oyz8TlXTiGAEFcgcOSDztZ7w2GVTr4Q6MRVMgUNXEuxG86V/0pwtm1NIO/KJq7WNMYtrmR3qrravJp1tZYTtRf5cq68xNUnayKFJrjxaO1gW28O1blqaigmWtRE9J8TrOdw0rittKlRHjkvVlwdNYfopJOILrvMFKbDhtmFhA/nyqtkt0D9O1voX4HbY/nayTUvtrtVtCumVvr6xUhvGE1JXg7h+XT9p0SF9vyBwobYjo+6sMBknKsrrzRTduRrmXtBizatuKosKza+V6f9EE9YoFOOlWO4lDYssNgad3TH+LrOz2z3xbniVMwiUeeqSxlLPMMCa1bQpqZ1ZrM1HzlX1Q88GrVAFMvfEFdqH6WaFWYRDEV8u4UFaie5FWuob1V0AlhZFD2uKqpaY8MCxXE3aBCNnDbTvK24aoX33E40QerX5yvnyjksME/X0DjCB2s+sG7vUO/Qe0E55nR5nnw+yu8djwBiDcmOrirICt99M3nnqrgpZnInX4M7IgtXjmKG63z/3//R1onjzG3KK6TCvz0YsxFuzpVb9cbttzf3m9q8O15x9b+oOaQlTyq48N0WOaaVYsLb5bF5cM1gu9hojg0L9HKuvmiOnsMF7bGrVbZS6lLBDCOn2ME918HHkDxmuoUFqo6NrQVKMs7VxGhZfEacJ+qYn1/Ybpy/busnxjyPx4EjjjB/sXo1tb+91H5c8IX5lFPo1rZTqZo2aUW9EVpp5VxFH9cVtHALbTz4YKIZM+LLudIJKb5Oce5hpgFxlUXOlTqwpta5cg8L7FZxtd7cMaWtCVQL5MGkn7RU2NTfeEyuAvbmN2/SZS9eZjS1E4ON0/7nPj580fmmIbqU06uy2goD0jlXBhyO9Xg0vG1V0yp7IZKS6DbaxNXIsUR33mnO9iNJNmo4oSqu1Imqm3PFK088UNbXmxrOSUCpq61+c67cxL4hrvKiVzU5b818wmZavn65zZ1jit5+Pea14nGuZGfCSVzx8/fbj2h8tE6Ju3PFVfk04qqqrMR1AhhPWKBXAQtfzpUUFqgTV6vaoqvKfp2rRHKu1IUadfIdM4Es2kKbeD+pZTsdwgJrGiMHBOcpKIrYcKhU5yov2ng4EedKl2+lHlflMeIq4lxFGDFoR624qpgcrSjr27laM54uOuIHRrSaSqeoRKvhwzUfmq9VUEwjeo3wLa50zpU8QfUSQLw6zscjT9YikXcxoYGFTz6WvHPlERbY3mZ+EC8xszVSrdSp+ansjojPI0fI295fOQ85VTBZ58qL1pZ8T3Elh4gKcWXk4Tk0Ffabc7Ws4b/R87+twt25kgpm+M23cjo23MICXZ2rZMRVzUrtPorJi65odczFill85/jwyAt0LP/UflxwPO4TT9BQ+ppW9Z5IPzkhOpaI88nIu4rsf/m6qQsL1MFDqhNeYYGMmvOcia4VA3EVcuQJjnxCh0FcyX2uCrjUeqLiKoGiFrZQjQZzxxR3+V+ilEP/hg6RdlrjICOWXUwCuS/TrAdm0dWvXE373bsftbWbq4i6iefb375D/a7vR0NuHGKFV4nvigcmITJE00O3xG/f4kqzy72cK46P9utc8WTm66/NVDh1kuJbXMml2JWwMLdwQkNcSePskB5Doi08ipuIJvyVlq9bHutcScnk8j5RD6t4nCs5lEVGVL/1FFecQFwS64hWcaWxyPvr8kiSCQv0Isa5+vhoazLDx3fMd1OyiVY0RQLrWcS3txgV8zydq0TCAhVxpY5bMRPIwq20iXfl3XfrbUopLLCknaikI/IiHBKoYIioXl8QTfgLVdU2Ec0zFYjlXCWQc2VRsdYWuiRPNEsrt+rDAiNMHTRVK67kvBE3bN/nR3Pomy/0k5bG9fpxlIWkcb5FQgKLCpwXs+TxMQjn6kc/Ilq3LtrOi1HPl6JnnrCvQEaIq9pmcVPMQorNuWrPMxbenAoFCERFRT/iap99iK6+2mweqwuTLlYWoJYtS15cOfSZ1kbJbNi2RnveyuKKwwK1zpVmP3qVYueQeFFQRlcIyy6uondG18ZWkPTCr3PVbeJKDqF0ca7KKto8j2XrO+OO7JFiZx0UPRgKf7rAdoBV3HULXf6rcuu9HntM51xFL+w9SmMLWqjweXz++c6P+wkLVMWUUwXBsANxlQPOlZ/O2ck6V0W8PU6l2B2wCRThXvksaiF/jtatkY3o6b8Mq7g4seAZMkgWVwNtj3+64VOrUzhX8NvUvMVRXP3wgePNkL/1I4jePd32OeXGhyIs0C20MEZclfoXV/LflURE2ajaUdaKuzoZcwxlkxLSdZMfn4UdqdClz5Wbc1XWt4a+ksQKO1fceWDhLc8TnborUflG07mS3DmmaPFT2tdTL5p+c674GHGaJKkV+Rz3ZR63KminAqUBbE1F9HV1oYHxOFfxlmyPca6WXGDd1DpXpRttEyo/ZdgTDQvspFbX4yRm/CvaajpXPAP/xz9cwwKrxVcwZ462e604T+nIuTTrT/OJhpqJGfGGBWrH6NKNjs5VccUW57BAItpz8J7mxFQNCywM9vjYvE6/QvDE8ieMRuDMPkPMcuqJhgWqzpUfd0ntdRbjXDU3EN1rD7Ni8vnc80lZZUdMCJLtPOgoppaOFm/nKnKcOzU/VcUZhxdzTaKjj459rhjDBdzz2pYzXFQRt7i6/nqia691ftzWHyqvw3be23pc6Zwrdug1KQJiP/L+FftFda5YwH/b9K2VR6sWMIoZJ9m56jSfs329PwdX3SaxXW7OFQsX+TsLLCxQEVdOOVdlFeabu10LbN/ZWWcZRXrknOyCfzwUfZzV/MyZxvWLjzvOf95331hx1dleaDbO5OMsv5dnOKtX4/oSH2GBalgunCsQupwrcSL46oHhE/nklnOuihJwrmxCT46H8BEaaPucZN5p57LFPrFyRUqqaPBgaQDfNMj2+H+/t+dwbGt1dq6+WreaqKOA6K5XidaNjdnnoqiFCAuM+S5ql9OEPhOs929sjk7Ey8sKEnKu5u18LD0y+xF6Zd4rVs6W6lw5NRT0IhHnSm6u6SX2y6ZPo5WRcbakq4DqK+qNicPBh/DOXG4JXtW5yv/CnhfkFBro17lip4HFsR/nynmSaO6QgjL7Sm1NRamrMAsi58qJmGqBykptrLhqMBYahEjx00A4UedKXahRxZUuLLBB7CcuVaVR/o2R7a7ZFjmJpOptMiL8j/lsYzSsxk9Y4DOfPUMnPX4SPfrJo/pJbkmjcRwLZPFUWNHsGhbIjXflZuTx4jc8rqMl9oBr72ynm9+K9nKaPX6262vEm3MVV16Uk7jicEZWDUqH2Lz3o3knXlSK4kYuhUA4qsFTXEWOc3lhwamXE1f0dKNUOfY3bFDOvZI4qwVGxq2LLyZatEjf69A2Uc/viLkOOoUFGs4VXws0oYHyeCKOazXn6pO1kbDjSFiuTcQ4FVZ6xUx8lXtJxoMYN92cK9W9sjtXCRa04LGXe4T5cK7KIxWNfTlXxmvnG7G0HcOiobsF1GGqnz/9yZYszPMB+bs3wgLlnoyRpvZFbbJlqEfubqFDHJsICwShda6cXA+boxM5P7xCGAJxroqSCAtMIO9KJ65ae8SWbXVC7hHCRfZU50pcvD74PprAzdhyzFTaS4k2jLQl8crPFUUt+OLMPzHfYVe+7QLx/aboxK2qrDgh54on70eNO8oWiqSKqyBwE1e6UuyCVnIO4i47YA9aJeLAN3VSXuQEGFA9wFb4Q825ckKNFvObc+VW3lcNA3Rqzmg4V7wvSu2zhZpyd+dKFxYoqu0GHhYo8d13mjGmbGM0bEcJidOFUiblXOW5O1fasMAekfd57TWz2ogEh3JZ4oqPAy5i4dB/wHKu2LnmgikRvMICX13xKh16/6F099K76eiHj6aG1mgj2egHabLCc2PEVXmTa1ggc+NBN1It2cOfOHQ5SHFV0iu2IexvXv+NVfJ6XN042n3Q7q6vIX8/PF6pk6lEnCsV9bwwxBUvwz8sVYLs6KC8vz/i+zVFcSM354rHbnnc/Xbz13TO0+cYubkCEVYu5xvKcEsyrkH0hz9EHQMnSovzXZ2ruEuxS3AIF4eD8W474QRn52rpd0sdnSuu0meIqkgvQgNNaKC8H0VooK4FiYHlXLmHcxus2sXmXMnvwxFyXohjz825Uq8fNnFV6px3KeCICxXjWqBEXTiVYhc98HzlXAlKSqhj/MToa999hxnj75YUZTlX0k4QRY7a7M2cdXNPp2uTQOxb9XPI31n/SvsEBeIKdAvyQScPeE4iSRU/TJDJrvLr88ksBr6iwvzEqwUGJa6qfdYUlxPxi6vs4uqtc4jePoPWN22OKT0sYu5jtl3QXkK0LrY8sXiuWtRC/Q7zO8toYHV0Y9Y0SqWjK0sTcq50zhAXpwgCN+fKT1jgmuY1dN6/znT8u4KeRbQxcpHp19hlZrXLjQ6tN7A7V074dq6UsECr94cGUezD60IuQo3ySu0T6B7lFXGHBd52mxlS//rrARe0UCZwOudKPic2bN0QIzwc3yfOghZe4ipmksFhgf2lbTjnHNtsqPmR+6kjInCrOwrNma0DsnO1fmt0kqgrxS6Lq3uX3mv1HuL/X1mp6btV0mireimLp/wyZaEhvz0m149dr4P6Hxf9RXEjvfHNG+QHNwFTXNxFNPg14/WGzLvU9hiPVVe8ZLp87ODe+YM7HZ1cHTxeqS4TXzvEBJXPvUSq2MZM0LjfF3PBBaa9w1x/PQ36Sqq37UFNdb72HLT6rnUWxThX9/z3TrrlrVto97t2p7e/fZtaO1qjuYgO4bL8XXANojPO8N6msrJYcaWG5CZ6jefrw6xZZr6XfK1Qnav/fGcv3S03d+drFruqzLCew0wXW3FkHJ0rJefKut5Gcq74+qaO2zHXvI4SOmD4AZawk4+lxx8PzrmSt8PmqJU0UYFLbzjmmmuI9j/d3mywqHyL3SFyCQusiGyjb+eKNM2uZx3mlhRsIYcFyuKqvLOvp1D3ioJpbfV2rtSqwLrS7JkAxFXIcQqbchJXqXSu7E5afnLOlVzUgsWVR0IPX0DEidxK5oduq/RXU1ztbq/mzdD//YGuvcAcqI34b4lODvtzFFelVLJxUsyvLeeqTCrH3rw25jXyO8psFY+EwGNqyksTzLlKPjfHCbevyB4WqHeuXvjyBdra6bzqt5WiE9v+PJfgZd7IZMK2oq+s/jFTit4JLCzQqzHlCy+YURZPPOH8nOLCyJetTDpqSqs9wwLV75CrO3Je8tRIfYNExZUhIl3OWSdxJSrG+RVXiYQFdpD9O1UvxjFClKsFlnSZDWyYTz4hOvlkc6b08svUePZp1lNrxkx0rQIhO1cyupwrERbIY8riLxbbnv/fte/Fvkhxk6O4ilkBV8ICBecvkA7WmafRS1+9RMmKqyFD8qjHmYcR/ayOaJj99f65/J9GnhHzk11+QlMGTqF40IkrTowXE1TeLrfFGd/O1Yzp5g1uYHTggabIuugi2oNepbl0L40atIX22MP9NXtW60NCCgojg0V7SYy4YudKCOrb37vdFrLnt/m4G6VS03iG+9ap7xHEAqrTQi47V0u+WWI7dzk8WBz7Y+uiMWUsunfqt5N2TJbnJqKQC4cFigUJZtm6ZZEnR699arsA9Zo3tuckevq4p62wd/E4N1GXw938Olfy68vHmKNzVdRMhUWqnRZ7DrR2KcWcyrfFRF04hQVWVOTFl3MVIZHjwogMUcQV5x3ntzpHJzBOCyR1dbFh9G45V6q4khe7MgmIqywTV17OVaL5NbrXlweY4mTFVQJFLURzRcO5GjHCElle8MWxK5IDwxN1/kzqxGPpkl6WuyLTFRFXTmGB5Rt31mxnrHPFRS1iXqOj2CauNjRFB+MeUuGDZJ2r3Xc3+10yl9oXqRNGnRz98IfR25P33Kh1royVT81FWNDUGd33/ZoiovvNN2PdK41zdf8xT9Lpp9tzOfyGBcY4Vx7iisuxc37waJdiVSWF5hfRWayKqxpP58rvRTHeSqCGWIhHXEUmPHGLq4T6XLk7VzGVFTkskMP+7rgjuiO4LDsr0X32oU0d0ZWgmhHuie/sTOmcGXGR52R80dBbFLTg8NSvN5mTbEFjqybRvaTJCqGKFVcbPcMCmUmTiG776/dERx5HtP2D9PLXL5MfjAkNtwTQwAtMhuNQ2BrTRP2xZdES5z+e8GOKFx6v1JwqHuLF9SORfCutuPrj76I2Mvf84/yrzk4jBejeSz6j5SvKY1on2F9gC/Uo129MgZg8a8IC5XOIQ+XUSn7JUl5qHwD4vYMKC3Q63+2fr8NoY3D4g4fT4s8XGwsJVm4Ui5vedgWzU9+dtKHaOueKkUXbZxvMfNmC0ujfq3lXalhgVX6dbQHPq12KiliUEjnksnMlH5uOzlVxM+UXuUft8La0dNlVYilXAPTpXFVW5CfkXCWSb2+ELdvEVZERvdHUmJ+QuHr2WaJRo4h+/GNz7qH7fPK2qeJq1wGRBbMMA+Iqh5wrHnQTWSH0I66KuFFsfpcxIUj4s8lFLfji6EFRQWdUXE2erO1lpEN38VN7PRWWtBkXke83m5UCLSIl1rXOVUcJ5TUqjW41OVci1EZ9ja52SVy1VNL3H5iNKJleNcE5V3zBeOklorvu0pf+DSIs8PLLiebNM4sWHXH8eu2F1BBXysXFSVwZzpXo4aHkXeku5MPOOJD++EezhHO8zhXnlAQd810SKVvdUbzB8bWdSrH7XRCJ99w2HDoHccXng3p8lpXmJxQWmJBzlWf/TtXjONa52mqsgreNH0t0//3RwSWShW5UEvT5fbKw0uWQyZ9RCHCxei9CpEQPKPOFYvdtr56F0ceVstSdZWtcqwXKnHpsPfXb/UWjeMB7q9+z9eVzOz7yi/UhtLw/RQibHHLGr/v6ytetgjx+JzrysahzrvhrkZ2rRFAnmgXDBpszuSFD7BvC1SM4Bk8ZG61QP5ceV9ZnKBbOVWmMcyVf875u+Np+fZGaRCdKeZn9Ys9fdeO2zYGEBco4Vq+NfL7nPn+OZvx1Bp3yz1PMQkIO4mrejvOoQOwvh9eXj2uxv7hoiiiSUV2V79u5Et8pH09PPhkd1/2KCfnY5ONSFlfysenoXBVvNpr8xiuuyivbY65/TjlX1ZUF8edcqWGBPo8LHp9K5Dy/jhJjXuLV48pprrrTTkTLlxP9+c/RcUH9HPJ4IS8+MdMGTaNMBOIq5DgNeIk4V0E0F3QUV0V5VJRfFJd7FfPZJk+O3uakdK+/j8TYG+Jq1119d723xatHqpyp4qqj3XyeCIcx6My3Thnedu7HZ6O9lDo31zo7V5FqgSIsUP0OO9uLouJq2Szq3BKZ4A16jfr3sffNcrvQvP2292oSN3LnJNtEJzaM23yOBRy3HLrlFqLKEr1zYYorZ+eqoSPaiLl/YSRW/NFHiT780N4wUnW/hg4lmjLF8XwQOE1I+MK/z9Boqenx9YlVoZKP77Iq8+Bsr7PnE8qTOd2qZDziKl7cnCvezer5WVdtumzcTHv9lvXd6lx5iatY52pbtFHwEUeY48dhh5lxKBMmUON5P4krhl9eCNGFCwrxJXKuxIq7CJ0z0OzbfrX2ybY8yeys+M5XWCDDIVDiuORtEO0dvMgv1u9/3p/ivXiyK0K1jO86knfG4V7x5Fq5OVfCvUoknFWgnWjuuKMZEvr3v5vJiTyr+8UvrJNIHgMKShShWewsrookccULBDZxlR+9ww3k5WbVbi0K/FJZGjsJaNzW3K1hgTInTpIqXRDRXUvvovlPzXdcjNqx7440a9yBrq9v63UVqRi4ctNKK1etZ1WRb+eKw+FWrjSvaTNnen8eFTkcmwt7yH3InZwrNSwwv6jV8xxoJ/u5V1XV6du5qq4sTCgsUK6sGM9xURFxygzayo3rrVePq3jyJstcHDhejBPjL4cj2hZSMwiIqxxyrpLNtxInqDhJ1abGxgp1MuKKPWOxhPHvf3v+fXGXeaU0wgF33dW3c6WLiVfFFTfPU0MCRUlS8Xk5gZMbQFq0l1JrU7Vv50otKdvVmU91pZEQtE3S6s2ut9rCx9zE1UEHEf32t8H1NQuioIWTc8E9xNzCAje2rbJu9ztsTvSByy+3hwXmd8Z2yIxskO58ELhNSP521N9o+rDpNHvcbOP/RFi82Hx/ThYfulfkeB7xdFziiueEbuJKFH46/vj4t88p54pTVfbcM/bY6VsTTYZm90ou9hB0QQs158rTucqzNwo2XHBOglu9muj992nTQfvG1ZRyUM2gmM8gu1miqAULEa7WJ4urmaNmmsenZt8O62MfaGTx1FK4xldYoGBM7ZjYXBUvIiWudeJKLqcvvie5BPeEerNNRLzonCvbJiU4RjlO0PgBrm9+2mkxVWZs4qq41bdzVVwcGWM6SlzDAnkx7ouNXwSbc1UUu4Oatm3p1rBAmSv3u5w+/MmHdMXesa0L+L0nD5AWRR3cNnX81TlX8jnUq7rCcVFMveaxqPjd74i+jLbdimu+I48l06cTfRotEOoYVq6GBeYVuk8++Dtp7bKrH2OtysG5UkVUj4jYdBMwXmGB8SzSVVcq4qrS7lzdcAPRMLNvdELiqtQjhP2hox+iOdvPoZfm+csnDSMQVzmUcxWEcyVf1OS+D4a44hXqOCoGxgzmvITEy0/M+++bWc9ufx9ZBTecq512SigsUEwo1IGhoz3fMSRQbDsPVjwYW7RW0Nam2FHD6nOl5FzpnDauGGhM4qTO81SzwlahzElc8ffN4X4yiVThSgRXcaVxLlraW8yQKpewwHXtK63b/WefHM2n+Mc/aNg3mgYoAqkEV6LiiuPO/zX3X/TQ7IeoKBLSFy97722WNF+2jKhHZWQf9F5GxZWRCe6Ql2wuSiLOFS/Of/SRtneqz/CP2IFETITV87N/j+gxyZPuhMICfTtX2+JzriKIHlwxvxeiy2eY5/AekZLSEYb1GGYlzKt5efzan22MTgxH1o6kA7Y7wOZoCCYOts9I5Elmc9tm32GBaiEB3+IqUoXNLSxQXoCyiatID754xwM+ft3EVaKLfokU5hkjFXOtHyPNoj2cq+KSTl9hgWqF2SDEFV8TVBq3ps654tdjl/TyfS6no8fZuxwfPPJgq1Kg13VHbgFic64iFQNlUVpbUeWr+bsQV7qpgt/jSnau1NeRK+s6Oleci+ox7+FtaVMKWvSoLvDtXNVExBWfS06fSyeu5AXceMRVfQ/puG0rp0n9Jtn2Da9dRSL0E1okKfUQV/sO29dY4NxjsEcFmhADcRVywuZcyas58slm9OOJ07nSTvx5yVzYImqtaZnmZiqKrK628eS9vNw+CI/5B+UVtNFf/uIeFigufqqL1N6Wb3Outuu5nVGG13UQ3NyPurryHD+n7FyxuNKJQf6dEfLGnecFFWttk3AnccUlelW607l68MHo7YULKa7JtVXC2iUscG3rCut2v/rtbHGYk2/6u+25fer/Zfx/zLAlRIOirkMiYYFBwoVD+Fixvr88op0vOZto30uJZp2ctHPFk9hx4xIPHazQnIRO4mpwbbQUL+f5JBQW2F05VxoRJSOHavkJC+SS0m73beXYt260Vt250AWXpp4xfIZ2LNx12JiYc4P7BOl6/nBvNKc+ScyY3tHXkgsMuNFVtNmXcyXGSMNd1oi5eHGbTPkN34rnNZ3g/lJcN4nPmT3OMFs7+HGuSkojNn17GTW3KuJKEdGy0A1CXBV0xn7Qpm3R84hFcSK5NfGIK8Gth9xKg6oHWZ/t4j0u9vXdcA6uHB2ic644pFLQq7LaUVzpQuF1C5WJiCu3Sndu1QK7POY9PD63dCoN5CuLYooxOeVciWqBbgumbs5VvNeHHQeOit5praCZo2fa5nu8z9Rw3iCdq2wA4irkOE2OnQbCVDhXQlzJgsRoFFlY6rvnEKPN9xHiinn1Vec/fu01K+eqNd88Uy2xwqtIc46i0b/eXRsuZXOuIqu1qrjikutcblYwuvfomLDAmAEl0oBYRTxHzbnSXRD4OzPFleRcla/zFRaoE1fd6VwdfjjRM8+YtUfUEAGnyfW2dvP4sCbmLs7V99vM5GY+rozJLJcbiliFO/zXnp9yTe+j6J/FR9Mdz9jDuRJ1roJGntCvr3iNaO9riHp9kdacK6ZSc5VzElfDevc3YuCFuOLQVoYFgG71OhnnSl3l9SzF7uVcSb/3ExZoNUOVnCsZOUSQj+XPN3xu/R3nJU0fPl0vrobbS0ryc0VZalVcFbfV26qgqciFBJat9+dcdRYqjYqdxFXEuZIrIKr7IB7cBFSi4ioR54o/J+ekfvghUW2d8v0UN8U0ENeNo01bWhzDApnPN34efT9pnyZKflfsObq5Zas1NhbmFyZUFc7vXEMeG7nP2nunv0f3HXkfLZu/jCb2jTapdbvuqNdhcczLOVcrGqPiqndV9AT36nPFzpVuodLvceU0lrg5V2pYYFees3PF3wcvgrV22UOiqytKzCJgmm1WxztZyDhd07/5JvZ3Yl4T7/VtVL/oXGan3tOM710OC9SJq3gWcssCagcTZiCusqygRSqdKxnDueJJdIW/xGrHE0xuRKLGuMk8/zwVk/kBW7qKjcrt4vOKKlBr25UgbJecKzXUoLO9wOZcja4dHRMWGDMIeogrnoCKiy1PTF3FVXNdNOSkdGMonSue9HM7Gbn/sw5DdCvOhShh7ZZztbrZ7DHG+8MIyeIfLnHYrx8VdhIdFFlUL28lmv15I8287TAqH2X/DuQLER8f/D1zOAOXTpdX+rpdXEkTek7cFqRbXFVp3tRJXFWVF1vuBZdjFxXyxEq2E1zoRoiyoHKunMLMRPU+N+fKV1igh7iSJ+Efr/3YEo0jeo0w/ufJyNh6afU3Qn3PWCdKLiQhU9yqNuCLDV0Vn8WPc8W5Ya7OVUmscyWqt3EYl5s76RUm7HbtSWVYICOGEln0my+4wbYAJiOP841bWlzDAoXQDsq50vVq2hxxrnTXr+50rkQExrE7HOtaaEA9X9XrkM65ksfF3pU1cYUFdpdz1bt37GI19/e06m1x5EVBe0zrCN12qOJKbq+iPledY8kL0U7iiqN01EidRJ0r2Sk7afuzjf9lccXjRTLOVWFAc9EwA3EVcsKYc6UTV3zxMS5W5f7FlbZKFAdmi4ZBb7xBtEHTK4Z54QXLudrWVkjbbUf0wQfmQ6IsKifc84TCT86V3JeJ6eosoO8aFXHVmZi4kp8jQgOdcq5inKuy9cbqlpzjIX/38muk2rnyi01cRSahUefKpaBFJHTQ1mdq8GBTdE+YQDc9Q3TaO0QPP1ZENTfcatZ+dzl/eN9ykQkODbrsMqKbb07dYC+LY9m9kUWX7nzgi+Juu0Xvz5oV7HZVlfoXV3ws7dzPVNIdXR1WRTk1ZE6FhbGYyPoNC2zP2+p6HDv1RnIKC5R/7ycscHzdeEMgCeTqkapz9faqaHlOIa6YH+94rO1vCos6tceZyD/h3BPRf44pKS7w3K/CvWKHyUu4Gsedz4IWLEa5DLsI1RrSY4gt5yweOMLb7fxKZVigTEy5/bKNtrxY20Ol0c/e2NzqGhYonJigxNXee+cR7W4vTbs5UhpOfGepFFd+UM9X9b4u50oca/y9VJWX+HaujNfQHNaJFLRwE/BiO7iQlUWROU515nmLK7UUu5WHq/kOBg7situ5YubODca5kt8vr63SEpXy+JuMuMqThpIR0SEzq4C4Cjl+rHqn54vBIN6+D17oJjZ8osXrXDmW4D300OjIwHFnKqwi3nvPEleODR81TYCdcq64LPmCBfYB7bum9XGFBeY3Sf1VJOTniIs3OzetbV0u4ipyka9Ya6xOyxMB+bv3cq7UcMd0wJMyIbDE5NpPWKDAVnad4Y6E//kPjXr8VbrttH/SIS99S3TmmZ7nAxcRPPhg/Xuk0rkScK6NPMlwcq6mTSO69lqi444jo3dXkGzf154DxHD1cidxxYnNXsUf3EJD/YYFtpN7QQuncdFyRN3ElY+wQBaD7572Lv3psD/RkpOXGGXIZeTFjre+fUsrrnYdZP8bW+8YB+fq6acj50Tlauq711Oe2ynnXQkn8UiyqeoAAEFBSURBVC/v/4Wue+26mIUlQ3wVOYsr+TPxfuSxU4Txqo094yVMzpUgRkiVbdCW4DffKzobXPV5L3roIelBl3ybQJoIc97djJ8RjX3E+l1zS0RclWSGuHJzrvh63NHZYTTiFn2O5G3xcq4YXQ+mIJwr3WI1FxGK/tJUZp2RKBqn/ccLFWrOleVcSeek+Gz1A7YkJK5UEneuoreFqBIClj8Pfz/JhAUy775LdPHF+ileNpAD5lxm43TAOi0i6gYDuYlwt4orXp0u1U9u4hJX3J+Ga30y//d/5qxY5uWXDdXgJK6KSzqsoY77tKjhC7qcKx6Ib7ghj2554iVq/8xcpf6uYUN8YYFynpTDdyjCTrqoi7a18Bdjv6Lxd1ZbOIioLTK6la81QpTkVWO+yd8lD5xe4kqX5JoOeHLNE7UY58olLFBgc64EfLVg1eGB3wtRKp0rAYtm+XvVTRbFw3wR6g4OHDmd7lBahHHTRy/nSsbLuWLida5aO93FldP39W2TGUqabFggw4UpTt05Uuve5Zh8d/W7WnGlbqPT5FVMNLmM98SdtxEt7G+siNdUxwpZt7wrDg38cuOXNPexuZZz/8v9f2kXVy7OVW1ZtOIA/60ICWSG1OgXjoIIC0xlzpWM7EwalG607QNdA23msYvNMCmLvA5DEMgFGYLsc2UtwMgl3yOKIx1hgUE4V/ICB4fycn6z6HHFbRCKJLHkVdBCrVwcZM6VW86uQVe+tgCPeuwbixSKCC8vKzCuC5tYXEWu90LA1NTzB6qIKyzQ2qSu6PmXaNSSPDcT4kr8z4/x66s58/FGyUyaZP5kK3CuQk68F550OleGO1HkL6dC/I0WzrsSIx4va6ijqbG8S1bOlUp1bXQCJxelcMu5EhRL+3ttU4MVetGnsk9U8EjfS0FRdOTv3KofpeVVI3lldGtL7BIcf2fl7VIOS/n6mPwP+bt0E1ccQXfAARQKRNUzEbpkiav8TspT+1Qp9KvSiCuf+F1NS4dzpU7y1ckiHzcJRmIlnHN18snR91T3HV88OYFdVLcT6I7PZJ2rts5W1ws3hwELJu0SPQdXNkbzNnQFLXjbg3ATWHjpSEZcMUb7h/KNREXbfG2nrWLguk/okhcvse7/6rVfGc2e7eLKOedKbpLMfycXs0jWueK2U2ETV2p+VXlNi2PbBZ4IO5LfbryWrhhGEMealRsmhx9GoigyJSxQHUvk/D12SWVhOrh6sKuo8SuuusO5cooEaVdyRNXtMM49RVzxaxtusXROCnG1rbMpYedqrRQ8JLY3XufKS1ypz/GzXbkGxFXI0U0OjznG+flpda54AhWJQU5KXPEoz5USGM65kgtb8If5xz/Mp2n6yDByFSh2rlQ2S/1k1GpOJSXRiePqTeusFU6u6lX476ul55n/V9Y1xKxi+b2gbzWcKzv8nZW3S05b2QYaWDUwbnHFZdK5v1JYBjxRHUrE18tlvAulME7fzlXA4iodzpU8mdVNFrtb8Ok+t+zE6pwrniyqvUf89D9SxbUX7OKo7y3Tp4+ZvM1pdg8/WGiFnercA7nQBQtaPpe7Q1xx1TZ2MJy+P6djUZ6Ay+NVvOLqif89YRTXkOGqjgLj3Cs3xzQV3r/yhDdZ50pdSe/Xj2jJEjOiN6hzL9mcKzUssFfP2DBtQYWruOowjqs+FX2887oSgI9X4/yRC2d0cqwZUXl+TUaIK/U7lvcL59baxFUCYYGfRdvMOb5nEM6VUwq4W58rN3FlnHOSmyzElZy6YLy8NGR5XdO/jq6JJOxcya6U2CYhrsRj6vWqO4tnZSIQVyFHPWAPOYTovvucn69WR0uVc8UnmjEZiMO5cl15lCtMcIU4wXPPEa0zJwhF9frqVX36Ri+SMY2APZwrOYm8eZu5Aw3Xiq9n3+xqPTZnjvl/r4Fr4gqFlC/oOueKxVVxm3SRLt1I4+rGxS2utt8+XOVOraT9SLK31efKCON0ntQk61z5FZfpcK7UlXOdc9XdqBMqL3HFnD05GhbFTUVlt8Zr9b29s9348aK1w925EuWd776baPjwPKtiIU/SOL9BRRxvcl5RMrBLIefLiYqCLLCSca7iFVfsGnI1Rmbpd0tjHhd5WIwxwXMoOMT7Vw0L/LohOkvjghbxoHNcp0whOvXU8DpXvXo5n3CuzlWeKa7UcYp7njmVdo8X41iTJ+etlUR/XEpPn/kno4l4EOLKaXKcyDjkNe7KQp4X2lRxFa9zpcPvfMetwbW6WC27QkyvsaKKlvNG8fHtKq6knCtR4t2Yo+x9pXG7olcjDR+emLgKoqCFmnMlHnO6PgATiKuQow54Eya4hwqp1dFS6VwZq/M9o13WvXAdtGfO5KudefuRR4hWRsJ9fvc76ylFQ5RCBxEG9Ct0d640OVeC0hJpJ0VyrERsfl5HZPTo8RVNm2ZO4Da2rCca+AYlckGv2S62fDL3yZV15YA+FXTMDsf4ElcNkonWI5hreuDOFU+seeIsO1elJbEHwvhZz2S9c6XmfGSCc8UcNe4oo3noMdsfQ78/5Pe+3ifeRsKqc+XlUgjHiM9ttWIgiy1R6CIIJ4HhXDnVvVJFpnrsxS2uirzFFYexjawd6fh4rLiKda64GiWHWcpOKp+fQYUFylpX9z0meu4lO5lTw3LreztvSFW5iwLMbzdeS3X3WGwlWmFRxXCu5EiNN88h+n4ibdtUY7T/6y7nKtHX8vpueLFJhBfzwodchj0R50qHX9HuNraoIm+NUh9rt9Pv8i5qUpmgc7XnL4jmzKLT/3C3bTu89u369ckXtFDFFZ/DaligCsSVHYirkKMObuPHuz8/Fc6VbtDiE864WI39B9Gw54kq1hBNuj3xN+EPcsYZ0dH1kkvMQhaRfCtOKCoerO8DM3RAqXvOlWS5q6vPZTZxVWyb3He1R0a44kYrvMnIaaizh+K4IedcVc+6jKhvNGyHeeEF+wD+8wPmaxPwdeIqUp3XPeQyTaild4W4MkJeSu0HOfeg6j/rVudqgRnoXOlWsMPgXCUirvg7u3b6tXT/Ufc7lq5OtpGw6lx5iWROghd8sdG+wMOCi0vHM/H0avJCFVfb9dzOdZudPoN8bsjjld98nf2G7qcNwWT+t0ERV0o1V+52wb3ajQT1ogrLBeNxTYQFsgMTU/whQXGlOx8Tda4S/TuBKnymjoytnCkoL8v3DAuMEVdJLAppF6fksMANUSHPLUhkARLk/uwuccVjiHDzDedKaiBsFLRwEVdBO1f8PN1zb7011rmSr828ENp/WJOnuGJnzIjY0ImrUrtzZYkrdq4KW4nG/JMGDe6Ka9/K84BEnSs1LFB+TbWQhQBhgTkgroYOHWoMnPLPL38ZrZqUSagLX+xcuaE6V3xhC7rPlW5ws8QVFyc4YX+ihf2Ihryc3Budd140IPrPfybaR+o1c8klVORQ2njYgCrX6mHCueJJSEG+faeUl0ijeqSvlZhEdbWLKhatVpgRNwP2U05cJxQ+2vo80Rk7Ex0y3/H5ct8bL3Elh0+EbaATzhXDFxohrthJKC62H+SHH070fYt5sS0uKE5qQhwW54rFheqaqMIkU5yrRJAn/H7yrlra7c6VlwEwsc9E6/YrX79ie0x2SYMKC2TUcN2pg6a67q/uCAtkjhh7hO3+gikLrFxSL+dqzJjoMcDXSeFeyTlX7CTEm6fmp5ptusWVynl7nO74WKnU58opLFANnUwmnNkzLLC9zDbGCQHCCzKZ4FwxYlyXC1rwccbXyFSGBerG3qOPJjrlFHfnqq5OOkc9xJVx7inNpvm1jX0w8E3rd1OnxlY3jc0Ld/8scoXgoJwruccVnKscFlfMVVddRatXr7Z+zj5bKZ+aofDF0A236jZBTSBdxZWARVaZU/anT3j0kju9Cvbai+ikkxwvrjUVpdbALYcbqDlXuslLRVlRjHPF4ooHKW4sbFDYYiXI8yTELZnVbbVbNGF1a6Qbj7gKYvUy1c4VT3Zjk5+7aHXTauN238q+SYXWhKVaICMXO2BUN4C3Vf6oYXWuEiHesEDVufJi/+H7W7cXPLuAbn/3div3Ss7vCyoskDl2B3uLiMNGHZaQc5WsuNpryF5WkRHO+zpz1zOtif63jd9a+0GXc6V+pyLvinsOifxIP9Ug/aI7hhK9LnE/NlE1cuHCxF7jmhtXU0llM528YCX1Knc+NlzDUh3CAvtWRBrGBbU4JYcFtpfazlMhQJJZVEu1uBLnIp+fIr9vQNUAI28xiLDAeI4r9fs9/XRzX6rzqe++sxfV8SOuPMMCd/8N0fDFNHzcBrr+eooZs9TFxXjEVaLOlSw2WVjJTZohrnJcXFVVVVHfvn2tnwonLzODBBWfzF6Dp7rSIk++U+Jc2X4pBf8mygknmF37amvN2eZRRxE99pjxYXQXA65KNWNGdCLLzhU3KNQ5V+qKEFNRWhyTc8UDvm31jJ2rSA6HERZY4CyuVF3AbgW7MTZcnK9EnavudmKSEVcclinEKV841EF5a2cjrd2yNpDQmrCEBerElRoWyMeKfFHLJufKJq7a48+58mJ83XibK3zak6fRTW/eFNNYOEhxtduA3WjqQHOpee7EuTHjX6qcK56MvjLvFfpu4Xf02TmfGftBVK7j/ShWwc1S7PZ9r15PdOF/arhjWMIC+XLwyitEf/870TXXJPYaPz+3H21trKA7bpDaX8QtrlLkXMnOR1uZ7RwWY38yi2rpcq54kdFYpJTGyCCcq3j2hepcCQGhbodI/WYGDozTuXISVxz+N3cG/fTeh4z5S8yYpbjtXvmnQThX/HyxT1hY+XGuwhYtk26yVlxxGGBtbS3ttNNOdN1111G7xxnZ0tJCjY2Ntp+w8MADRBdeaKYceaGutMirPEFNuGfP9imuknWuBKedZvrxHPjLxS0iikM9mbnv8PLl5raIQZoLKKhFLUTOlW7yUlVWog0LVMWV3blyPrbUQY3DHlis2XBppJuIuFIdkLCFBa5qWmU0UWb44qJ+j59vWhbYBCUsYYFacaXJVwqTuNL1uQoiLFAuKOPqXB17CNGQl+iBh7xnU+xu/umwPxn5QYJLXrjEmNTYnKsAwwL5PZ89/ll6ed7LxnurJFLQYvXm1drf+9kWrmoqwvdEhVM5j0sXjqmOT7rCFdv1il9cxRMWmMy517+/2UMrmbLsfsZK12M/EhbI+05UrVQbPAdT0MI5LDAjnSvNuSjGyHQ7V2ItXp1PrZA6PQwaFJC40oQvu7nt6r791a+Cz7mS+39x+XlZXMk+xaOPEg0ZwpFiqYm0yCSycnecc8459MADD9CLL75Ip59+Ov3iF7+gn/3sZ65/s2jRIqqpqbF+BvGZExImTuTtIxo92vu5qXCudtjBNJPUSWFMuemgxBXDZ64y+qsXgx13jJZV5UaEArnMK3dKFyFHukIRfap7xYQFDqgeYFVeNChosQY/r7BA3YAjJ98z5eV5gYYFhi0kUHWu5FBNnXP1v4aPrNvJOldhCgtUnSrdRFYWV+kOC1QfS0ZcyQU9RENfz5yrUU9T3on70Q+P9vflHDrqUPrugu9odK05UHJY22srXus250pUG+WwvJLCkrgbqQrkcYgXHoJoQCv3XBLtKCxxNeJpa7xU0YUAJutcyQTpXKUSP2GB7CC+ecqbdPW+V9NvDvgNzRozq/vCAiXnivefGPszSVzVl8e6pEKcprKghc65EgJC3p+LF0edK55nsPjwI654sdfoMReHuHLLE1W/Y57avvhi9P4tt0RFYKLOlYiUYr791qwnJn8eOT/6q6+ILr00/tfPdjJGXF144YUxRSrUn2XcNZWIzj//fNpnn31owoQJdMYZZ9D1119Pt9xyi+FOOXHRRRfRpk2brJ+Vsv+bQagFLbrDuWLmzvXhXJU2EOU5tDQPAPViIJeIlwXMysbod+lmtxu/q5BepKPIyEHgAdTRuYozLFBXZaxvT+cuhk4l1b2cq7AhO1fy98HVktTt/WTD+9btVIUFpsK5khu+ctluXRXEMDlX6rGbzHEln2vi3HFDLIBwCG08OXcs4q7cx+wPw/zri391m3PlhV/nSt4mOddMbRORsLhSnasj5tJFv/qSHn/cn7hKNufKqxR75osr07kSTvsle11CC3dfaOt5FnxBi9LAnSs+zdTxINExyM9kXndc+QkLFHMadkzciGfbVXElFmnl6weXOBfNisXaux9xxa/h5FzJ575f50rOoxcMUIJhRDuXZJwrIa4YUaQ5jJWIw0rIMjOcWbhwIc2bN8/1OcPlTmsSu+22mxEW+NVXX9FoB/unpKTE+Ml0eIAUK1lvvkm0enX3TNbUXcUnXIkqrvI7qai8mdqaE58kuKFeSGRxJScXf7nxS+u2W6IoU1IiTeQ6imlSv0nGTZsuL2yJ5lwl4FyN6jXKdr9/rx70hYOwcpr0Z7Jzxcnybs7V0rVvpzwsMBVC5pCRh9DO/XY2wlKfOvYp7XPkfZFu50olme2RnSs/4krkXOkcIS/2GxYtTf7qyldp7yF7d5tz5Ya6vxzFlcM2JVMl0xYWGHGuRIEKqlhHx85rpsH13hNezpUb3dtHyIRPgg4LDIW4ioQFdicxOVcd+oIWyY79/PdBRLv4WQ8Z1nNYQmGBYvv4+njuubyYrn99vw6X7vsV4srp+pGYuOqIda6KHMICI/OLgryCGAdb/lxiP6vikOd+yTpXXDBGB8SVPzJgWDOpq6szfhJh6dKllJ+fT/X1iffqyCTkwej3v++eCaQ6ePIJl1cUe4EprdaLq8mTu9e5kvMEPt/4ud5u10xqbK/ZWUQ/2eVE42ZMQQu5FHscOVfM9OHT6YqXr7Du7zBwOL1K3qtRugmJUcWwy/w+MtK50uRcfbjuPaKi7HOu2Il457R3XJ8j74t0O1dBEq+4kp2reOFcNl5c4Sa4//3+v7bmvkEWGYgXp/NS1wMtWSHo6lwpOXBuIYC7Ddwtoe8gFaXYU4nreVHQqm0SHiSGi5mvj7wJqlqgeC25IEKiY5CfcUTnXE3oM8F3QQseu9zGd5dApRhUcSLuO72+uDZbi4Ye4mqDU84VRcWVKOrBiPkFO1uqcy/P78T4rW6/IJk2PLJzJZPBteFSSsaEBfplyZIldOONN9L7779PX3zxBd133320YMECOv7446mnUwJLlsFVbASrVqVmAsmDKSeTiyaUgvLq6Ag3apRZWX3OHKKHHkr+PdWLcq9e+knCZxs+04YF6laG5UF91sjZdPiYw43bupwrrnJkiDWXsECduOIqYzJzdvqB9m/dDlf5uxQDaBBx96nOudKVYpf3ZzY5V35ItXmeLnHFOVWXvnApnfv0uTE9rRjxO7lARTzs2HdHq3jGv7/+t/X7mGIyKcRPWKCf3yfqXPkRVyxM9xka7Sc4Y/iMhN5/qJROKCqgZbJzJS/cxVC0pdudK0NoO0zgg8q5Eq8VxLjI3/8hh5jf7V//qn8Otw1QK1V6OVdy384gxZU67gk947Q/RbEHP84Vv4YuLJC3v7Sw1BLmcuEtMU/RLbDIzpXYT07jtggLTCbnSgXOVY6KKw7t42IWe++9N40fP56uvfZaQ1z96U+x1ZyyFTmWvinSQLy7J5A8GPEKi1rUorpHdNmJT3BuN/a3v3nHS/tBHTDkFRWemIieLTZx5dHzRh7Ujxw1x1o10pVi58R8o1dVnGGBRQVFdO1+1xpC9IKpF9CIPrG5N4zon6xDnpCIwTao0JDuQJ7MeYUFGj3SIqi9Y+LFz2SDv+KwVDqS90U8YS2ZJq7Ofvpsuubf19DNb91Mdy+9O1DnSm0q/GXDl1aIm5NLlAqczkueYPGP7bn5RbYFiWSKp6zbus63uGIePPpBOnjEwTR92HSjZ1Yi3H67uTjEoUVymfRMda5EmFgMhVuJ8ru6XVwZC4FKE1pdKfawiCvmySfNSnPHHefsxsnH/eQBk63rrZO4kvONeNu6y7kS8ObceWfs78Vcw29YoBGS6/Ac7uUoiytuHbOpZZPjAku6nSunPHBgJyRTiuCYNGkSvfHGG9TQ0EBbt26ljz/+2ChWkQ35VH6RJ+WbN3ffCuHuu5v/y6luarGG7XdbYw36F18c7Pt/GU2l0iLCgTgMTTQulcMCdc6V06BuE1ecc7VtY9TGjzMskLl4z4up+eJmum7GdY4rQY4XdA9xFfawwLbONtewQAGL49pyUyAnip+LSlhcqzCKqzMjc+v585N7H1nUfNP0Dd3+3u3W/be+fSvQnCtmYt+ouBJw1c9kGlIni9t5qYo+XThQPMjnjRG6HIe4YgfhqeOeon/N/VdsBVifcHNfrjL29ddEvXtnvnPlOBYXtBoLAIkep34xJtlKzo5celsUDQki5yqosZEPX7drGPO7g39nFf44YswR1u+dwgLVAl1ui0FqOKEbbq/z4x8nL650zpUancEuO/8IYRWPc6WbZ/AxIY6LIMUVtz8AOSiugH0VozudK245xWF+//qXs9V/9Mkr6J13zBKmukEqGeR+DjvvHPv4yNqR1u3/rf+fr2qBToO6PSzQdK6MSoHG/ficK9nBYpzEVbzOVZjDAp3KSmudqwhBJtK7EabJXbrFlXr/d78zJ8hc3jcZ5EnC6ytftz32VcNX3epchSEk0Gviq06iki28weJJCChVXPE+DbKSndt1yE+vtExwrhzDAiNl2LsbYyHQYXIuL6AmO/arf9/dC08nTzqZPjv7M3rtpNfoxB3N/Ga3RU612EZQzpX8OuoYyNui5hmJa7YfcTVsmLu4Es6VcK/k8EA5vNdNXOmQXb5EIjO4BZAOOcwXOANxleXiqjudKz7JOMyPBw+nJNU+lfWG8OmOWiILF5qDBn+uP/859vEd6newbnNie7xhgY7OVaSgRdS5Skxcye+pu4jttJN/ccUrVGLQDeNkxanymZtzNarWXlWxu4Bz5b76PHhw8k2peaIvJvRqE+FPN3xqu9/V1ZV0zhVXIqsqropxrkIrrpSFnmQqBQp6l/fWiis316q7ydRS7EbBJilcOdXiyrhWOYQFBimugnSu/DKkxxDafdDuNqdWXeR85hnTRV++PPr7IHOuvASLGgonxJZ1LinCiecm48YRcYHrvfaKnHsObWnkok0srFY3RUs86wo6XX559PY99/hbfE7ke3SaszlVEQR2IK5ySFylYqBUxRXHUXcXEyYQffop0eefmwOZ2+r1+9+/7yss0J9zZZZiF5MWN+fqRz/y/hx8TVFXxvi7OuUU/+JKFoJhdK6cJos8aXC6QI6pjfaFykXnSi1B3B2kKteMJ05O+U6cgyeHrHV0dVAXdSXlXOXn5VuVxwRyY/F04HZexjhXAfTjEuKKHXYWrGEQV7p9EKbzz7XFSXFH1jtX6RBXOuRjYssWooMPJvrDH8z/5eeo1w7ZYYwnDSFRcVWQX2CeT8p3w9WQP/qI6O67zWPHFFfk6VyxsJIbiet6IY4Zw4XbiJ57jmiGS72Z5kjnhWTG+Ztuiv1dDmXYJEUGDGsgXuSDvzudK6d4fZlkGmH6waG1mYE8udKJK90Exq9ztbV9a3QQVAbWXXc1B9eGBqJrr/W/MtrYGL3PHdfdVnRVcSVvXxhXgrkiEvfs4ImzgEMqODTSabA+etzR3bY9fLHk7ydszpU8OUqFc5VKWEBYCxIKXzd8TWPrxhq35eqByeSyTBs0jV5b+Zp1f8Z2iVW+Cwq38bc7nSs+5ziPQ4grLuyRLniSJ1e3C+t4paOzMy9t4spYmHDIuZIn0dkirkRDYx4DN0aDTeh7s/ClY1jgrFlEc+ea1fwcWppq0RWJcKvcKy+G8nVsizIHUAtMyItHKrI7tXrzatrWHrWcnFqRTJlCngSxsM65tv/9r76oB3AHzlUWwhcwMejIjksqBsp9h+5rxSHfdthtlE54RUhUzRJhgd82fWutiMdT0EJtIsx8vuFzbVggD6ycq8IlaEXJVi/UvCuvCYebuAqjc8XOhdMEUre9V+x9ha1XWTLw6ptcpp/DLeXvJZedq1QytIdUn1tB7n0l8q2Sca6YM3Y5w7rNlff2HLInpRORXK5DzVUVlU6DEFfM2ua11NjSmJIFLy/UCXGYzj83Oto106X8tpSIK3ZIyoq9FxrCVNAiWcR1Qc4bl9EVtOC/YTdnN3u3E0+8xL6Tc8UY8x1FXKnXc9HAu2THR43/L700+tigmkG2/FMv58ovQThX/P2fdFLCm5DTQFxlKbrSnKm4iHGVqv+e8V96fu7zdMokl7i2FE3oRdWwNc1rjHhmUQack9s5dMhvWKDqXDGfbfxMGxaYiG2uDsZeAkm+APC2hT0skFHFrLiv7q8vzvmCLt9HCixPknPOIVq3ztxPixcTvfCC/T3D6lzJlbGygZG9ogVm3MSVqBSYTM6VyLu6fsb1hqj7w6F/SEkRh0TF1bi6cY75ookiCzQe90SVzu5ueOuFer5ninPV1ZmfNueKqfBxYQnauUrntURsixzRIaNzrhLd3qDFlZNz1feE82nFCqKrroo+Jjc557Yx7F4F0edRFlfJXOMmTSKqi3R2kNsqAHcyZM0IxAuf3CLsKdWTSJ7U8E8YmFA/gf71hVnO8I1v3rDCAp2S2+XBWXar1Jwr5tP1n2qdqyDEldeEQy5xyxcfeTAP62TFSVypF8TuOHaMnIkiov33p5j3DJO4Ssd39+yzZmL0eed17/vI1TtT4Vwx50893/gJA27ianzdeNv9XfrvEqhz9fnGiMvO4irB8uq5Lq60sLgqTo24qi4rJ31QbfeJK6f+SalAfBankuq6nKtEjyWvnCs58iEZcVVRXE6DokaV5U7xIhIvKvF5Kl8nncICU+VcMewOvvmmGR4o57wBd+BcZSnpcq7Chtzv5qlPn3LsxyWQwwxkQaVzrkSIoTqwpsK5ksPa1q7NDOdKDXUSIZvpSJCVL6BhOi/SsS0cRnP//WaeYCqdqykDp+idq4ByrsKGm7hSK2OOr7eLraTFlQhhDqFzFabzL25S6FwN7qG/ZmWruPISSkE6VyefHL39059693aKR1x5FZPhCBpRCIzP00/WfmLc7lPRh8qKEv8CgixmxhWhOZ8trHOLMAJxlaXoBsUwrdCnikn9Jlm3H/74Yev2wKqBnvttq9l3WNtEWKayzB74nYhYUKsFel1YZHF1yCFEr77q/2/D4lyJC0o6xJV8kUhVxTw/hPW7C4Lt67e33Z83cZ63c5VfnBPiikXkYaMOM24fs/0xgYQwyuLKCmEOgbhS82Qy+phPobjarvcQz+ckuy/VyXOYxVWQztXMmWYvv1//mujUU5MXV/JiKY9nnV2drpU6RWggu1eixcsOfeILDX7ooe5xrkBiYJdnKXCuorkMosyxPIFzCguUL/xynwi1ibBMXZU9INupKXB3OVfMCSf4/9uwiKvtem6XtuNS3kduk95Uk83nKPezuWHGDXTk2CNp6elLbU2iHXOussi5kpt66nh49sNGM9W7Z90dyPs5OlchCwvM6GO+IDUFLZgRtcNyyrny+iy6ghaJiisOGz/rLNO10h2PAwYk7lyJYhbG3xUpq6gOC0+J5F3Onm0vP5/qNjzADsRVlgLnKmq57zF4j5jfi4l9Qs6VIq76VPdKWlypzlW84iqev00XauUjsVqXbIPaRJD3kdekN5Vk9ETTBwumLqC///DvRriu3Pequ3KuwoSXiC8tLDWaqQYlKB1zrkIWFpgpztUOE9rT6lyNqnPpO5KF4sorokEXFjgiWhsiUFTnSt4vlUXu4kouw+7kXO09ZG9fgssLOTcsqIIWIDEgrrIUdUUnFyZuTkwfNt12P4/yaNrgaUk4V/awwD5Vta5CyQ9qNSKvCUe1y/U8rJOVQ0cearsvSq2nI2QB4ir92MRVi0POVRLVAsNGqh1SWVyJMuxhdK7COl6p/PkvnUQ9vkybuCotLkq5uNLNI1KF13vzWKmOl0cemRpxJV+zjNYGSYorno+o1Yt5oSVebI4ZwgLTCnZ5lgLnKsqc7efY7vOquVOTTnm/CXHF9vp110lPUkqv96uJTmISda5UcZWNzhUn6Qv3ive/uJ1ucRWmkue5Kq42bduU9c5VqkW8U6+ssDlXmXLMT9yhiAoWjCKq+zAt4srP9TvoPldhd6446uHYY837XN5crqIbJG6vazZ4bnf8rvyIKw4t/NH4H1n3DxpxEI3pPSYpQYqwwPQCcZWlIOcqSp/KPjR34lzr/vE7HO9rQBdhgS+/bH9Or6oKV3GViHOldoCPp6CFSphXgp869ik6fsLx9Jcj/mKt1KUjLFDeR2FyrsL83QUNT0rZRc6VnKtUO1e876qKY2eFcK4S75tYVVJJ1OOr6C9rvk6ZuPJz/c6lsECxP/76V7OPodyYN2j4GnXggeZt8b+buJLxI66Y22feTifueKLhWN180M0JbSecq/CQo9Pt7AfOlZ07Zt5hVCfjCYdcAtop1ID7XgjnSu0Q369PAW2QIgNHK7HwiThXQYqrsDpXwjVkYSWTbnEF5yo9sLjmiemmlk20cdvGrHSufvQjogcfNG/vtlvq359DA5ta7QMYnKvEYYeh4ZCziP40xYxg2P9Cqi55LSXv7ef6nUviSjzO149avUkbKCziuAk9t66IR1w1tzb7ElcVxRV016y7ktpGJ3GVy3O/dJFBwxqIBzhXdooKimjfYfv63ncsqIS4kotZjB9PtO+EUfTR289Zv5s2bLfAc668BIdbzlWYxZWOdIgr+WID5yq9k1UWV5tbN2dlztXNN5sOeL9+RHPs0ckpE1dfNnwZKudKzaVRx74wY1SG67mM6PyBRPkdRAXtKduffibIcjPcTC/F7iWuUn2d692b6JhjYn8fj3PlVC2wO84tOFfpBbs8S4FzlfwAJcIC5WIWCxYQHTH2iMBLsavOlReZWNAiEReuu5AvNmESV7m2AMKrteoERA4LzHTnqr6e6PHHif74x/RMcNQKnaI5aTpRJ82JLEalC6tISFGLIazU3MFUjg077xz7nE3R1MWMd668ClqEZRExqLDAINClNTCY+6UeiKssBdUCE0dcUIRzJYsrHtD3GrIX7dR3J+P+jQfeGHNBCiLnygu3wTIsFx2/7Lsv0ZQp5oXhqady27nKtXNUrOTKoTNyWGA25Vylg6E9hsbsb6diPukSV+lwrhOlvqLedp/DWrmEfipQx/y//IXo8MPtv2uIpi5mvLgKm3PlhNFHM68j1OIKzlXqybFLee6gcy+wepG8c8WDV2F+Ib1xyhu0qmlVzOQlVc4Vww0Df/GL8F50/MID/+uvm9WNuqvak+49Bci5Sh9istHW2UZtHW1G+K4cFpjpzlW6UccnbuTMhRnSidekOczUl9vFVd/Kvil7b/X6PXo00aOPmmOmqAwnV4jL9FLsmSKuDOeqKz8U4kreJ3Cu0gv0bJaiG3hybeIWlHMl51yJAZ8nfTphFVTOlR+uvZbovvsyPyyQ4fleqoSVerGBuEp/WKA8CbE5Vxmec5VuhvUYZrs/uGYwpZtMXkVXnatUhliqY4PYj/I1YOHC5N4DzlX8GDl3nc4X3eY2fwUtggDOVXjIsUt57gBxlThitY7FFZdPVp0rLxJxrhK9iOnylRJ5/1wjrDlXvBotGDeOsh45wZsnITxRydZS7KFwrmqGULrZsiVzr0kx4qoydeLKyX2YOZPoySfNPNyxY5N7D4ir+OGw0GKqIGkNNhTO1XvvRW/DuUo9GTa0Ab/o3It0WvyZhLyfWFilQlyxc8OOF1f4iecCqXuvVJSlzXTCGha4ww5mM8zXXjMrzWU78mRD51whLDA5hvccHjrnSq5ilknFLJi6ijrb/b4VqQsLdBKifO049NBg3iNM4ipTClow1XWbaV3k9sCBLtUCJae+O3Can8C5Sj3Y5VmKbuBJ50CZScj7id0rtaCFF4lOGN5+m+jKK81VSL/o3qtXevPVM4KwOlcMN8N85hmiUaMot5yrSFGLbCrFnm6qSqro1/v/2tiPvK9njZ4VKucq08RVGJ2rIEEp9sToPXAT0X4XU8GIF+jZZ8PhXMnAuUo9cK6yFN1JBufKH/J+4rhlXc6VG4mG5bFjddll8f2N7r0grjK3WmCuIU82RG4CnKtg+em0n9KZu55pFA1JVdnwnBFXKcy5SsUEWQ5LZiCu/GGcV3stog5aRKPGtNmm1umqFigDcZV64FxlKQgLTBz5gsLhWfGGBaZyP0NcJQYuNuEtaIGcq+7Zz2EQVqL3lyDT3NlRtaNobG8zbpudwINGHJSy9062QbAfpk2z30e1wDjKsUdobGkMnXOFsMDUg12epcC5Shx5P82eHb+4SmWlY1VcsWhwazAMTHCxCW9YIJyr7Oaaa8zWEzxO3XorZRTchuM/p/+Hlpy8hL45/xsaVDMoZe8tX4e6c2GxsjK5Kra5KK7khYuGbQ2hqBYog8XE1IMpRpYCcZU4y5bZ7/vJuZozx/x/+nRKKaq44klLJjXlTBcQV+EtaGFzrpBzlXVwwv833xCtXk00JP3FC+OG3dQpA6ek3Alkl09EVZx1Vve9z5IlRIcdRvTHP6Y3bDOTClq4iStbQQtpMak7cGrDgutd6kHOVZaCsMDEUav9+HGu7ryT6LjjiPbck9IqrhAS6A9cbMIXFoicq9wB7SLih689775L9OabZkRFd7H99kRPPEFpJ2ucq4gjL8q2dydOC6twrlIPphhZCpyrxDn/fPv9Vau8B3yeLPBqn67vVHeX55WFAsSVP3CxCWFBC121QORcAWArejRvXuYVAsklcbVx60bbY2LRiF2rvDSFlWAxMfVgl+eQc4VS7P6YMoVor73sJdLjyblKNXK1O4grf+BiEw7kMBn0uQIAZKtzVVksJbOlGCwmph5MMbIUOFfJ4ZTIG6YBXUcm5jCkA1xswhsWiJwrAEC25Fxtbt2ckgbCbmAxMfVgl2cpuoEnjK5LJg3s7OhzGF6YOSh1lYEzGlxswhsWCOcKAJCppdidxBWcq9wCU4wcCQvkgQgTyuTEFQ/2YazEN3Ro9HaqqxVmKscfH719003p3JLcRhcWKOdcQVwBkJtkkrhycq7aO9stJz6d4gpzv9QT8nV4kCjqwIOQwGDEVRi57Taiq64iOuWU3Eh0DoJhw4j+/W+ir74i+uEP0701uYtbtcCi/KK0JYADANJLpoqrjds2aisFdncZdjfgXKUeiKssBeIqOXT7K0yDucyMGeYPiI899jB/QDj7XKFSIAC5i1cBrjBdj2vLa63b67as0zYQhnOVW2CX50hYIMRV9jpXAGQq8mqu6lwhJBCA3KXSQ4uE6XrMzlVhvulVrGleE5NvxSDnKreAuMpS4FwlB8QVAOntc4VKgQDkLtXVmeNc5eflU+/y3sbttVvWasUVwgJzC4irLEUdeNDjKj4grgDofooKiozcKl2fKzhXAOQuXnOWsAmG+op6y7nq6uqKyblCWGBugV2epSAsMHdyrgDIhqIWap8r5FwBkLtkWi2buvI6a3GoqbXJuI2wwNwF4ipLQVhgcsC5AiC1oYFqnys4VwCATKGuwhRXzNrmtTEFLdBEOLfIuF1+7bXX0u67707l5eXUo0e0/KXMihUr6NBDDzWeU19fTz/96U+pvb2dcgmIq+SAuAIgNYhcBLXPFXKuAACC4cMp1NSXm2GBclELOFe5S8aJq9bWVpo9ezb95Cc/0T7e0dFhCCt+3uuvv0733nsv3XPPPXTZZZdRLqGeTBBX8aHbX8hbA6B7wwK56WZHV4dxv7QQgxYAwGT8eMoc5ypS1CIs4grOVerJuF1+5ZVX0oIFC2iHHXbQPv7cc8/Rxx9/TH/9619pxx13pIMPPpiuvvpquvXWWw3BlauUR4tyAR9AXAGQ2rBADgeUJyNlRTjhAAAmQ4ZQqBEFLWTnKixNhDMtfy0byDhx5cWSJUsM4dWnTx/rdwceeCA1NjbSRx995Ph3LS0txnPkn2yif/90b0FmAXEFQGqQJx3rt6y3bsO5AgDI0Thjxpi3DzqIQlvQQs65amyJziOrSqpSsh3XXx/7u/XRYRWkiKwTV999951NWDHiPj/mxKJFi6impsb6GTRoEGUTAwakewsyC4grAFLf62r91ugsoKwQJxwAuYxc9bitjeiZZ4h+9zuiu+6iUIcFCueqYVuDrdFwKjjrLKL77rP/zmXqC7JZXF144YWUl5fn+rNs2bJu3YaLLrqINm3aZP2sXLmSsomBA9O9BZmFLgEUeWsABI9cRQvOFQBAV5irpcUMDZw/n6hfPwp1WKDIuWpoSb244n127LFEI0Y4t+YB3U8hhYCFCxfSvHnzXJ8z3GepmL59+9Jbb71l+933339vPeZESUmJ8ZOtQFzFR2dn7O/gXAHQzWGBknMFcQVAbsNCoTmSthT2lHlbWGBEXG3atinl4krwj38Q7bILUWUl0TnnpPStQVjEVV1dnfETBFOnTjXKta9Zs8Yow84sXryYqqurady4cZSrQFzFB8QVAGkIC5ScK4QFApDbyM4VhwWGGRZPhfmFRsVTXVhgTUlNSreHa76tWUNUWEhUkb5aGjlLKMIC44F7WC1dutT4n8uu823+2bzZrDI1Y8YMQ0T9+Mc/pvfff5+effZZuuSSS2j+/PlZ7Ux54WLaAQ2DB8f+DuIKgOCBcwUA0HHvvdHbYe+mw+krwr0SBS2EuOIFpKKC1Mfm1dRAWKWLjBNX3K9qp512ossvv9wQVHybf9555x3j8YKCAnryySeN/9nFOv7442nu3Ll01VVXUa5x5pnm/wceiJjbeOF45UWL7L9DzhUAqcu5Qil2AHKbGTM48oirQBONHUuhRxS1YOeqq6vLElepDgkE6ScUYYHxwA2B+ceNIUOG0FNPPUW5zi23EJ1wAtHEienekszkwgu50En0PpwrAFJXLRDOFQC5Dfdn2n9/yhhEUYu2zjba1LLJ+GEgrnKPjBNXIL6u3JMnp3srsgeIKwBSFxaInCsAQCbRvyraUPTrhq+tpuipzrcC6SfjwgIBSBcQVwCkrqAFnCsAQCYxsCpaOezjtR9bt+Fc5R4QVwD4BDlXAHRzzhXCAgEAGcrA6qi4+nDNh9ZtiKvcA+IKAJ/AuQKgm8MCUdACAJChDKgeYN3+YM0H1m2Iq9wD4goAn0BcAdC9YYHNbZGOoXCuAAAZ7Fy99e1b1u1+lf3StEUgXUBcAeATiCsAujcsUAYFLQAAmSquvm/+XutogdwA4goAn0BcAdC9YYEycK4AAJlE7/Le2kWhAVUQV7kGxBUAPikuTvcWAJDdYYEyyLkCAGQS+Xn5NLJ2pGuJdpAbQFwB4JNCdIUDIGVhgXCuAACZxuja0TG/Q1hg7gFxBYBPEBYIQOqcK4grAECmMab3mJhxrGdpz7RtD0gPEFcAuPC3v5n9rY45hqhv33RvDQDZR3FBMRXmx9rCEFcAgEx3rob1GEZ5eXlp2x6QHiCuAHBhzhyihgai++9P95YAkL3oilpUFVelZVsAACBR9hu2n+3+ISMPSdu2gPQBcQWAByUl6d4CAHIrNDCP8qiqBOIKAJBZ9KvqR0ePO9q6P2f7OWndHpAekKIPAAAgVEUtWFhx5S0AAMg0fn/I740Fo1367UK79N8l3ZsD0gDEFQAAgFA5VzUlNWnbFgAASIa6ijq69/B7070ZII1gaRAAAEBa6VXWy3a/phTiCgAAQGYCcQUAACCt9C7vbbsP5woAAECmAnEFAAAgrfQus4urHqU90rYtAAAAQDJAXAEAAAiXc4WwQAAAABkKxBUAAIC0grBAAAAA2QLEFQAAgLQCcQUAACBbgLgCAACQVhAWCAAAIFuAuAIAAJBW4FwBAADIFiCuAAAApL3ppsw+Q/dJ27YAAAAAyQBxBQAAIK0Mqh5E+w7dlwryCui3B/6WxtaNTfcmAQAAAAlRmNifAQAAAMGQl5dHz899njZu20i9ynqle3MAAACAhIFzBQAAIBQCC8IKAABApgNxBQAAAAAAAAABAHEFAAAAAAAAAAEAcQUAAAAAAAAAAQBxBQAAAAAAAAABAHEFAAAAAAAAAAEAcQUAAAAAAAAAAQBxBQAAAAAAAAABAHEFAAAAAAAAAAEAcQUAAAAAAAAAAQBxBQAAAAAAAAABAHEFAAAAAAAAAAEAcQUAAAAAAAAAAQBxBQAAAAAAAAABAHEFAAAAAAAAALkorq699lrafffdqby8nHr06KF9Tl5eXszPAw88kPJtBQAAAAAAAOQOhZRhtLa20uzZs2nq1Kl05513Oj7v7rvvpoMOOsi67yTEAAAAAAAAACAnxdWVV15p/H/PPfe4Po/FVN++fVO0VQAAAAAAAIBcJ+PElV/mz59Pp5xyCg0fPpzOOOMMOvHEE43wQCdaWlqMH8GmTZuM/xsbG1OyvQAAAAAAAIBwIjRBV1dX7omrq666ivbbbz8jL+u5556jM888kzZv3kznnHOO498sWrTIcsVkBg0a1M1bCwAAAAAAAMgEmpqaqKamxvHxvC4v+ZUCLrzwQvrVr37l+pxPPvmExowZY93nsMDzzjuPGhoaPF//sssuM3KwVq5c6du56uzspA0bNlBtba2r45Uqpcwij7e/uro6rdsCMgMcMyBecMyAeMDxAuIFxwzI9GOGJRMLq/79+1N+fn64nauFCxfSvHnzXJ/D4X2Jsttuu9HVV19tiKeSkhLtc/j36mNhK4LBB1YYDi6QOeCYAfGCYwbEA44XEC84ZkAmHzNujlWoxFVdXZ3x010sXbqUevbs6SisAAAAAAAAACBZQiGu4mHFihVGuB7/39HRYQgnZsSIEVRZWUlPPPEEff/99zRlyhQqLS2lxYsX0y9+8Qu64IIL0r3pAAAAAAAAgCwm48QV50/de++91v2ddtrJ+P/FF1+kffbZh4qKiujWW2+lBQsWGLGRLLpuuOEGOvXUUylTYcft8ssvh/MGfINjBsQLjhkQDzheQLzgmAG5csyEoqAFAAAAAAAAAGQ6zqUuAAAAAAAAAAD4BuIKAAAAAAAAAAIA4goAAAAAAAAAAgDiCgAAAAAAAAACAOIq5HDlw6FDhxpl5bkZ8ltvvZXuTQIh4ZVXXqGZM2cancLz8vLosccesz3OtWq4uma/fv2orKyM9t9/f/r000/Ttr0g/SxatIh23XVXqqqqovr6ejr88MNp+fLltuds27aN5s+fT7W1tUZ7i6OOOspobwFykz/84Q80YcIEq4nn1KlT6emnn7Yex/EC3PjlL39pXJ/OO+8863c4ZoDKFVdcYRwn8s+YMWMy9piBuAoxDz74IJ1//vlGGcr33nuPJk6cSAceeCCtWbMm3ZsGQkBzc7NxTLAA1/HrX/+abr75ZvrjH/9Ib775JlVUVBjHDw9SIDd5+eWXjQvUG2+8YfQAbGtroxkzZhjHkoDbWHC/wIcffth4/qpVq+jII49M63aD9DFw4EBjgvzuu+/SO++8Q/vttx/NmjWLPvroI+NxHC/Aibfffptuu+02Q5zL4JgBOsaPH0+rV6+2fl599dXMPWa4FDsIJ5MnT+6aP3++db+jo6Orf//+XYsWLUrrdoHwwafyo48+at3v7Ozs6tu3b9d1111n/a6hoaGrpKSk629/+1uathKEjTVr1hjHzssvv2wdI0VFRV0PP/yw9ZxPPvnEeM6SJUvSuKUgTPTs2bPrjjvuwPECHGlqauoaOXJk1+LFi7v23nvvrnPPPdf4PY4ZoOPyyy/vmjhxovaxTDxm4FyFlNbWVmOlkEO5BPn5+cb9JUuWpHXbQPj58ssv6bvvvrMdPzU1NUZoKY4fINi0aZPxf69evYz/ecxhN0s+bjg0Y/DgwThuAHV0dNADDzxgOJ0cHojjBTjBDvmhhx5qOzYYHDPACU5b4DSH4cOH03HHHUcrVqzI2GOmMN0bAPSsW7fOuJD16dPH9nu+v2zZsrRtF8gMWFgxuuNHPAZym87OTiMPYtq0abT99tsbv+Njo7i4mHr06GF7Lo6b3OaDDz4wxBSHFHO+w6OPPkrjxo2jpUuX4ngBMbAA51QGDgtUwRgDdPDC7z333EOjR482QgKvvPJK2nPPPenDDz/MyGMG4goAAHJ0ZZkvXHJcOwA6eMLDQoqdzkceeYROOOEEI+8BAJWVK1fSueeea+R0ciEuAPxw8MEHW7c5R4/F1pAhQ+ihhx4yCnJlGggLDCm9e/emgoKCmGoofL9v375p2y6QGYhjBMcP0HHWWWfRk08+SS+++KJRsEDAxwaHJDc0NNiej+Mmt+FV4xEjRtDOO+9sVJzkQjo33XQTjhcQA4dwcdGtSZMmUWFhofHDQpyLK/FtdhtwzAAv2KUaNWoUffbZZxk5zkBchfhixhey559/3hbGw/c5PAMAN4YNG2YMOvLx09jYaFQNxPGTu3DtExZWHNb1wgsvGMeJDI85RUVFtuOGS7Vz7DuOGyBfi1paWnC8gBimT59uhJGy0yl+dtllFyOHRtzGMQO82Lx5M33++edGK5lMHGcQFhhiuAw7h1/wYDR58mS68cYbjUTiE088Md2bBkIy+PCqjlzEgi9eXJyAEz05n+aaa66hkSNHGpPoSy+91EgW5d5GIHdDAe+//356/PHHjV5XIl6di51w6AX/f/LJJxtjDx9H3Nfo7LPPNi5gU6ZMSffmgzRw0UUXGSE7PKY0NTUZx89LL71Ezz77LI4XEAOPKyKHU8BtQLg/kfg9jhmgcsEFFxh9OzkUkMuscwsijt465phjMnOcSXe5QuDOLbfc0jV48OCu4uJiozT7G2+8ke5NAiHhxRdfNEqRqj8nnHCCVY790ksv7erTp49Rgn369Oldy5cvT/dmgzSiO1745+6777aes3Xr1q4zzzzTKLddXl7edcQRR3StXr06rdsN0sdJJ53UNWTIEOMaVFdXZ4wjzz33nPU4jhfghVyKncExA1R+9KMfdfXr188YZwYMGGDc/+yzzzL2mMnjf9It8AAAAAAAAAAg00HOFQAAAAAAAAAEAMQVAAAAAAAAAAQAxBUAAAAAAAAABADEFQAAAAAAAAAEAMQVAAAAAAAAAAQAxBUAAAAAAAAABADEFQAAAAAAAAAEAMQVAAAAAAAAAAQAxBUAAAAAAAAABADEFQAAAAAAAAAEAMQVAACArGfBggV05JFHej7vxBNPpEsuuSQl2wQAACD7gLgCAACQ9bz11lu0yy67uD6no6ODnnzySfrBD36Qsu0CAACQXUBcAQAAyFpaW1upqKiIXn/9dfr5z39OeXl5NGXKFO1z+Tn83F133VX7+D777ENnnXWW8VNTU0O9e/emSy+9lLq6uqznPPLII7TDDjtQWVkZ1dbW0v7770/Nzc3d9vkAAACEC4grAAAAWUthYSG99tprxu2lS5fS6tWr6ZlnntE+95///CfNnDnTEGBO3HvvvcZrshN200030Q033EB33HGH8Ri/9jHHHEMnnXQSffLJJ/TSSy8ZoYiy+AIAAJDdFKZ7AwAAAIDuIj8/n1atWmW4SBMnTnR97uOPP06//e1vXZ8zaNAg4zkswEaPHk0ffPCBcf/UU081xFV7e7shqIYMGWI8n10sAAAAuQOcKwAAAFnNf/7zH09hxU4Ti7Dp06e7Po9DCmVna+rUqfTpp58a+Vr8Hvz3LKhmz55Nt99+O23cuDGwzwEAACD8QFwBAADIajgc0EtccUjgAQccQKWlpQm/T0FBAS1evJiefvppGjduHN1yyy2Gu/Xll18m/JoAAAAyC4grAAAAWQ2H7u24446eIYGzZs3yfK0333zTdv+NN96gkSNHGsKKYVdr2rRpdOWVVxqOWXFxMT366KNJfgIAAACZAnKuAAAAZDWdnZ20fPlyI+yvoqLCqPQns2bNGnrnnXcM98qLFStW0Pnnn0+nn346vffee4Y7df3111vC6/nnn6cZM2ZQfX29cX/t2rU0duzYbvtsAAAAwgWcKwAAAFnNNddcQ/fccw8NGDDAuK3yxBNP0OTJk43S6l7MnTuXtm7dajx//vz5dO6559Jpp51mPFZdXU2vvPIKHXLIITRq1CijGTELr4MPPrhbPhcAAIDwkdeFGrEAAAByGG4avMcee9DPfvYz1+dxnysOL7zxxhtTtm0AAAAyCzhXAAAAchoWVtyfCgAAAEgW5FwBAADIabwcKwAAAMAvCAsEAAAAAAAAgABAWCAAAAAAAAAABADEFQAAAAAAAAAEAMQVAAAAAAAAAAQAxBUAAAAAAAAABADEFQAAAAAAAAAEAMQVAAAAAAAAAAQAxBUAAAAAAAAABADEFQAAAAAAAAAEAMQVAAAAAAAAAAQAxBUAAAAAAAAAUPL8Py5yNnApuW0/AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Timeline\n", "tmin    = 0\n", "tmax    = 50\n", "Nsteps  = 1000\n", "tvalues = np.linspace( 0, tmax, Nsteps )\n", "dt      = tvalues[1] - tvalues[0]\n", "print(dt)\n", "\n", "# Number of oscillators\n", "Noscillators = np.array([20, 2000, 200000])        \n", "\n", "R = np.zeros((<PERSON><PERSON><PERSON>,3))\n", "\n", "for i,N in enumerate(Noscillators):\n", "  omega    = np.random.uniform( 0, 1, N ) * N ** a\n", "  k_spring = ang_freqs(omega, N)\n", "  m        = k_spring / omega ** 2\n", "  \n", "  # noise term\n", "  xi     = np.random.normal(0, 1, N)\n", "  # Q0     = 0\n", "  # q0     = Q0 + xi * np.sqrt( 1 / beta / k_spring )\n", "\n", "  eta    = np.random.normal(0, 1, N)\n", "  # p0     = eta * np.sqrt(m / beta )\n", "\n", "  for k,t in tqdm(enumerate(tvalues)):\n", "      R[k,i] = noise_term( t, omega, k_spring, xi, eta )\n", "\n", "W = np.sqrt( 2 * M * gamma / beta ) * np.random.normal(0,1,Nsteps)\n", "\n", "fig, (ax1) = plt.subplots(1, 1,figsize=(10, 4))  \n", "ax1.plot(tvalues, R[:,0], 'r-',    linewidth = 2,   label =r'$R(t),\\, N=20$')\n", "ax1.plot(tvalues, R[:,1], 'g-',    linewidth = 2,   label =r'$R(t),\\, N=2000$')\n", "ax1.plot(tvalues, R[:,2], 'b-',    linewidth = 2,   label =r'$R(t),\\, N=200000$')\n", "ax1.set_xlabel(r'$t$ / ps')\n", "ax1.set_ylabel(r'$R(t)$')\n", "ax1.set_title('Noise term')\n", "ax1.legend()\n", "ax1.set_ylim((-15, 15));\n", "\n", "#ax2.plot(tvalues, W, 'k-',    linewidth = 2,   label ='White noise')\n", "\n", "#ax2.set_xlabel(r'$t$ / ps')\n", "#ax2.set_ylabel(r'$R(t)$')\n", "\n", "#ax2.set_title('White noise')\n", "#ax2.legend()\n", "#ax2.set_ylim((-15, 15))\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3)\n", "\n", "#fig.savefig('figures/noise_term1.png', format='png', dpi=900, bbox_inches='tight')\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "67271a88", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10/10 [00:00<00:00, 180.42it/s]\n", "100%|██████████| 10/10 [00:00<00:00, 30.35it/s]\n", "100%|██████████| 10/10 [00:31<00:00,  3.18s/it]\n"]}], "source": ["# Generate and ensemble of random processes R(t)\n", "Noscillators     = np.array([20, 2000, 200000])\n", "Nreps = 10 # Number of replicas\n", "\n", "R     = np.zeros((<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 3))\n", "K     = np.zeros((<PERSON><PERSON><PERSON>, 3))\n", "\n", "for j in range(3):\n", "  N        = Noscillators[j]\n", "  omega    = np.random.uniform( 0, 1, N ) * N ** a\n", "  k_spring = ang_freqs(omega, N)\n", "  m        = k_spring / omega ** 2\n", "\n", "  for k,t in enumerate(tvalues):\n", "    K[k,j] = memory_kernel1( t, omega, k_spring )\n", "\n", "  for r in tqdm(range(Nreps)):\n", "    # noise term\n", "    xi     = np.random.normal(0, 1, N)\n", "    eta    = np.random.normal(0, 1, N)\n", "\n", "    for k, t in enumerate(tvalues):\n", "      R[k,r,j] = noise_term( t, omega, k_spring, xi, eta )"]}, {"cell_type": "code", "execution_count": 7, "id": "0fda2779", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 3/3 [00:05<00:00,  1.73s/it]\n"]}], "source": ["#R = sqrt( 2 * M * gamma / beta ) * np.random.normal(0,1,(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>))\n", "\n", "# Autocorrelation\n", "Atot  = np.zeros((<PERSON><PERSON><PERSON>, 3))\n", "Ntau  = np.arange(Nsteps)\n", "\n", "for j in tqdm(range(3)):\n", "  for r in range(Nreps):\n", "    A = np.zeros(Nsteps)\n", "    for t,tau in enumerate(Ntau):\n", "      for k in range(Nsteps - tau):\n", "        A[t] = A[t] + R[k,r,j] * R[k+tau,r,j]\n", "      A[t] = A[t] / (Nsteps - tau)\n", "    Atot[:,j] = Atot[:,j] + A\n", "  Atot[:,j] = Atot[:,j] / Nreps "]}, {"cell_type": "code", "execution_count": 8, "id": "3a5954dd", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************************************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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 1,figsize=(10, 4))  \n", "\n", "ax.plot(tvalues, f(tvalues) / beta, 'k',   label=r'$\\frac{1}{\\beta}f(t)$')\n", "ax.plot(tvalues, Atot[:,0],  'r--' , label=r\"$\\langle R(0) R(t) \\rangle, \\, N=20$\")\n", "ax.plot(tvalues, Atot[:,1],  'g--' , label=r\"$\\langle R(0) R(t) \\rangle, \\, N=2000$\")\n", "ax.plot(tvalues, Atot[:,2],  'b--' , label=r\"$\\langle R(0) R(t) \\rangle, \\, N=200000$\")\n", "\n", "#ax.plot(tvalues, K[:,0],  'r-.' , label=r\"$K(t), \\, N=200$\")\n", "#ax.plot(tvalues, K[:,1],  'g-.' , label=r\"$K(t), \\, N=2000$\")\n", "#ax.plot(tvalues, K[:,2],  'b-.' , label=r\"$K(t), \\, N=20000$\")\n", "\n", "ax.set_xlabel(r'$t$ / ps')\n", "ax.set_ylabel(r'$K(t)$')\n", "ax.set_title(r'Autocorrelation $\\langle R(0) R(t) \\rangle$')\n", "ax.legend();\n", "# plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3);\n", "#fig.savefig('figures/noise_term2.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "nonlinear", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}