{"cells": [{"cell_type": "code", "execution_count": 1, "id": "98db341d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import sympy as sp\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "from sympy import *\n", "from tqdm import tqdm\n", "\n", "from integrator_functions import euler_integrator_step, vel_verlet_integrator_step, leap_frog_integrator_step, acf\n", "\n", "\n", "np.random.seed(0)\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "b82a2782", "metadata": {}, "outputs": [], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "beta  = 1 / kB / T  # kJ-1 mol\n", "M     = 1           # amu"]}, {"cell_type": "markdown", "id": "a048b617", "metadata": {}, "source": ["# Initial positions and momenta of the oscillators\n", "\n", "We draw the initial positions and the initial momenta from the <PERSON><PERSON>mann distributions:\n", "\n", "$$\n", "\\pi_q (q_i) = \\frac{1}{Z_q} \\exp\\left( - \\beta \\frac{k_i}{2}(q_i - Q(0))^2 \\right) \\,; \\quad\\quad \\pi_p (p_i) = \\frac{1}{Z_p} \\exp\\left( - \\beta \\frac{p_i^2}{2m_i}\\right) \\, .\n", "$$\n", "\n", "Comparing with the Gaussian function\n", "\n", "$$\n", "f(x) = \\frac{1}{\\sqrt{2\\pi \\sigma^2}} \\exp\\left( -  \\frac{1}{2}\\frac{(x - x(0))^2}{\\sigma^2} \\right) \\, ,\n", "$$\n", "\n", "we obtain that the initial positions and momenta can be estimated as\n", "\n", "$$\n", "q_i(0) = Q(0) + \\xi_i \\sqrt{\\frac{1}{\\beta k_i}} \\\\\n", "p_i(0) = \\eta_i \\sqrt{\\frac{m_i}{\\beta}} \\, ,\n", "$$\n", "\n", "with $\\xi, \\eta_i \\in \\mathcal{N}(0,1)$.\n", "\n", "The noise term is \n", "\n", "\\begin{eqnarray}\n", "R(t) = \n", "\\sqrt{\\frac{1}{\\beta}}\n", "\\sum_{i=1}^N \n", "\\sqrt{k_i}\n", "\\left[\n", "\\sqrt{k_i \\beta}\n", "\\left(\n", "q_i(0) - Q(0)\n", "\\right) \\cos(\\omega_i t)\n", "+ \n", "p_i(0)\n", "\\sqrt{\\frac{\\beta}{m_i}}\n", "\\sin(\\omega_i t)\n", "\\right]\\, ,\n", "\\label{eq:noise2}\n", "\\end{eqnarray}\n", "\n", "or\n", "\n", "\\begin{eqnarray}\n", "R(t) = \n", "\\sqrt{\\frac{1}{\\beta}}\n", "\\sum_{i=1}^N \n", "\\sqrt{k_i}\n", "\\left[\n", "\\xi_i \\cos(\\omega_i t)\n", "+ \n", "\\eta_i\n", "\\sin(\\omega_i t)\n", "\\right]\\, ,\n", "\\label{eq:noise3}\n", "\\end{eqnarray}"]}, {"cell_type": "code", "execution_count": 3, "id": "2660db31", "metadata": {}, "outputs": [], "source": ["a       = 0.3333\n", "\n", "gamma   = 1.5\n", "alpha   = 5\n", "\n", "# Angular frequencies\n", "def ang_freqs(omega, N):\n", "    \n", "    domega = N**a / N\n", "    return ( 2 / np.pi ) * ( alpha ** 2 * M * gamma ) / ( alpha ** 2  + omega ** 2) * domega\n", "\n", "# Memory kernel\n", "def memory_kernel1( t, omega, k_spring ):\n", "\n", "    return np.sum(  k_spring * np.cos( omega * t ) )\n", "\n", "def f(t):\n", "    return alpha * gamma * np.exp( - alpha * t )\n", "\n", "# noise term\n", "def noise_term( t, omega, k_spring, xi, eta ):\n", "    return np.sqrt( 1 / beta ) * np.sum( np.sqrt( k_spring ) * ( xi * np.cos( omega * t ) + eta * np.sin( omega * t ) ) )      "]}, {"cell_type": "code", "execution_count": 4, "id": "7676f421", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.05005005005005005\n"]}, {"name": "stderr", "output_type": "stream", "text": ["1000it [00:00, 47753.71it/s]\n", "1000it [00:00, 9763.96it/s]\n", "1000it [00:11, 86.62it/s]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAY8AAAEkCAYAAADXQb3KAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAAsTAAALEwEAmpwYAACQuUlEQVR4nO19d3wcxfn+M7fX1GzJcu82LriBAdOr6aG3ECCkQBIggXTCNyS/BAKBQAIJJKSR0GsgJKFD6KFjg427MbjKTZJlySrXd35/zJbZ2dm9PelOJ0vzfD627vb2dmdvZ+eZ533feV9CKYWCgoKCgkIhCJW7AQoKCgoKux8UeSgoKCgoFAxFHgoKCgoKBUORh4KCgoJCwVDkoaCgoKBQMBR5KCgoKCgUDEUeCgrdBCHkeULIV8rdDgWFckCRh8KABSFkPSFkOyGkitv2dULI60G+Tyn9HKX0viK36XVCyNeLeUwFhVJAkYfCQEcYwHfL3YhigRCilbsNCgMDijwUBjp+A+BKQkit7ENCyCGEkAWEkDbj7yHcZ5ZKIIRMIYS8YezXTAj5B7ffnoSQlwghLYSQ1YSQcz3OdQOAwwHcQQjpIITcke/7hJB7CSF/JoQ8RwjpBDDfUFQ/IoQsIYR0EkLuIoSMMMxs7YSQlwkhdUX47RQGMBR5KAx0LATwOoArxQ8IIUMAPAvg9wDqAfwWwLOEkHrJca4H8F8AdQDGAviDcYwqAC8BeBjAcADnA/gTIWSWeABK6U8BvAngCkppNaX0ioDfvwDADQBqALxlbDsbwHEApgE4FcDzAH4CYCjYc/+d/D+NgoI3FHkoKAA/B/BtQsgwYfvJANZQSh+glGYppY8AWAU2GIvIAJgAYDSlNEkpNQfxUwCsp5TeYxzjIwBPADgnYNuCfP9JSunblFKdUpo0tv2BUrqdUroZjJDep5QuopSmAPwbwD4Bz6+gIIUiD4UBD0rpMgDPAPix8NFoABuEbRsAjJEc5ioABMAHhJDlhJCLje0TABxICGk1/wH4IoCRAZsX5PubJN/bzr1OSN5XBzy/goIU4XI3QEGhj+AaAB8BuJXbtgVs8OYxHsAL4pcppdsAfAMACCGHAXiZEPI/sIH9DUrpcQHbIaa5DvJ9lRpbodehlIeCAgBK6acA/gGnL+A5ANMIIRcQQsKEkC8AmAmmUhwghHyeEDLWeLsTbEDPGftOI4R8iRASMf7tTwiZ4dGU7QAmc+8L/b6CQq9AkYeCgo3rAFhrPiilO8B8Dj8EsAPMNHUKpbRZ8t39AbxPCOkA8BSA71JK11FK2wEcD+A8MCWzDcDNAGIebbgdwDmEkJ2EkN934/sKCr0CoopBKSgoKCgUCqU8FBQUFBQKRp8hD0LI3YSQRkLIMm7btYSQzYSQxca/k8rZRgUFBQUFhj5DHgDuBXCiZPvvKKVzjX/P9XKbFBQUFBQk6DPkQSn9H4CWcrdDQUFBQSE/+gx5+OAKI0fP3Sofj4KCgkLfQJ+KtiKETATwDKV0tvF+BIBmsJj56wGMopReLPneJQAuAYCqqqr99txzz15rs4KCgkJ/xYcffthMKRXT9gDo4+QR9DMe8+bNowsXLixNAxUUFBQGEAghH1JK58k+69NmK0LIKO7tmQCWee2roKCgoNB76DO5rQghjwA4CsBQQkgDWK6howghc8HMVusBXFqu9ikoKCgo2Ogz5EEpPV+y+a5eb4iCgoKCQl70GfIoJTKZDBoaGpBMJvPvrLDbIR6PY+zYsYhEIuVuioLCgMGAII+GhgbU1NRg4sSJIISUuzkKRQSlFDt27EBDQwMmTZpU7uYoKAwY9GmHebGQTCZRX1+viKMfghCC+vp6pSoVFHoZA4I8ACji6MdQ91ZBofcxYMhDQUFBQaF4UOShoKCgoFAwFHn0Iv76179i5MiR2HvvvbHHHnvg/vvvBwAkEgkceeSRyOVyAJiD/x//+AcAIJ1O44gjjkA2mw18DkIIVq5caW2bMWMG1q9fX3B7N23ahPnz52PGjBmYNWsWbr/9duuzF154AdOnT8eUKVNw0003FXxsBQWF3RuKPHoRS5YswbXXXouPP/4YjzzyCH7wgx8AAO6++26cddZZ0DQNAPDKK6/go48+AgBEo1Ecc8wxFpkEOcfcuXPx7LPPAgBSqRS2b9+OCRMmFNzecDiMW2+9FStXrsR7772HP/7xj1ixYgVyuRwuv/xyPP/881ixYgUeeeQRrFixouDjKygo7L5Q5NGLWLp0KaZPnw4AmDRpEqLRKADgoYcewumnnw4AeOutt/CDH/wA//znPzF37lysW7cOZ5xxBh566KHA5/jxj39skcfy5csxY8aMbjmVR40ahX333RcAUFNTgxkzZmDz5s344IMPMGXKFEyePBnRaBTnnXcennzyyYKPr6CgsPti4JEHIaX5FwAmeVBKcccdd+CGG25AOp3G2rVrMXHiRADAYYcdhv333x9PPvkkFi9ejEmTJmH27NlYsGBBoHOsWLECp512GhobG9HW1oalS5dizpw5jn0OP/xwzJ071/Xv5Zdf9jzu+vXrsWjRIhx44IHYvHkzxo0bZ302duxYbN68OVD7FBQU+gcGxCLBvoBNmzahvb0dJ510EjZv3oy99toL1157LbZu3Yra2lrHvqtXr7YUCgBomoZoNIr29nbU1NT4nqO+vh4VFRU47rjj8OKLL2LJkiXYa6+9HPu9+eabBbW9o6MDZ599Nm677TYMGjQIskzMKlxWQWFgYeCRR5lS0C9ZsgRHHHEEXn31VezcuROzZ8/Gu+++ixkzZjgWuO3YsQODBw92pdpIpVKIx+N5z2GqjJNOOgkPPfQQtm7dijPOOMOx3+GHH4729nbX92+55RYce+yxjm2ZTAZnn302vvjFL+Kss84CwJTGpk2brH0aGhowevTo/D+CgoJCv8HAI48yYenSpdhnn30AAHV1dbjgggvw7LPP4pBDDkEul0MymUQ8Hse6detcA/GOHTswbNgwi1COOeYY3H///RgzZozrHCZ5HHnkkbjsssvQ1dXlMlsFVR6UUnzta1/DjBkzLOc+AOy///5Ys2YN1q1bhzFjxuDRRx/Fww8/XNgPoqCgsFtj4Pk8ygSePADg1FNPxXPPPQcAOP744/HWW28BAPbcc080Nzdj9uzZeOeddwAAr732Gk466SQAgK7r+PTTTzFkyBDpOUyiiMVimDNnDqLRqMssFhRvv/02HnjgAbz66quWX+S5555DOBzGHXfcgRNOOAEzZszAueeei1mzZnXrHAoKCrsn+lQlwWJAVklw5cqVmDFjRplalB+LFi3Cb3/7WzzwwAPSz8866yz86le/wvTp07Fs2TLcfffd+O1vf9vLrezb6Ov3WEFhd8RuW0lwoGCfffbB/PnzrUWCPNLpNM444wzLgT579mxFHAoKCmWH8nn0EVx88cXS7dFoFF/+8pd7uTUKCgoK/lDKQ0FBQUGhYCjyUFBQUFAoGIo8FBQUFBQKhiIPBQUFBYWCochDQUFBQaFgKPJQUFBQUCgYijwUFMqEJ54AJk0CPv643C1RUCgcijx6EQOhkqBfhUFVfdCJc84B1q8HvvvdcrdEQaFwKPLoRfT3SoJ+FQZV9UFvVFaWuwUKCoVDkUcvor9XEvSrMKiqD3pj6NByt0BBoXAMuPQk5BelKVpEr8mfYLKQSoK33HILZs+eDYDN2gutJHjdddf5VhIMWs/DBF9J8L///a+rkuD7778vrTD4/vvvA4DvZwMd9fXlboGCiHvuASZOBObPL3dL+i4GHHmUCwOhkqBfhUFVfdAbQt0vhTJj6VLATDXXz5KOFxV9hjwIIXcDOAVAI6V0trFtCIB/AJgIYD2AcymlO3tyniAKoRQYCJUE/SoM9lb1QUoDl5TvM9jd2tvfsW6d/bqlBZCUzlFA3/J53AvgRGHbjwG8QimdCuAV4/1uCa9KgnV1dVYlQQCBKwlu3rxZeg6+kuCbb77pIBQTb775JhYvXuz6JxJHkEqC6XQajz76KE477TTP7X7fKSZWrwamTAGuu66ohy05FHn0LWzcaL8+9dTytaOvo8+QB6X0fwBahM2nA7jPeH0fgDN6s03FxECoJOhXYbA3qg9+5zvA2rXANdcU9bAlR6jPPIUKAJBI2K+NR1BBBkppn/kHZp5axr1vFT7fme8Y++23HxWxYsUK17a+hI8++oheeOGFnp+feeaZdNWqVZRSSpcuXUq///3v91bTdhusWLGCHnEEpcxwVe7WBIPZ1p/8pNwtUeDxy1/a92Z36UulAoCF1GOs7RdzHkLIJYSQhYSQhU1NTeVuTsFQlQSLg93V/LO7tru/IpMpdwt2D/R18thOCBkFAMbfRtlOlNI7KaXzKKXzhg0b1qsNLBYuvvhia5EgD1VJMDh2V/NPXyWPhQuBK64A2trK3ZLeRTpd7hbsHugz0VYeeArAVwDcZPxVq8oUPNFXB2EZ+BDQvkp6++/P/sZiwK23lrctvQlFHsHQZ7otIeQRAO8CmE4IaSCEfA2MNI4jhKwBcJzxXqGIyGSAVatYSOLujr46CMug6+VuAbBpE3DSScAbb/jvJwns69dQZqtg6DPKg1J6vsdHx/RqQwYYtm4FOjrYv909nn13Ig/evVUuIvnmN4Hnn2f//BbDVVT0Xpv6ApTyCIbd6HFTKAX6wgy4WOAHQEnsQZ9CXyCP7duD7ZdnbWq/gyKPYFDkodBvwD/0qVT52hEEfYE8gmKgKQ9ltgoGRR4K/QY8YfALvfoi+gJ5BM3bNNDIQymPYFDkodBvwJNHXw8v5Wt79RXyoBRYs8Zt8lPkoSCDIo9ehKokWNh3Cq08yD/0f/hDwZfbq+gLykPEm28C06YBRmkZCwPN5yGarYrhP2trA956q59l6fVaer67/uvL6Um+9a1v0T//+c+UUkrff/99Wl9fTyml9I477qC33Xabtd+9995Lr7rqKuv9tddeSx988MHA55g7dy79zW9+QymlNJlM0rq6OqrrunT/desoXbCA/ROxZcsW+uGHH1JKKd21axedOnUqXb58Oc1ms3Ty5Mn0s88+o6lUiu61116+2ymlBX/H71gyrFixgk6duvukldiyxW7n975Xnjbss4/zt7ruOud78/Xtt5enfeXC8cc7+1Ey2fNjzprFjvXEEz0/Vm8C/T09ye4CVUkw+He6U3mwr8zgg2DxYvt1XzFbVVfbr197zX4d7jMB/b0D0WxVDAf68uXs79NP9/xYfQUDjjwIKc2/ICikkuCTTz6JxYsXY9KkSZg9e3bBlQQbGxt9KwmaWXJPOmkuLriA/Xv55Zc9j8tXEpRVBdy8ebPndkBeSdDvO37H8kLQQbgvkIyRJBlA32iPiKOPtl/vTiv3iwHRQhzQYtytY+/OGGBzivKhr1YSXL8eaG5mr+fNkx+3lJUEu3MsL/CDsEc1XWzezGp+XHEF8Jvf+B6u19BXlIeXbb9f2ekDQPwdihm625/IY8ApD6c1s3j/8sGsJLh48WJ88sknWLVqFd59911UVFSUrJLgs88+22PlUepKgt05lhf4Qdjrnvz5z0AyCdxyi++hehW6Drz9NsDFOPQKxN/IK8qoLyqjUkIkj+efL92xd2co5dFL8KokeMghh1iVBOPxeOBKgvfffz/GjBnjOgdfSfCyyy5DV1eXtJKgCT/lQWn+SoJjxozBo48+iocffhjTp0+Xbu/Od/yO5YW+GMEUBEuXAocdxl6Xc5bvNcMeaMpDVAd8Wdqeoj8tQBxwyqNc6GuVBLe2b8XKppWg8B5le6OSYHeO5QWeMLzIoy8OhG+/XZ7z8r+FrivlYUJUB4MHF+/Y/clsVfbQ2mL/68uhul7o7UqCuq7TBZsX0AWbF9BVaxKeobq7E1asWEGHD7cNiYcfLt/v6qv7RiivlwG0NzF7tn3exx6j9Mor5W367W+Lc741a1jI6q9+VZzjlQrm73LMMezvLbf0/Jjmb3niiT0/Vm8CKlS3b6O3KwlmdFs7U/TBqXg3wc+QvWzLA20W7QdeeWzaVHqz1Xe/y0JWr766OMcrFUx1YETSF7XPtLYW71jlhiKPMqOrC9ixo3crCeZ0e2TN0fKMppQWfyD3Mlslk8CSJfZ5+wLMgUmEX/veew844ADgww+L0wb+XISU3mzV1VWc45Qa5sTDvEfFdHJzMSAFoy3ZhsufvRwfbf2oeA3qARR5lBkrVjCHXHt7752zvR1A63ggFwYtE3msXAl89FFxCcSLPD73OWDvvVn6jZ4ikQCeeKLn92vqVPl2v4Hq6KOBBQuca0R6gqDkUSzC3V3qrYjkUcw+GjQNvgw/fvnH+NPCP+Goe48qWnt6gt3kdvZ/cNG6JUdzYwToGg7s3KNsRitzFho0+21bG1uj4TeQeZHH66+zv//9b88Hwh/+EDjnHODCC3t2HK8ByS+VvPlbFcv0IZKHl9mqWIOnRFj3OeRywNq17HUplEc22/0++PqG1wEA7elenGn6YMCQB+0r9oo+gK4OI0I7XVM25WEi6OrlNWtY1UPZjN+8t7JQXf62jxnTc/Iw8lXiqad6dhyvATlIRtdSrPgOhXYv5XH33cCECcAnnxTvmADwpz/Zr4ulPMTvd7dcQIgU/gO+9NlLeH396w5TdbEwIMgjHo9jx44dfZpAerNpkagdLyg6zCllqqBU7dm2zV5XAhQ+EIqzY0opduzYgXg8LlUefGhkT2Z9JoqV58lrQNq8GTjxRH9yKhZ59LbPo5jk8bWvARs3Aj/5SfGOCQDPPGO/LpbyEH+/Xbu6d5xBsUGO99s6tuHAvx+IR5Y+4vmdkx8+GfPvm4+sXvwY4QGxSHDs2LFoaGhAU1NT0Y7Zke5AV6YLwyqHdSvpoAlzINV15jjvDTQ25ZDLMBsC2ZkBzW0FwPwQTU2MPIYPL34dh1wOaGhwbluzBhAW00th/k6UAo2Nzs/i8TjGjh0rJQ/eDFQM02CxTC9eJGau53zxRe99SqU8Sm22KoQ8Nm5kBHH11XaeLV0Hzj3XuZi12L5C/v6WSnm0twMjRxZ+HI3YjdOpjnsX34sPNn+AC/51Ac6fc75r/5yeQ0bPgIAgqnlEaPQAA4I8IpEIJk2aFGjfri6gstJ/n6yeReR6NuI9/vnHcc6Mc7rdtpkz2d/bbwe+851uH6YgHHVyExrXDXNt13W7PV/+MnDffcU97/r1zHnNY/VqVkMiH8x23XMP8NWvyveRkQc/m04mi6E8KICej949GZBKoTz8FgmWw2z1rW8BL7/M/pnnf/ttFqzwxBP2fsVO9yEjj56eQ/x+d5VHKmfPhBKZBDrSHewNBXaldrmUSSLL7GPxcLxHE1wvDAizVVDccANQVZU/l822jm3Wa+sG9hC9mfMmk5Z3JL4N48cX/7zFMFsEdZib18Irj1SqZwNhJpfB9q4t3T8Ah75AHrzSyGT6ltmqpcW9TeYriMXYX0qZT6yn4NtoKuJSKI/uIJGxf4DOTCcadjUAOQ24cyG+8lX3AJLMMqldESlNKUhFHhz+3/9jf6++GvjmN1kSPRlaEnbP3tpehB6L3l28lknLbzvvHyjUPBOk/bJ9Cr1uv/2DmK16Qh6ftnyKNC3OYoVC28EPOMUiD54s0unSLxIshDxk/c9vgnXppcDo0UCeki95wfu0SuXz6DZ5ZDnySHdiV2oXsH0vYOt++M+jde79DbKpCCvy6DV8/DHwl78w6SwDTx6NnY3ynTikUkzVLF3qvY+sg77+OnDxxcW362bTcmbg21AIeWzcyPL//Pznec4r8dkV+mD65awSzTBAccmjJdECkOJIxEJIM5UCBg3Kv1+h2GMP+3U63beUh6z/yfqPeT//9jf293e/8z5mTs+5gmZEP1hv+Dzyma1yOeD664F333Vu55WH6XMF8e7QJtko5dGHwJNHVyb/TPT225mqEcpqOCAbROfPZzb+YtedyGXkri5+oC3kQb/tNqCjg3V4P8ge/kIfTK/BX9wu83n4raEIgu2d24FQcaJWCrnubduc74ulPPh77EcexVIehUxIZPvKVqiLfcqrrTk9h0PuPgQz/zQT6Ry70CVLWFDIlVfKz+unPAqZ9Ij75psMPvIIm4gdcohzu0N5ZDrZe+LdkUyyiYdLU4RekUc3sDOx03rdmenMu/+KFfmP6TeY8KGtxYAXefAPZyEPR9DwVZlZJMggyg8IXu0St5dCeTR3NQOh3lceIlkUizz4NviZrcqhPGR9qkPiXgya4vzdhnfxweYPsKp5Fd7ZxLJV33orHH+BYMpj4UKmtP/wB3tbVxcL/pBB/P6GDf5t3bhRvt3h80h3GhPXAMpDma36DngneRDyCDJg+T2geWpAFYTXXwf0TEz6WSd3KYWkjg4Saut1zCADE08MQQc4GXmk0z0jj65MV9GURyHtKFVaD9FH1NfNVkHIw+t3XbNjjfV6WeMy1zmuvJJdJ0/MJoGJE5MbbmDPCh8defDBwJ57Au+/7z63+PvddJN7n61bgQcfZNcjI06d6o5oq1QuxcgkgPIoldlqQITqFhs8YXSm85NHEPAd9KnVT2Fl00oA/wfAjigpBubP9/6sEPLoynShIlwBQkjJySOI6cmLPHiTj673jDzYw2rfqLfeYqaF7gzuPVEe3Tlfa7IVg2ODHSGbYnRaXwrVlZGHbOIQVHlsbLOn8xtaN7jOceut7pxh5ufivRo71n18M/Hmc88BBx7o/CzIvZ43D9iyhZGI7NrNyCn+PfN5eBODUh5lhsxMwhNGT5SHzBzTlenC6Y+ejh+/8mPrs2IqDz/wZis/8liyfQmqbqzC/3uVhacFJQ/Zgx7EPMZ/Lyh5mMd96SXnPj0ij2zC4aA8/HDgjju6d6zeDNVd1bwKdTfX4Uv//pJnG3S9b5mtgkZbiW32auvWDjsqcuOujdJziO/N9uZyzudhmHuJlOs7fm2aMMG9zxYjAvzNN+XKgzdZAYw8GDnY/VEMBjDHqepotXeDe4DdgjwIIesJIUsJIYsJIQt789yyB6pYyoN/GMz7zstrE8VUHn4Iqjx+/hoLq7rxrRsBAB1Z2wfk9z3ZZ9vb86/67w55mO+LWZo2mU0C1Dly33cfupX6oZC29HTmf+/iewEADy19yLMNvPIQJwOlUB5G6SuksvIbGjTaSnw+334b+Otf3fu1Jlut12aEpHgOcXJhfv73vwP772/3JT/Hv4w8RNLzmzAR4jy+2R7eWQ4wMhGjrVo4Xyxgm9cHNHkYmE8pnUspnZd/1+KBl/Iff8xSYxSqPLzqGPCdyHzdlmpz7ddbyoMnD78Ozocnr2pehZvescOs7rnH+3syIv7e8z9wb/T53mdNDdJ9ZOTx5JPswee39dhsJawu39nZjuj1Udz/8f0FHauQdojXVqjyqIpU5T0u7/MQ09IUS3mISvuiJy/CiFtG4ONtH7v25QdQk2CCKA8AuOwy9zaePJo62YRFnOHncs6SszwRLF7M6qmI20X4KQ/zfH4TLEKc12lOlqTKI5NwTGZWN33q2EeRR5lh3rw1a4C5c4ERI5yE4TVz4sGnU+DBdyKLPJIGeej2remtVNY8eTz+eLBB47HljzmcyOYDJoPsoVnbsi7vOVIpe9T5ZLs8VEVGHmec4d7G71cokSSyCZfyWLejARQUX/nPVwo6ViEDck/Jg3eY8ipJVB7mQCySR7GUh3i++z6+D22pNvzqrV+59uX7/I1vMoUbRHl4IYjyyOXs1EQXXuj+fOdO4POPfx5/XGCHWYm/jR95mIoun/Lg156YE0/R59GebjfyVtkM+NhS50CjyIOBAvgvIeRDQsglvXliM/6dz7/Ek4d4UwsB/zCYHcxSHlnbVtVbqUv4lBBbtzrTU/PY+PBVwAMvADpha140+wkeMsT7+NIZF9XyZjtu7rDVGE8kPLxCdcVtMsIOCmY6EEbuHIvnJAXmu+pN8nAoZe51OZUHfx9kpZD5gfv5T1m+INn9ChoVyJNHc1czcnpOarYyzzF3rpsItrU1458r/onNbXYURibjvC4/8giyYl0kD3Nf0WxlLheIa3YivudW/9exjyIPhkMppfsC+ByAywkhR/AfEkIuIYQsJIQsLGbmXIA9UOJsmieMYpGHS3nkbPL47DMWSrilOGmVPCFmq33sMfl+m189A/jsBKBlKhv4uXBBXvaLkM4SaYilWeCQzCYdaV+277JZrSMhHy3MB9QrQsbc1iPyEEwHAEB0NiIUWgu+kNl8TycPvCmUH4RE8jDPI/rYSmW2sre7f4wcbHuxOQgWojwaOxuxvnW99Z7/DSgoWhItruPlcv5+jXUthslUt2f74uJKvywKJnnkM1vx5GHuK/a9nUk3eaxr2eio26HIAwCldIvxtxHAvwEcIHx+J6V0HqV03jC/UIhu4P77gSOPdG7bsWGE9ZqPvS4UMp+HNZBmbUfHHXewUMLzzvM/XibD0jOsWtW99oi8m7dsKw2xh1K3n7TClUcIOxLOXPRH3nskJtw2gSV+A7Cr036aupLykVQ0DcgGPDFqptBBWeYwN5VHoehN5cHPuvnJDn9c/ncplcPcseYmY588R903Ip2z29ncxVbJvr/JHSvjRR77/21/TL59MnZ0sb5l/gbjBo0DwMhFDL4QyWNXl3PAbjczM3L9PZNx+jRlyRsLNVvx7bLIQ1AeZpaLWIgzSWYpNrdvtt6bhClm2y0W+jx5EEKqCCE15msAxwNY1lvnv/Za97YVf/mZ9TqdS0PvZjU+mdnKeriz7hCrRYv8j/fHPwI/+AEwY4Zzey7H1nfwaRhkkIm2dX4uCUrYg025OgM+P4UneXTZ5NHc1YwPNn+AjJ7Bku0seH4X90Qmk/KRTHRKyvIH8bNroDhmK5oLGKcsaUt39w1KHpSyWaxDeWTkysO8N6GQ25FcDOXR1gb885/2+10J23wmy0ydofZ0viXRAkop3t7gXoHnRR4b2zaCguL19a8jmU0imU2CPHU3Oh94EKBAU1eTlDzM3yEcBhY1OFNDWP1QUB48eciyGBRitgqF3AXMAG/lEdM4GyPVHM+S6dsZVlncCbXV1pIctbgYAeAtQsjHAD4A8Cyl9IVyNijb5YxeMXPlyOD34PGRQFYKcVPJ5NzkkS9O3lyoxKOtjeXGev11ZxoGGWRpsP1VDGGqgZuJ+UlyL7MVrzz4vGHmA9ORtJ/yTEr+I4jk4bUP374g5UA/bfkUpz96OlY3r3ZFt7CD2icsZBIhmtmC7GsiKHlcdBHzX2zdZPclfgbLD3LmvQmF3G0qhvJ49VXn+9YumzAsUy2HjG4/UzmaQ1uqDdms+/fN5zBf37reOj796CK0LDoCSAxB4xvPub7rVh6Ck9pUItxkKZ129qN772Wm23ffZdv33deudmgqj2yWskR3Rx/tyj0kRlvlUx682Qq6ZpEKYJPH8Krhrt+lGOjz5EEpXUsp3dv4N4tSekO524SQs9f5+T38BtNrrrFfB1Ee3VlZfMQRLMV8EMgG006/SGSJ8vB7mKWV/HTnbKk9ZWeNM00NHQmbPNIe6eS7Qx75EtRls8DpN96Op5a+hEPuPsTXYQ4ES5JpwhyQgyyw9FrDkg9mMa8N/zvc2pbPbFUq5SHOtl97lQApNgmT/W4Z3SkLdnTtcBC1iXwO88bORmn4e9M/7oauO1mRV6aaBuwUOn+72YEF5cE/Nzt2sH510UUsVHzRIjtNvKU8spSl2H7tNeCXv3S1TUoexkQqHGLnNh3mDuWhhx0myu2d2wEAw6oGrvLocxCdo37hun6dmy9uaCkP81hZ9+KOfOQhmyHK1IgXZAvwxDUqjoHENDkFVB7bt0s2CsqDd56bD31HgpuFZjTpDF+0K8tQKHncdBOw4tY/AI8/jpZEi9znwV17IYXBRFNGkH1NBA1PNZHU7ZvYHbNVMZSH2C+u/OZI4EFmQOjKdCGn5/DmhjctIklzygOUKQj+tzaRz/TY2NXIBlT+GihBY2IH9FanbTOf8rAUsI/PwzoFdbfN/F0pQtDNScgjjzh+HNFslcuxFDtP3jUD6BqCujir22H7PLhxgmoWqaxsWomGXQ2ojFRafp5iQ5FHN0B1duPNusDdVR6jR9uvCzVbLWtchq89+TVsabdDsAp5yEdMdo/kQZSH43r0CJPJ3EzM73pvvFGy8ZFnsHqVPSDz5GEpjyTvQYxJB+lSKI+HHzZerDkZgHyRII9Csg2Y7Q2SPUAchAonD/v38oq28jNbeQUf+KpSyf4ubDrMatN9H9+HI+49Ahf+60IAQIq/SBrCutZ1UuWRTxU1dprkwS9vD6GpCshtcT4DvM9D04BEytnoTos8vJWHX9s0DdBCbGPusKOAKVNYiOM771j7yMxWp58OPP2nQ4Cn70RtvBYAW+cBeCuP29+/HQDwuSmfQ0Trnl8uHxR5dAO6QR6DYywu1S/iyu9BlznMrWPlMVud9NBJuHvx3bj0mUuDNVrA169zh1KtcWdG8ScP01kc0Gzl9ZD96aLLrdc8eZiz0ESK/6HCUhu5+cD5kQc/swQKL7Il2p0BOJSINNvAmjXAP/7h+mF6YrYqmDxyNnkUy2x16KFAdXXwcgF+CiGRSeCJlWyB279X/Zu1k7/If9+PtTvWO/qZCbEImAiLPHjVoofRWAXoO1pdbeT7kbimqDPF1FCFZoe+ig5zHuLvFgoBYSOxZu7AQ4ATTmAfcGGNMvL44APjzYbDUVfhrBgY5ZWHrll99I0NbwAAfnTIj+SNKwIUeXQDZmcdHGfk0V3lIYv8sY6VR3ls2rUJAPBeg8+Sbi+QLEbVDg20az7lwf7aD+bbb8uP4ztD5GaFvKowTXjJFL8sXJPasEuhPERIHeYcXIETDQ0sXep55znyd/ODHU8eXiasnpKHTuzJjZfZilceQcxWZurxvOHcBvzIoyvT5TKtpNLcjVr6Rbz3VqVUeQD+faups4lNNvjvUg1NlYDe5uwAotlq8BznxXUZ5DE4Wm9t81IelMrJQwM7QXbvfYHDmPLiHxpC5NFWAIBwyjJbmeBDdUE16/6aFolp9dNQKijy6A6MAcSMn+6uz0NGHtaxJOsHZD6PfKuzpSA6ZoyaGGjXQpXHq686M9lauwYMi+WJ2FRhqTS/mk2TKg8+esnLFNRT8ujMdAKJes/PXf3gb3+zY4bvust6bbZVNBF5qRBxEJLZ033BFbAKYrbKd34efu34+0d/tyY3fvulcilHP2YJE50PztadzvVEPPyescbORmZapW7lkWt3dm5dZwtyAbZeKTppIQD74hNJdqJBEXsAz+W8fR5S8tDZD52bPdcuFWhJC0YeCW7VoePaOofjwxtuA1afDGRZZ3EqjzC6Ml3oynRhV2oXolrUMnOVAoo8uoPxbyKmxaw8+cVQHi6zleRBkT3YWsjeLzCPEIrpoyRFCSQQH4x8ygMAXnnFfZzukIf52kEeeZRHKAQM9RBVhZKHKyR21WlAxyhxL+uVS3nwLJrJsGIPQlt5eIXtygZvWRGkyy8HbrlFcgAu91g+s5WmAZvtdWbs2O3egQBexLJ0+1J84+lv4OC7DkYincI3vuF5CABAa6rV0cZ0xtlhmjubgeXyVbJ+5YUT2QSbhfPKQ9fQVBOSpvH/2MjReMghhgoevtw+lqGGqsNO8giqPDSSQ5ga5DFmPDBuHFBXx0K0zGvJJfHMques987JWgzNq/YEHnkG+GUa6KwXfB7MbLW9g/lyRlSNcNRvKTYUeXQHoSzi4ThiYTbFDerzEM0BstXO1sMtkeiyfpDJ2ScISh4kRFE7KFgdMJH8+JXBMuUBAH9f9De83+Bc0NUT5ZFOcxeWR3mEQt71FnpEHg0HAK9f67u/ox8kEqxeKSGsgD1gsap5nwiRpwA3YX4mG6DF+7JqFctF9iOZiZsjjyBmq53OzN7Q335XclAGr/u6oc1OYPnS+5s8v29iW4edL2pXahcywpqO5rvv8vxuvtr0a1rWCD6PCHbEdeSE4S+btX+TigojAIJLvZNKs4utjtgrtr2Uh6xqZbItbZutEGYdYO+9HYFgrcmd3mYr14Wd7FIeiWzCcqabZvVSQZFHd6BHEA/HrcLyQZVHLufsTVLlkU0BH34d2Hg4RJgzVV7iZ/QCDeDGccTkd14QO28ixZ3PJDhBeezoasLNb9/s2NYt8siaZiuePMJoS7Vh2TLgqKPYWqunngpOHt12mP/9faBlins79VAeH37IRuQ5c4Djj7e3wdlWP/Iw2xpEebQJfOo16CSyCSxrXIYXP33R02GeEwZuun07sHat9HiexZe43GTNnTvlO3EwZ8sAI49sLrg5Ni957FjjmIzVxpjpMSH84OZxNI2N650ZJ3kgF8ag2CBEYC/M81Iemza571FrSw5hZK3vAcCVbT/DFNip1DuzHY7JmO9zQ3REiTNU16rzAaAyUunxxeJAlaHtDnJO8vDzebDZCusMlBLourNCmXVI43XLJzOAp/8mPZb5PX6G25nuhE51hEgouPIg7FiVld6RImK7TCTTWQCGU0GXKw8QZ44dAMjt6gRQBU2jyOXcEiqTyyCiRaTKw/EQUg0d6Q6ceIZtXjn9dOCjj9jrUEheqQ1wKw9ZChPHZYjNTNf47u/oB8sNc8c++7B/hADLlgHJJHQ9brWVh4w8wuFgdSz4AYylJaGwTGocubcmWzHnz3MAAPW5LMy+ySuPTEcagD0o6Qix3CJXXeVqh9fgZi5QA4CWrgDk0ekkD3Gi5Qfp4lMAQyuHormrGZ+2fApQ29w4JDYcrQBSWgjg2m+6GkwLQTKbdJQdhh5GXbwOEW7A9iIPwJ3IVE9nLeVh/m63LjrasU9nqjPwuimAOpUHDVk+D8C7jkuxoJRHd6BHEAtVomPtTCAX9lUeOzud01uvxHzm6/YG7wU95j78egIK6liVHQRaiA0q1QGSbYqdN8Xbok2zlcvERp2mpUwGuZNOBQAMhtvkBNi5enhiNH/XdMZptupMd7rs8uZvEwqxyb4MYmLE/MqjsGAEh/Iw87rsuSf7oadPx5LMnrj8y+1WDrFiKg+eCDMZoKWD81Potid+0TY7QRo/u3coj5TzpusIsfw2EngpDytqLhfGfx4cI9+JA78yOpFNIBcw1TrgrTzGDx4PwFjUy/XRtb/6F9AxDCmDvc3+IicPPlgjgrqKOmiwIzKyWeo5ARMXxaZSXLSVx/V1pLscbfWNrCMUkRAXHUJDSGR7T3ko8ugOchHsevly/Pfq64Fn/uLr89jR4V7Fio0bgRUrpOs8MgnvVWNm5JOYzsF0IAdXHow8qjwmJi+8ADxkVCwVO3nSseZC7jAHoc6FfHffjdxSNhPXPPKAmStmZWarTJYjLEN5iOCjrcZ6xALoui0IAO8Zq3WqQD+o/Qg5+sHKleyvmaVyzz2xN5bgT48Pw/e/zzaJPg9RieS2NgL33gt9uztjpdg03k+RSADtSa6P6GFrFspXgeTTc/DKI5cRzFYgrC6BhCm8lIc1wfngCrz971nynTzQlelCui1YKDngfR9N8gDg7qPvfQ9JI9jEDJE2SSgcZvfeTR5MefDH6kwlPZXHtm3O96lMyGW2EtGZ7nQoedEc6QDRncrj1V9i+4opijz6NPQIml8/n71e9DVf5dGacA502UceByZPBmbNQm6nTSxmZwpCHuJiNH7WFgTJTva0eK0riET4JG7CdzO8dPIwW4E6Ce6hh5AzzCMactjvuG+6zmmmVZCZrbI5Z6iuH3mEQt41RcQkj/n8MJR4TKs94FAen3zC/k6f7vzLfSSShVhuOHvCycBFF0H/zvdc5xLHcX4ATSaBrjRHZHoYo2tYOgOzBCsA5HSJ8kAOOepsmB6vYuwkWUXqpTysPrplP/kOPvh4YRyZzbMD7+9JHoN48hDUMdGRNm6A2dd5n0dGz7A0OHwf+PBS1MaGIJOx7Zk7u9o8lYeYpTqV1VxmKxGd6S4HOcmSldqgiBBuvFg/H8tv/qsijz6NXASEe8D8yKMj4fws+63v4IPcvmjEMOQ67F5ndqZs1vuWjDLMtmJ6Zln0URB45cqKRLzrLZsRJwB8lIeO1mQrK0yzYwfw9tvIGf4hDTnUj/yL65x+ysNBHlRDR8afPAYNcn0shfmbb94MrF4t26ObZitKbYfMOMMMyZGH6UsRzVZ8uhoAyH3G8uHrO9wjiKg8+PuUTAJdKSd5zBjGFBCfR0zqMM+kLaK3zlVrhKYuc1dC8BwETfIgha9Deu4fwcLITXiZrcYN5kzA4gQnnEJaY508YqyD4ZWH1Q+FCUTbomMcxZ9aunZ5Ko+0ILJTeiSv2aozlXQQne8KfqI7zVYGFHn0MgrKHKqHkemotd76OsyFXrI0NRUH4gNM0jY5HlLz/Nms98O2zz7sr7iewFQexSraEw4HJA8v5UEoKCjzY7z7LqDryO27PwBAi2roCgOo2OH4iunz4MkjnUtjwwYgw2fS1cPSHFI8edTWBrpMa+AbO5a5JkQHetAU6xph12/1g5072cgxaJBtG+TIwyRt0SEvmtt+gWuAAw9kPgcBYn8VU82L5DF1yFTXMfjLs8xWmaSLPPQag41XOOtbAN7kYSvPPJ1SdwdPaDGfXPlDPnVtkpFHTIthRJVdtK0qISgPLY2M0ckjuhkSzj7iyUPTnO3TW8c6/BA7u3Z5kpdIHhlE85qtdB2O50ms7umA6PMwYKX12TIJ3/tePvXSfSjyMFAQeRgJ3Uz4KY+0MPquIjMBAF25GBKw42Ut5ZH2TnbkSp5ooFCzVT6Ew7bz1tdhrns7zAGjAtyCBQCA3F6M+bSKGLoiAC48wfENmfJoXzMXEycCG578KnfO/GarKZKIWh7ng2U8zOWchNvaKl5FMDY28w1ZpL7VCFMdZUf4OJUHtdrKY7hQduEOfBu45x7kQm77oh95tLQ4VykfM+FEnDjlRPcxJDXFQ+mUW3lUG+Rh+nE82vHaayxEFeB8HvmUx/Jz3ccMey9KDGXdUR6ywbsqWoUxg2xH/eCkMMHRUsiYyiPHyEpGHmHNeZMiqHKQR1tXh6dTW7bdfK48Taa6BkLt58m/qrZgtjJg/vaPXfVN3H47cNllfsfoPhR5GOhJzQI/h7m4UjZ2uF1BtxP2g6DrQFbPShMimjA7nKg8CnWY54Of2SrJ5xzyWOdhDhjNXc1soRyA3Ky9AABaZRSdUQDVzlAUmc+j7c0vuhuXx2EeCrEZ/Ve+6aP369m5c7s6HGpDHMwDk4eRb8jqB2aMJk8e9XZaE2L8qKLZSpqeZMYM6JMmuzb7ma0OPdTp8zh4zOFWNgTHMbhZv6U80gnoovLwIQ+zTy5YwGobjTfcDJbyyOQxnbROcm3KhLxjqEPUTaRmni0eVZEqR76skW1u5ZE2HeZGyhbe52H2Q7FPRFHtNFt1dHo6tUXlcQxeRjjKDugZgquHEQ/Z44K/8vA3W3W1suN45ZvrKRR5GCi0JCkPP+WRyjh7Sddxp3uef0NDCmjLH6pbLLOVV+YC3mwl/i6yUN2wa/ZDgUVfwV1/rLWVh0keFTF0RuCMn4dceeQSklhiD+XBh+oCQJZ6E3rXTHZxua2NfGYIScqPYDOKIRWscLtLeYhODAOhDGsbIc7wXK+kjump7uR2+ZIlJjL29WezQEVEQh7cAkdLeaTcJiO9shpP4jSsXMGSasl8JYsXO79j3ccVn3cdz4Gw+3xpzQ4dGznS+dm0sx+GiJtuch+2MlLp8HkMEpVHOIUsMRzmhn9G5vMIhZwPSVh3Ko9/3Xwy3nrLfX7AfU+ewNnQokbf8xpvqIZYyCZcX+VBKCLETaZiQI2/euk+FHkY6JHy8PF5pLPOHtQZlyfWy2SAKROqgDWneB7LU3l0x2GeSgEbN0g/ika9lUfakaSQkUeECAMTocCT9+LeX8/GzqYMUFcHfRQzIYRiYXTGiCNZHyD3eegyNqSaNPW5Wf/BIg94E3poClMKue3NaGm2r0e8VoognUK3zFZWP5CZrTiQtD2r5ZM4euW2apvknlD4KQ/AnVwvn/Kwo63c1/zW+xGcgScxM70Y2LjRMfC99tmb+NWbv3LN0P3UuANh931KEZs8HLPm467EzBPece0vQ1W0CvFwHEdMOAIAcMgGgZmfugudW/cGAETS3uQhWK0QppWBsxrzyuPUOesxGLugxdhN9lYeGqJcplzftUg05MxtZcC0RMQqWEMLzcIcFIo8DPRIeeSEB2DjRmauWbAA6Zdfc3zkFdYXpJ52UZXHr38NdMhty7GYTR6N7U7Htiy3VVQkj5Qd7pRCDJg2DTljoNI0wsxWAZQHDcvSlWpoT7nb3WnMmM0BOOOjPHIjWLtzqSy63l9qbXeRR5AfNNphma1kPg9KKRZsXuBYX0EMk1Io5AzP9VIeHcOHuLb5+TwAIJESyCOP8rDMftDxm70ecOzH+4J+fg1xPCvPffICfvLqT9CSdJoJXUkiPRCDu+MnYZMH//uMm70Z8UiA0ouwV1c/cNq9ePph4PhPvYvFV6VbAdih8H4Oc41WuMxRXuAHbVNthg3yuOcejy9RDWFqX3R7u08f1DVnDXMDzV3sXkTjPRjUAkCRh4GiKI/OTuC001h+jP33Bw44AGnBTuxVfS3I7EAkD7OeMZ+RNDBuu83zI155rG7Y7khy6DBb7WLhQbGQsNqwZQ/r5aKhcWCPPWyzkkaRDlEQBCCPrHw07UhKBpws+03MGXCG2sc5YqqQViPKSCkHDenn7RTAmQyLLP7971nAVE4PQB5U8/Z5jB6Np1Y/hQP+fgCOuOcI6yskVZjy6KxzL1wxeW1Xahe2d2yXBDbYG7IffowKWSEl3f34a8jhyi94JzK8/oGJzsVvhr9rfZsz95WfGudRKVGI7Sl2f4Yf+oyDUG894deoiAQovQimPABgfLYKp3wC5AZ5p9KvTrHB1vR/hcN2OLxotiKp4MqDd+RrGYOMDLPVXXcBa9dJBh09jBBs51dXl09WXKqBwH1fzWeJ+FS9LAYUeRjoCXlYA963vgU8/TRLGrXvvsA++yAjxF96KY9UMn8DzAwRJnkMq2QZALtltmppca9KMxCLASHNaE/zTLy53A6PdCiPJV8GKBw2WgDAynOsly9OcJIHMcxVVYLUa/iY2fV5c4eekbcvkXYPTAluNg8AGWoTTKzKORjqEWYLyEFD+nXbDJJOM4fvd78L/OhKmievkHmwsG+01dJGpmxW77AXkph+BUKct8DLB9Wpu2eQZn+dd+c8jLx1JNoF6ZrmVuVn33wHFT+7znkAD14MQQfmzpV/aEDMNQYAHWlnHwyqPKp0+16a9XE6DPIIh0OOIIJJ9eNQEZH3CddxzbxORo6Q3GC3erP2NRZOmiYi3mEuqsHcwy8g05InKZoBfvGiqTa1mH3A95Y3uL+kayA0YNlYXZOOW80dTLkp8ugl9MRslcqlgKVLgfvvZyPvggUsg+pHHyF91DGOfT3JoytYA5qb7QdzeBWL7WQ1mmnh0VYeq+liMaA1bZsh3n/Drl8gRo9B1xDy6ewraisc5GEuuqrMOo+zYwmLQkskdSBnPGAZj9S/Off5ROWRgz3CZcNCriaTPKoGIZ2wP7vhBvv+vH33KmRbApCyrvmarUwTAg+StJWHB3870ClxYlPKZsdrWtiq7+2iebHFNv1kEUbFn/8uHID9UIQAY7jUU0HIwxEaayiP9oxzQA3q86jkwlLN/myRh+Ykj3AYVhmEfLBKxRrhSnqtN3lUG+ngeeVh+zycA3AHrUEmEWRWIcD40cIV9vWu2brNtRvRNehZbxObA1ROHjuM+iulrOUBKPKwkE953HwzcLg7SzoA1tH+fc//4YKzgcTXvwrMnGl9Jq7z8DJbJRPBRv5EwjYJDKtiyqN1zVKgpgZYJ0+b7YlKeXKraBRI6rZfoStnD6KOJIWAK7RQxLsf3i9VHtXC75LKppHLUaRubABu2cYW3nmFeUqq+aXSTod5DvbMNyOQRy7Eri1XNwxp2Db0//zH3mc0tiDXFWD2TMOoi7OBKZVLsVGdC9Xl61SYIAbREQKcfDLbNn26t89KprR0Hfhs52fGBYXR2u60pWSW2gv6stNmIaIDGl9C1xj0w2Fn8axQOOTp6Lfaw3OZQUIdGYnyCNClK6K11mtzUZ+Zcysa1hwz/3AYVibrvMclhqkvCHnorO2m8uDJY9x+yx37tmGwo88EhWmq1OI2G25s3uHaL5YOBzaLfXO/b0vHrbZOW9mWEoo8DOQjj/POAx58UP5ZZ2sEZw1+Ho/MAf58lHNAFovaeJFHa2ewDtnUxAZawJ6ptdEk9M4uPLbAuR7A1+FbUQFUyB/EcBhI6/YIkYEtl9JC0rzfn/Bn1MeNAhrj3AHlHa1zBOVhmK1CztlVTs9hZ2cHS3ueqEdUi3krj6TbByAqD514kIeWRNq4ntygWqRHcPmPONDpeyJHPTzYAgZFuFDdXbvY6FpVBdTUOFKNm+gy6kHU1ABXXAE8/jjwv/95H1/MUgAwotmVMqbK976GV59wrpfIrF1nvc5OZibBCn5QMiLlwmFnbZdQVQVAiC9/OHJJUVN52OTBysimHLXpvVBRYzPXiGpjRbhBbBFNcygPTWMrx4MgTgxVbZit9MF1nvtWgT2UJiny5DHnxPdw7bX2vsvDcx3rs4LCjLALV9jPeWOL298Tz2jIJINZIfYZMU8+bhlrxUR/TbGhyMOAaLYSk+uJ9aZ5NH1sD9o76pydOyOYZ/LVz8iH/fYDnvgNqwEw3PB57KwAHou5F9S9vclnddDBBwNEfvsJATqS9myXX3CWFsjwS3MuAjWdsTEPM8/IkdbvS02fR0wgLhpyFASKoALIepmt3ERrhqba5GE/mJkoN2pGEpYzPZcjSF8sX36bHTYa2ZDbPCarFRLOsTofqWzKGaZLiFR5mORRX8/61DnnsNXlXlzvMhWCTXas9S5CxgMASDfZs9rs4Hpg1ChUOFLbG6urIxLyAAuRPcL28TuQ5FWOMdDv4nweWT0LCipd0CeieohtMxtpphMxSCca0RzPHCFAVAs2ybLIw1Qeg2q92wBn9B7v86iMxXDKiTZ578p2M19UKgUKQOPIo7XTbY6MZTRkUsGsELmcx6Q3y56tPufzIIRUEUICGuV2H4g3Qcw460ceu/SN9muhtkZaII9EQPOUHz54imUqHbwrjdG7gIwGfHDO9137rdnhzoJq4cgjfc/RmbIH38/+N896nUk725/JcFE7YQ87N+HCO4lRB7pS8LdkY9jWYYezRlHtbbaSkEcqw8jDvEe6xpEHH7IYTiKVM5RHDkiPl+cyyeYIqmvdjlGzKCCP914aDaSqmPIQ1nhYhJjj0myDzUzq3dY3KcQ+BLD+KsvxZbWfe7SzOQKceqqgPBh5uJRHNVPOkyYBv/iF/NiJTdyqM1N5GOGugO37iUnCSEUMGzLRej09aczoDfKIaG7l5+fzqOFqdVWEBLOVj/IQyYNXHhWRCtD16z2/K0N4sHtZ+JKhOur/D9iWtEldttg1kgkjE9Cl4kkeOUN5lNvnQQgJEUIuIIQ8SwhpBLAKwFZCyHJCyG8IIe6Ma7shROUhxnJrmjd5tIbtziJmfBXNVolkAeQxeL33Zx3DENvQgDnGqbfXux8q3xK1echj5B72NW376AB8ZpjXM0LixmwWyGWNTqr5rK0wlYdBHlU1wsiph7Gt3VYeYVrp4zB3k0dSWCSYI5zZrZqvuJRG2ojEyuXc99lENgtUDWeD89yJj1vbZWnsb75qT+CJh5nPgwvTTWVT1uJHZGxzZiPYDFskD+ohS0X1CjCVIlssaSJNbNWUzQI45RTE+MPkPMxWNfaA75V1OdnABQFYyqPV2mQ6yx21JjwwvNJeQj7rU4OsTeURFnJsUX+z1cknA9XDWdtiMJgkgPKYjtUIcdlzw2FWkApg56PL3WlZ/BCJu5+71cModlYAyxttH0pHyn3/KtMaMrlgc3pv5WH8RuUmDwCvAdgDwNUARlJKx1FKhwM4HMB7AG4ihFxYwjb2CsSbIOar8VMeWa7jibMJRzpxAElBkobDfmTic/MXXobo+k2YbZJHp3u24xvCe8ABjrdDLzrb8b4r0wkc9ivrvZljRyTDbJYjXpnyGPIJKKUu8qiuG4F98JG9nx7B9nZ7RhvWqwAvE0FOQpRcvihAUB787kRHSueUhw95ZKNs8BtPbPOfVw0UfHKaS3nwCwORdtvJXRUPm+T5uEQ/E8CZrTy6T8pR7Q7A/PlwjEm82Yoj/VC1/Zt7jT3JzVyaVkN5tKXt6C5TeURpfv9EJBTHN2qOwjnLgT3eM8KZLfJwKg9d9zdbNbRvAAkb5zbJw/B50EEeRV4AVCCBPQfb5sVYzF7nURmpxNS2hXmvg0c8Kvvh2I3albTVrBheDQDQo67cYl7wUx6k5EarYORxLKX0egBtlEv2QyltoZQ+QSk9G8A/StbCXoJ4E846y/nejzz4FMqiKUGcNTYKrBSPd/cWE0Q3b8McY7K+jfMXmDDTFEhR4ZzV1wzjRlFqFHOK2NdiLmYTo62c5CF5GGq2IKtn7ZKqpvIYOgoLyIG4h1zMPshFHNfQ9vRPvdsuUR5p4wSW8gjZs/iMw3VBA5FHJgPkjOikCm7NiCd5gNVh58nD4SyXkMchh7C/XZkurN25FrRRnoRIJGzAUB7pTk+ndJoK5FFdjWwFN5jzZqsOm7RCXD4OL+WR2M6Z86zz2/0iaaymjkpCqkWEaBh3fuFBPP44UPnOB8ahjISF4YgjtKuatvuarT7c/gEQMslDCNX1UR4acghl7Y4QjTprYtRuXIJtGIE541vzXg8AVFf6kab9vPN+RRPJaMBiNPBXHpWRyvKH6lJKTQ32b/EzQshBwj4lASHkRELIakLIp4SQH5fiHKLZShaW65U+gn+ARVOCaObpSjoNmr5x/tTn5mspRLc22marDkF5ZDJ2NI7X4bmmDZvSCJxyKe4YehSwcSN7eKJu8hCvJ5PhggAiErOLzmq8m51cN7pKVcVgaHtMRJwa39EjaOQGsfaFZ3g3XEIeJklbua2I3ZYsP4YRimSOXZeue5MHpYDJ+5VByUPPSMN0I6GIlDyG1bG+8PWnvo6pf5iKdTvctSr4a+NhKQ9XOnyGNBHIA0BHnK+Lwpmt2rZam3nC8Fy02Mj1cSujsr2zGbwQ9cgQwCuuEMJsocm0aahsNe6ZqTwIBY48Eo/iC7gFP8SEK05FjHgTUiLbgY5tzNfUsMpwvpvkUeOtPELQEeJWxMdiQkGl1asxAo2oGxos+m6Yx7mmpGscz3RXyt35kpHgkVx+yqPUhaCAYD6PcwkhNwGoIYTMEJzld5auadb5NQB/BPA5ADMBnE8Imen/rcIh3gRx/Vw266M8uJTk+cxW4gKgmK+y9yGPbAWiaR171LFIr7akkyjo+x+4ap2L4EVQTawGmHcnpg5+A1i0iH2XM2dY9T2EacJxx3HVSaMSG7weRiqXsmu0U/bADKkYAsyaZRXHgR52E6AXJGnr04LZiiePtMYNvkR3+Dy8YuqXLwe2LGa1tyu5PFmBlcfo0ZazfM+he0qd//Vdm5DOpfHIskegUx3vZJ2JKvfayziul/LIdLrT4RtIV9rrGsxrDEc5tckpj3jzZmszTx5eyqOlifs9TdXNTaDMQTHi8dveeCMw9LD/sH3bjXt52mm2Q984VmzRImDBAnxh3Lv44ZB7gTfeQOzt94AjrsOIfd9zZ7MlOStf18v/Gstyt3V1AfE49Kj3LC0EHVrGeY+7sqz/VGgx4FNG6tHBHj44AIOmLLFeV8YlJEMoLsvNBf9M05x7v6SYJ84Hfsoj0joj8HG6iyBmq7cBrABQB+C3ANYQQj4ihDwDSLKaFR8HAPiUUrqWUpoG8CgAeV7zHiAn2JUFqw6GDvUzW3HKQzRbCeSRbnN6Sf3JwwfJWkRzwKCpsxEJRZAR0kHkXnrVN1U8AEc68uoom/F0RAEsXswGJs6XYw3+gvLYuJF7I1MeVEMqm7JUTkZn7ayvqAdmzULEXAmei6Cx06/mJgeZ2UpUHrDvQ4bwas9WHn5mKx6VXJ4sv/uV0eVmqxnDZkhXxVdu+RRb2+1Zf6fuNGOYqlQMjwYCKI8KO7rIVB5zx7IovXgGnM+DIrzVTt8ShDx27HQvNuRn1AljwWbUI36CUkA3Jg2/v348+8m+/nWEKBDJwSaPhgZWFvKll4BfMf9b9NkXgKOvwZzv/QzDhguKLJQDjryWXX8yDDQ2ogFj8GXtQXy8xHsiFgprCHHPj8PnsbODse/YsYjE5QNAdGgDRn71B9Z7WV0WQoGvVB8Gx5Ar6ROJlCTfGMfCFRUs8xEA3H47cN11rt2Bt/8PW258w5mDrAQIYrbaTCm9H8DplNLPUUonAzgWwDUAji5t8wAAYwDwyYkajG0WCCGXEEIWEkIWNnUzeb3+unOVFv/gvPOOf7SVw+chmK38ysoCHgWAzDb4RUTnIojmALLHFAytHOoqBau/+jqWPzvf99xmmol43FAeANqjAJYuZcqDI49cjl2Hb74nGXm07IHnnwtx5MFOWl9ZD8yebZOHHgmuPCQOc1PhWVl1Qxx5hLgpMKFI6uyz9mSnIyTZC1W6vU8hPg/TbDVj6Ax5ePFnq7GlfYv1vjHuJIkPPgD+9S8P5ZHJGspDTh6ZuG06Me/Z3077O85oH4PX7wU04zfUcimEd9mziCBmq5YO7lqohj987g+YOdS2RXUljcSdCfnKW123yQMAXnkFOPir03HSqMVMfRiEFMvpbGXu9OnABRcA8ThiS9jK+VQ2hc2d650HJjlgFouM69gVARobcQEexgOdZ0trfpiIjKx3pKJ3+Dy2Gb/NtGne975miyNggA8ZNjEoTTF0+ERUR7gPJfcuk5GU5eUCUQYNAk4xqjY0NnqkbN/gH0lZLAQxWxEAoJRaISeGs/xDSmknv0+J4B26YLfnTkrpPErpvGHDhnXrJPrM2Y73vNnKDKn0monxysNttvInD09CQh7yoBqiOQB77MHSlAimHP29D7D47q/5nvvuu9nfBx4AaqIGecQALFniIo+ksY5izQfuym8WZOSRHIJvnD8KL73E3qaNgdilPD79HJraAxZbDuDzyHDpVTIab9KjyMGoWZ3J4cU1L+c9XZVm30N/8kizpzkeB2prLWKYXj8dId1tNmlbuxJbO2zlIUtpdPbZ8gmIvmUb62v3v+L+EoB01B6kTPIYP3g8/j3rehy4GYgajqBQV5ttOkRA5aFzayZ0DdPqp+Enh/0/a5OlPLrk5FFVBVDunO+/D7z3HvDC1r1RgbCtPA49xM7fUl0NHHWUFW6czqXR0LHOeeBQzjK15jIhoLERizFXfhEcImNHOMjD4fNoMIIepk/3nOil1x2AtpRNHuICYwAY1kmBYcOs5wyAXcY5D9JcUbR4ZpfvmNGbCBSqSwj5NiHEkceBEBIlhBxNCLkPwFdK0zwATGnw1XDGAtjisW+3kasb6nhfX8+y2N5zDzCNL+QWkky9ObuzaLbKSmaNPPw6guZHHrrGHqQ99kBtvNZaVWoil+e8AHDRRcwkfM45HHnEQ8Bnn6ErsctBHl3pJJYtA7aschcmsiCLtjLw3HPGcQxb8rjB44Dp0xEO2QNjerVbKRFZQSYZeQjRVmndJoxMBWdbI9SuJUI1NHXkJ6zKYfZgGQ4DX7x0q3S/jBmxY6wu39DGfBgTayeiKuxepLZr06fO+vP7suSFhwkLxkW/GQDoGzaxvrZtH2lbspo94DjU4gknAIQgmjLqq+xqdpCHuKJbhmZwzwoNoSJcAY1zZO/sZNPhSJfg9KjahuM/vx5HHw3kuHNu4uwKlSPG2uQxV7i2449nEyawtSRbExudnxPdChdPpwmwfTvakT96KTt2qKfyqNho3Gs/5QEgmbMnK7W17s/33AFg6FCn8kjLc8v5IdTSDG2tz+JfL/QkbbhXWwLscyKAHIBHCCFbCCErCCHrAKwBcD6A31FK7y16y2wsADCVEDKJEBIFcB6Ap4p9EvG3HTKEraP76leFHYUiRgAcyiOjZxzpqHuiPHzJg1MeVZEqF3nsjAZbaGT6diyz1aghAKXo3LENGG3Ht3elk1i0KM/BNO+gO3OA6Mp0goBgwuAJQCyGyJjhXKP3cH0vHJKYlQIojxS/GDPGETrJ2VUMdc1l7pMhPmqE9ToSAW64KQN8ewow21kSNW0uyjSKQK1ZVgMkazChdgKqQrWu47ZtXedUqoMb8Idfnos//Um8Nr7QObsWuqlBukLZ+o4Yqmti9Gg2CKeZySTU3oIwN0lwmq3kfbcR3D2jGuLhuOP5aTQUZCQr9MG978fZ//dfhEJOsxX/DAyO11nPUzwszMwPPBAx42upbAq70iLxU0Bjz14qRfIUALexa3wdNK6+jEN5rDU6bh7y4CdaMuVRnQoBw4ahmlceC78VqH0itKddga/50ZO04R4I4vNIUkr/RCk9FMAEAMcA2IdSOoFS+g1K6eKit8p5/iyAKwC8CGAlgMcopcv9v9Wd8wBhrjSqLHUEpdSDPJwDEK8+cpJZIw8/8ghLcitZ0DVEdQKMH8+c3UIeqNsOLEzbmsqjYwSbIXe1NQEj7Sp7nelE/mqHst/GgBUWTHIYP3i8Fa8fmcIli1p7rOt7ISLxaEuVB/udLbNVhbOGx6T5r7MXR15vt1MPe/oMeFRwWQKnTwdGVo8A6j9zldLNcOTxjdseR9vvX0Xk8ecwqnoUKkMS5bFlPToSzrU4G0cQV5+wJiDHXA2MfRcA0LZ2h+8K82yGS08iiuUrr0TEWPwSQgbaXNtfwZNH2iOt+nbYZApdQ0WkwkEeLV1M9YVddjhiRaDpHqay2tgQmzzEwk9z5yJmfJbOptCek6hGzVQeCEweO0cMdigPXefI4zMjAs7HbMUuwu4LMp9HAhXA0KEYNyp4NJUMJBKG1lKiouQFIojP40uEkCZCSAOA8ymlWwHsSQi5nhDyYembCFBKn6OUTqOU7kEpvaEU5zjwQCDz+ju4BtfiqpH3oU6SCieZTcpXPe+YBvx+NfAxS07IP9Q9UR6+GUSphuiwkUA0apCHU3k0jPKOa5fBjLZqr2c9v8soKIPRbOFWIp3Kn9RRZtJzgWKvEXtZ7xzkselQ9+6y2a8kVDeTcZJHetBnwFfmo+IKFm477Uu/B749FZj9GFNIgzYBNIzURnGZtxvxkaPxBTyKw4etxAEHcPmVBLLUNxwKnQDp0SNwz5/Y7CPz2WEghKAy5L4fnSSDzm3M9DKridmI1sa73ORhKI/KWMya4Z790mVY+UuPNM8Actysf/VqoXDkscciEmFpQUicIHzGKdZH/EDelZMrG8cKaKqhIuwkj9Yu9j2X8qDEikDTuS/wk+IoqbQmYxVR4T5XViK2h1E0LNGB9oyMPEzlgcDk0TKsykEeb77JkcfGbUxuTpjgUB58OhPMftihPGRmqw5UA3V1OPzAwjPy8iB1tQ6VFBi+zNc9BLFt/BzASQD2ATCZEPISgH8CiAH4XtFbVE7Mno1r8Qvc3HqZVOaZ+W5MjDJzWr1yI9AyDfg3e5h55SFZ3+WAv9nKZ1ashxEZORoApOSBiW4TkB8ss9VgdpyuhOEzMHwSXakUmvNE0g4fXJv/RIRiyhA7GWF4qo8DHgA0SReVheoaNvxQCNCpjjTJAZNex6DhrFpbJ5qAemMRHgEwcjEAILvTv3YFAMRHjcKjOB//m3yRdb/eufgdHLuHs9AXHn4amRCwbASB3u6UrhUS8uiIAh1b2cx2r23sd17budnlqDYj3apiMQeZphpmebY5Jwzc3/8+8Mkn9vvt/2NzsHc75iM8tNbaHgqxgfNvH/4NH2/PZ6eEVHm0dbH+H85JlIdBHjlu5SZfXGrpvZcCO9napXDY7XSJzmF+kFS6C7uyAnkQCmg5gOSg60DGjJTiIFMFO2vjDvLYd1/7Wa/MAJgyBQiHHeNvLMpNak76tmMiUVPDglD++U97l45wLaBpuOLygnPRui5AG9QzAioWglxJB6V0AaW0CcAvAOwNYDal9CpK6ZulbV4vo64OGDeOFSwwMwFyMGO/TQzKGrNz4jRNmbboRYuAxNITfE/pHznhE8Sma4gNYwNfVaTKlUQwNWmy73lFWA7zStagTmPmFQmzLpLIJM00QZ5YddO9Ac5EMW4Q53Tfw5/kqCzkR0Ieyx9l6dU+/dQulhXP2MWDXKvtjfBdms2f4rtivNHetXaxrYPHHYyJQ4TgAaohowFb6sIQ04ZYKcI5dEaAjq1Meexl/LZrd66VKA/WDypjcQSqsAQgJwnd2sX9BMntE63XfOaEUAi44X834JJnLsH3X/xu/hNJlEdbgpFHRGwD5cxWGfszvj7I1jdOBlaebbVFRGz23gCYw7w15SSP4UZKdy3CFHDTVrcSHjHCtQnNNZqDPL7/fWo7zLOwImb49jjW+8RbHWNAKARceCGLlDPRqbH7H48Ds893+soKASEE2hEShV4GBCGPkcY6iiMBjADQQCltLW2zyggzd8LSpa6PEtkEcMaXrfeDYNiro05535npREcHm8HktvkvhvdMeQLv+g7sQw2xkWy5i0x5JKi35JHJakt5hLLAoEHoMmy4EWMk60qnHFaAKXu57a6xGPDv65f5NBoAoRgzyF6mkxnrrzyorKCNhDxMPPOMnU47ngViUWZmdJMHG1ioxAQmomLkaBaC09TEVi2bhxCfHpJDJgRsq3G2ef16QOsYAxGdUaCjkflmJrYC1SSGtlSbI0MtYCuP6lhFoAJLAJDLuMlDNlGZvlerizxeWmvEVRN/fx0AFm0lKI9dCaOMrER5NHY2glIKyi2QS3ostZGSx54spD5Nc85INQBHT5qPHx78Q1QZNTPam9wHrqpitVN4NGid0LhrjVSw78UQZsJ7+nRXeyJ88sOQ7vB5yKLUOomtFg4YO8+9Q0AQAmiHH9Lt7xcTQXriNQD2AnAd2ErzOYSQl4107BeUtHXlgJkTYskS10eJTAKos2PLB8EYkCJOx2VnutOVldcL3Y7Z1jVER3iTR5ekdCkOvQmnf2W9Q06bsBzm6Q5g9mx0Gc+2mRY7kUnBWn959vkYOsrtPQ+HgdB2eRirBaKz1CQGvFbt2pAwqA95AECKI49ozEkeVlZWMzIsgMO8Ml4BTJzI3nC1HWT3LqMB2+JZ8Kpx0iTg9YcPdO3bWVuJDmOGW50hmFjLzrG1o8Gxn2lBrYrF87ZXMxzRsjrYfHsrR7D79O1ffeggDx0ZfLTVyHbsEW3lgK4hpsWkyiMs8Xm0JltZYAFH2oWQR3Qmm9ylQrqd7t7AqJpRuOX4WxCPsd++Y6c7+i8cBhoanAP8pvbNCHFVNc3KmZVmGmIJeRBxUuMRsXbXZQsAAHfO+5u1bcZwPvY/AM74qn0aAmi1EtsbgGOOKX44rh+CRFvdSSm9glJ6JKV0CIBJYGlKmsHyTfUv5FMetTZ51MBY3inkdOrMdAYOq/ZceIj8yiM6mplNpMojIyGPeBu+d+16HHOM+6PBxork1mQrMGeORR4RY2RJpFO2bXrIGqn/TdMCkAcoBsVsE44rLblr78LI4777gGQzW9kd1wliRj6ndqNIV228lu3o5dyXRIzFoxHGAACwzr7/7ntHkAkBbVEaSCF0HrwfzMXa1fsfgvFDmQlvW9dmx34meQRRHhGNdbycJCkh395MBxuAYoN2OcijLdWKnKlaAyiPECIghDj6e3unoTxcREfQlmpjoey5/OQhI2dt7DhoOsuG0twpjzoyHdsd1B3cEgq5fcebdm0CrbT3TRrFwirSxkXNnGl917oSjn0IiKPf8PtdPGcBMgjj2Fny5JOBMPm/jrey3+XXvwb2Hx8wQ0ORULD3hlLaYEQ/3Uwp/VIpGlVO0Nmz8af9gcfa3rG2bWrbhC3tW5jyqLHXJ5q1j8WBqCPdEZg8/MxWf/+7zxd1DbGxLFJJSh7pFMieTzq/E8paPgARQyvZwq/mrmZQjjyiRjW3RCZlJxHUMohI2h0KAdr2POs3iZM8CAFu+ZmPTJNwR0hiagrXMOfogQcCyU1sgI+TiBUZZYbR2uThsSYl7m5LOEyAyYYPiSMP10NMQ0jHwmjXMv4ZkQ107DsHHSNZWF/193/M1r4A2NzhDDM2yaMmbkciecEcGHcsdC+45GNA9AwbYfVwh+M62jOt/AXlvYYQZSd0mq0M5cGlSAFYBt1kNsnULTcBKER5gBDEjMqVu9Lt4kcAbH9EO9wzdPOY/MRsY9tG5Cpss5JpDhvcafxgBnl4LaB8Za9b8dyFz0k/Q3Mzwsix5Hh+1+UHzQ5XJ0ROHqEQoL33jvuDEqKHrv/+hzcqtuPyk4EvHLEdufZdaOpswvjbxuPguw5mTrQQxeG3n4fNB51th8wJs8HOtFt5aGG5D8LLbJVOA6ee6tNQqiFqkEdVtMq91iSVZOtSeJCcJ3lURaoQD8eRyCbQvOd4UALEcgRh02GeTtnrBUIZaSQMIUBo62bXdqHhDvIAgHC9d1ixTHloGbfyMAfDSARIbmFO6LgWcxUPyqs8JKQSCsFWHpzTXEYemZHD0J7ugG+wg4FOmkbHGJZOp3r8FIwfzJI4rGtzriDO5dixauKVec1WEWkhIgZ+vYduKJMs6XIpDwA4Z+Y5gZQHgZs8Ogw20KpqHftGNdb3mruau222AoCYXxQi/MlD5o/YtGsT9LitPHYk2ESkvpMCY8dauYq8UrfMT47E56aeKG+Maevl0iYFIY9qPqCKW3zrSR4vPg9tpdtaUkoo8hDwyS57Ztmw6HW8uu5VAGx28mkLC/WsG57A6HVv2xEawiyzM9PpivTVwvIH0asjmTNIK1XFKGFJzdrjEBk5FoChPEwCMzLItiXaJSYO4kkehBAMq2QdfMMYljahMk0RNmy7yUzaqTwikqeQUoQ2b3Jvd5xId5GHn9+HQgdGLAYATJ1qnt5NHmbUVCQCJI0ssfFIhWutzODYYOsapJCQiqYhmNmKEmRGDGUmsgDKozPTaUXmVUerLfJ4fZPTTEF1dqzqeIVnCnYTfj4kkzxyObC68ySHlO4kj11Ggr/Tpp0WzGwlUR4Zo+9HqpyLpaIhdi+aOpu6rzzgXU3QHHDNqMAL8IhrHxd5EB0d6Q6kufCpHV0GeSRgqQ6xPY7jbHWaah1zNjO+vcCce0OGcG8CKA/y0ovdW//RAyjyEMBH5axb/jZaEnY44OJtiwEAFTQMbN9u30RhkJaZrcRMC9Z2fhJVtRWY8U+8+j/bGf3aa+xhiIx057P53//YQ1QdrbYHFWPw25XsdJs4aMiTPACwBIsANuhsAKnMAGGddUgHeYQyiEiUB5qaoHXtcm/nQairUI2f6Q4A8I0Dcc6dV2K//YzTS8JrzaipSARINjLTWSxa6ao81x3loWkIbLbKDK9nfSiIz0MgD9NstXbXKueOxr2tjEXzKo9wxPu8JnlYaei1NJLZpOP378wyU9DU+ql4+Jz8IaUhY8Ggo78bbdQGOfPFmUTe1NXk8HlwAWzOY3tcSoVHoaPRbNmTo9RA/mMa65g4O6z5zNd3wZM8DB86g5D73EEepvLgzFZBTNqFkkdo/DhoJ3kvC/D1n3YTijwE8OSxc91KR0TH+rb1AICKTuY5Dg1mM+ihFSPtA+hEaraKRIS7N5pFYfz859y2qibgC5/HEXvbnSUcZqGFVZIHxlyK4lAexow6l9PdA1g+8jCVh5HQrzIDhI2oLSd5ZOXK45NPHPHyMmihEELE2S5/5UGBcBpDRrZb+8nIw0QkAiSb2EwwHq/2MVt5KA+JInGYrdats55Et/IIITOs3iCEAD6PdIdFHlWRKkt58IMFO65BHtFYYJ+HDKYatgIftJSLPBKGs3hY5TAcNiF/SCih7MuO/v4yy38ernGSh6k8mruaHcrDq6aKV7+ojMujjaZMkW52QLxnxFwEy92u9VtXAgBGtwPYx07OyH935kzgv1e+iNWY5lIeDkjMVkHSTDmK0YWcz5SUPH70Q2iHe6//KEFeREUeInjyaN3ymSOWfGObYUtvYw+YNoTd4cERPsuoJjVbRcXB9rBf4cyHz8Xee3Pb9DAiOUDb7o6aqIi4B31zNsHIwxxZjcGPhqTkURH2zq1jKo/1resBAFVpIGyMNKLDPCqb4a5ZE4g8RPgl9Df9NhEtYg1yxGdtRjjMRVtVDXaZrfIrDw+zVV0dWyDT0WGZItwPMUFm6BAWlRfAbNXc1Qyd6ohpMUS0CEbVjGLJMIXBwpzJV8Zi+c1WPuQhUx6JbMJBHsksc3YPqxrme19MSMlj+1xg+yxotU5TTSTE+nBTZ5M0xYwIT+VRKc+Ue/TReQ/puibzvVlyAABu/PB3AIyFmwcfLG1PKAQcdywwDWv8zVYS5RGEPMJh4Pg7zwf+z2n683OY+/lSSpAXUZGHiLaUHW3T2rQJOxO28jDJo6KVSftQXS0AoZSprmFz+2a8/NmrjuPGXI5MgiyYsddyjg35FNVpSGcyFRH3oG920ppojUt5gGrdVh4meVRmgEiSmdCSmYzDYe4iQwD45BOH3XXmUW4Hnhbq3sKWqBYNrjxaGPnGa+q64fPwMFsBtunKcJpLlUd9HYvKC2C2auxk7TTzioVDYYwdNNa9o1H/fMTgwT1SHkHMVlmkENWiqInWBHLsSskDADYc6SKPSfNYfpSmrqZAv4/X+Stj7vQcjz7qPwkxIZpvzHMks275c+KOOoec4dujaQBGGhYHL+VBqdTnEWQgD4WAkcOiQEWrYzsh8t8lFPJWarfc0oP1ZH5tLP4hd2/wyqMt3Y7WXXZODjPVekUzIxitvhaAkLWUanjmk2fwvee/7zhuPOZO1ZAyspa+/z5wwVfbgVMv8SQP0XYP2A+sw2yVR3l4ORsBYEwNW3S4poX5VyozQMTIU5TKZPIrD8FsNXJyM/BN50IOTZNHaXnDUB6hiPUAEN2HPGgayXZG+PGaum5EW7m3Ww+r4DR3PZBjPkB6aK2xwj3/SGam6jDJA4CcPJKszROH1+ePtuoheYDoGFY5DISQQIMxjLBZl029aQa0OFMIhx4KvPEGsNeRzM7a1NUEnP3FvIf2JA/OhHvyN6/F977HatIEgSsA0bjGdMZ53/97P1BzwqmOzumKtjKdLF7Ko62NzSxrauyawghmQgqF7Mmc2F7Z9/3I44ILFHn0Chxmqziwc4c79LRiO3OohQzyEJWH468Bl48glLVyMM2cCVx98wagZjtqUgC2uNdKyAZ9sxNpIa4uhTn46TLlQeBX9HHcYLbocHXzagBAZZYgkpCQR8gj2mrNGofy0DTimsmHu6k8IlqEC1DwNr2FtzVYa1QqY9Uu0rUivQrweQRWHoM3MuUR0GxlhiHz5CGbJJjkMaQujDDxN/cUSh4LtyzEi+uesXciumW+DKQ8jFXYrgGtc7gViVVfDxxxBDDI8FW0JFqAMQsx95fn+x7b0+fBkcfccW/jd78LPjiK7bRS+AulEya1go26kn2t1/X1zL60c6cjZMwiDzOfj5APJQh5EOJNHjLl4kceJUioy85ZmsPuvhDJo7XdvYq1orEFIARaPbNHOhx+5iAuDNyuQjJTn3MUjTJXQNd4KI+o5u4BfCck5nk1f+XhBzNhoTmoVVYMQoSykySzTod5EJ9HOERcg3G4QJ+HmSKDN1vFde8KbGTjBps8IpUOs1VMi9nmv0J9HkB+8tDDyNQNYsojYA4qwEketx5/qzNxJGCRR00NUKG5TTY8CnKYh1NYtG0Rrn/LLiFrKg8g4GI2Yw2Ka0CkGsKEdXrz9zNT4JjRTLE8BcuCKI/aZu+CWDK4zFZG5xPrxI876yLg+OM92xMKGf+Zpisu4so6h8RZDgQ3W/E54HgUqjxk5SWKAUUeAlzKw1g0xWNQggKTJiEUNRZa8eONLicP/qE+5yuNQDhjma0AOxOvt9nKPSo4FI/oMM9UAhuOdH4hz4BmRfsYGFw/ChGjp6bSObvThnJy8kgmERpqpyLXtJArckgrVD9PZ6vkebOVTtjg8eMDX3WWCAaADTZ5VIQrHIotqkXtgIECfB7WoCGQR0bcVdeQjoWtxIxBwZPH3JFzsfH7G507cORRE671PVahysPxF3Aoj0BmKy/y0DWEjAWE5u9nXqfpR5RlKeARiDy27pTv5NVccf2VsY6Jr7tTHa1G7G93u34Al88DYCWHAbnfw1Qe3SSPQ8a5o90KNVv5kUpPochDgMPnEQNadXcCwJo0gBkzrJsiHcSpaLayX5sDr2m2AoB2I9VCTQpy5RF2m60WL7ZnOS7l8fTfXPvnI4+R1SMRDtlPdO2oyRZ5JFOmjSwNEG/zkzZ1svU6HJaZrQrocmdeCHyOpQXno63SMGa0OxrdA8zGjUgY+1VGnOs8YuGYlT3YU3loGYSjzs+8lEdKTB9GNSuVfRCfhwmePKTQWeepqQGyYrJBAX7k8ZGR79BFHmHuQohuBRU4fttRCyFF1lt5mGYr8/ezyMMIf4/6rIZ3nZ8Dv8i0bmNhVfXcZiuDPLhSv3Vx+VRdusJcQh4u5SGYrYKSx+S6ybj/jPvx3AXO1CeFmK0cIb9FhiIPAW1JO9qqpQJoDbsHmZoUgH32kZNHAOVh1sjglYcp5WuTCKw8HnwQuOMOtlZET5vmGKMxLVPdF5cnS6oW0iynOQAMnjwDUeNps5L0GscnHkQUnckVetJCbrOVpLiT5wx374eAKBuMebNV2lyE1iwhjw3+ZqupQ8xl6vISqwhlcelf7nJssh7K8ePZU7ppE5BOu8lDD1uTD4Lg07285GEgHkfeao5+Cy5vuomZ5q3syFEjN5SgPMz2OO7L/J8jcrkklbin8ghbykMkDzP8vbtmKzMPGwCM2NTivcpQAlm0VTgUBuWKU9VVFJk8BOXh5fOo4qyx5vG/tPeX8LmpzvyzhZBHdbCu1S0o8uCQ03OOErKbagn0EFAdrnJ02Jo0gH33lSZZmzvCeMB8yCMWYXeZ93lYq1oT8HCYyx2lt94KXH89t0E2o57zEDB0BaKH/EV6DB6m0xwABo+ahEiMzfJTXVHH8WXFhgCBPEIhl/KQrfMIAt5slc6wAUtr3eGowwDARR682SoWjmFktWGjrnauCrYQyiAyahXiZ33L2mQNotEoKxam68DGje7Fbbpm+a5IAY9WEPIgWhaEAJ3eZcsB5HeOtrfbCsSspiiSh+mbcNyqSBcycXe/pFkAlNr17ce8z/7qGjQP8jDhDl93wsvc4iCPDrAKYBz++uW3PY8pDtyEEHY8LrFo0ZSHh9nKizymcvM9T5NdpbfZSlr/JP9ymm5DkQeHdiFL58446wV1iDsiHwalAOy3n7Rz33f6g1h86WJf8oga5MGbrax8OikNaG11jRIy5QFIZiEyW/5BtwFXzEJa8vCL4P0etRV1iBodP/3CjY7jb9sqn+JG59h5G6JRd3u6Sx688jAr62nIIpQR/Aui8uDMVlEtCkII1n5nLR784u/lJwpl8eDSB5GMNsg/5xIkysxWtvIIfp1VEe8AAKtZGrvR+dJMLFjg/3kmA6vWzIzJxiAZQHloNTvlEWq6BmzdaleZrDdq3XLKQ/R5mIh2U3nwz+KITjjr6wK4ZOpruA3yKogy5TG0cqijEmdPlYeFAsxW//qXM+WJqMaffhrYe2/g7rsLUx6lirQCFHk4YD741uzUQG2COmY7gypqgXHjpJ27KjwIs4fPloTq2q9N5cGbraxMnuasZ7MzRNgRqnvaxdbLrCg0JA/4z478KQDgl/N/6W6wgCl1tnIYN2gctNHO38Iq36rLpzmR6bbPIxaHy2EeklUGDABHqK4BDTmEUoJPineYC4kRzdeT6ibhzPmTIYWWYekzajwWfnF+Dz+zVZBQXRNBlEfIIyuziHylghMJe+Z66X7fwP1n3O9WHjG38ojWtMlVLQ0BK1bYVSbNkgVUsxYQeimPiqhzAmLWYTPhRR6Hjj8Uc0fOxc/SB7OJnEAe2LzZLtQmwK08wIqTFag8/Bzm+ZSHOPgPGQKceaa7HDCPU05hPs7p04FDJFljvMjDNT4UEYo8OJgP/pCKIZZ0B4C61pSDPEbO2J+F6kpuVi7HfAfxsHM2KVMevNnKIg8j0sVFHrzDnCs+5ZqFSB7ws2adgU+//Sn+77D/czdYwOzhs63XewzZA6HxQtio4W+45ucERx8NXHQR99n++yNazZmJYnDn5emB2Uq052vIQRPJI5NB1yA2EMjMViYqK4EDr/6p+0Qm+Q7eIG+IWXP9008l5KFhV7o4Po8GQfiYyqOn4MlD00JMaXooDx7RmnZp36JUAz7+2CaPamMQ1QOQR8w5LebNNoC/z2PRpYtw3SRjEiUhjyjkCbNkyqM75NETn4f4zJr78+OJ32MyY4a8bbLxyNVHi4j8NTgHEEzyGBQbhNp4rWXGqt3RiUjGvuOV81n8t+wGmx2jMlwN3qDCr/MwS7tKzVa1owCsdI0ejuimsD1gNonBJiH3IBMKMSIIglOmnYLjJh/Hci1Vj0JokDCDTlchHApjwgSCV15h11u3/E0c+8GNwGGHOa4zHiOAYFUqNLeVCd5sZR0LOYSSbidAV00cQBKVkUp0hG1nqrjQUqOSVC2mD6W6Cf/vxkZMFAtem1lWly1DSvw6tX0ePVUeY8YAoSHroLcwM5lXSn8RsRhFKuV9bp48QiGjdj1PCpRYE6eqKuD001lKr+cjYSDk9tZTqgHvvYddxkQ/EtuBDAB0DUUm6fR5WJFuBuLCgkhxLVTeEFPTziMlD7mylCmPungdQM3EabmSm61EkjTHjKDkIYNXzitFHr0EM9JqcGwwOuOd2LRrEwAWAVXXYEdh4bjjAMhvlkUeWg1auO1iqC7JEORoDjk9By2k2cpjqOFzEKeePCLu8GET42vHQVglUFBHrIhU4L9fsutJuL6brUQkZF+MpgG3xn4K4E3g0Mvd5CGg28rDy2yVcA9oXZWsEbJoKx4jZq4Bqregemg7OtZPd3xGQPD/fjQYMfEJmW0os6VLkRLMLM5oq+DX6TlYadRacmmSxyOPAOf7LMxmWWK9yaOry0ke1dFq5+5Uc/g8/vMftnn872JS5aFDA955B52UnTca3sXIY8ee+PGP7PMAEoe5QB6ifT5vVzEX+axezabv5ixkwwZEME76FZnycCiNcLIw5TFiBDuvJb2Mc1DqqTwuu4z5ncyM2uaY4We2yodQSD4JU9FWvQRReZioSwIX3/YGjlgPPPPWBMs4m0958OAfjHCYWLNg0+9hRVuNNByygtnK0THC3uSx18hZrm3dHK8BeDjh+NXubW3Ae+8Zq5oOcVxnPO4+sdZdn0dIQh6EgnJTq9oK9hsmYuy8lZFKRyJIcbCqrNKB74/H9KsvdJ1vdM1oeaqQyZOZzWvLFgypFqZ1uYgddFHACnOvwUqL2v4rkzzOOw9YtMj7WPls3ImEPYCGQnCYZwEAetilEABDtYWyIIIZUocGbNmCzp3MTBSNupWged9iWoxlDTYgJukUlUfefjt8OFvI0NpqJyBsawN27EDEwxl/olDwLxQyyPuYq9mG434USHlYfTEcZuRAKcaPYT/+YYeBtSmbZe2LuRXWz35mvzdD/b3K3Mrw8svutonPx957A//4h/9xegJFHhy8yKO2agj22g68cS9w8vk/t+6sr/LwJQ97IMvkWM8xyWPIWEPTisqD19s+ykNWHrYnK0xlDzCvPPD886z3H3YYMGSIo9PL1nRokm2GkPOF1Gw1vB4614WvP4xlx+sKs9GxIlzBSvQaEJVHVIsCWg6dGfc6gUl1k+QNCYWAWYygb7lgEU6buwF/wBXsMz2CHVurgOXnFEQefF/jEamwbX7hsD1l5nLsobIS+MEP7PfZjP95pWYrHnpY7vPQomxxaNxph9RJGBRAZ5LdnPAgt6/BSmhJiDOPl9ZD5UGIrT5M05WxgDM8aqj0K780YkZuuIH9vekmg7wPvwn44UjggD8XpjwAy3S1+rElaGgwAvJMM9aIEXkuwib8QpTHMccARx1lv9d153fOPZc52OdJluYUC4o8OHgqjwu/waZ8N9/s8BD7KY+45u0w1zQ4lEcym0Q6l0ZMiyE+zhi0BPIgvHlGLBbEIS4x4xddeXCr0HHffezvmWe69pOtQteIuzFjxrDccn7gV5hbxxrtfDAH72Tx/V2G36cyUukYrESfh0mCnWn3bHlSrQd5AMAclil4VMMCPHnEb3E8DDNfLoIN170CPP44Ml3y8Fsz1970w5ZZ27xmurFKe6DWuGJi/ET2s89YtwyKZNJJHpWRShDebpWNudUI7N8uFHOaCXVoSCOKHMKIIA29Su5zM8HfD1F5FEwegCd5aKPcg/bFFwMVRkTuT34CtLQAF15oOMwBoIaFqhXk8wAs8oi3bMEYc42taTkYK8mSLMAOYPA4fp7vAcxqxn+/VPmseCjy4OBFHiOmzGXG5quucuhJf5+Ht/LgySOdS1srbgfHB9udTTBbOdZ9eGWEhXxRUE/Iw5XQEUCk3RhAPv4YeOEFdtIvfcm1n5Q8PBpTW+u/OlpqtpowFpQb+FJ6GthjD3RlmTITyUOsnW7eA35hqAmr7ocMBx7I/r7xBrBwISIw7kcuaqUS8cKXvwysXAl88+b/WdtGVY+S7hurtO8zb+0TlUc4DJzxq9uBiw53fF+b+ZTrmOm0kzxCJORQZ8hWSJWHqZRJ1KnSdJ3gjD1ZFuaqSoqs5pNYEv4ZhAt2mANu8jAWDIbHuMljkjAfMAdYkSwKVh6y1Ozm5M9iEzdEsizU58GTx9atzt9L9twWG32aPAgh1xJCNhNCFhv/Tirl+Xjy4DvQiCq59PQjjwpBefA3Mxy2JXs6l7Yc9bXxWpalMxRiAftc3hPSzsWteyX1Q/GVh4yMIq3tzFZiTqMvvZSlpxYQlvxAMrOV/Zl3O6Rmq1l7OsxWaQ2gBx+ELiO/VEXEORCKhGD6bsyklAAwrIpFxpw/x8crfcwx7O8TTwDvvIOoZjzFeYgDYNe4557AxCH2oCLzMQBARbV9n7evH2K95vuSmdJi7kGtwIS3EKuy1YoZKssjlXKSByD4PbJxJ5mY5zSIVo+610+8sGoia0tdDFnqDu/xIg9ReVQJp+2W8ljGFJ02eYJr18Ee8wGRLAryeQDyiCtz8udDHi4lXaDy4J3/55zj/H4pV5ab6NPkYeB3lNK5xr/n8u/efZhVBAfHBjts3iOq5eThb7Zy1hz3NFtlU7byiA1mPWrkSNYzuDQlpJ2L9upF5SElDx3A734HrFjBRsJf/EL63coK94nDPg5zP/KQRltNmQRw6ialAe3zDwUFRVWEhRQ7yCMukIdhtuLX2xw58UgsvnSxNKOphcmTHRXmIgcbhuVc/umeeS9OnX4qrjnyGvz3wv967lvJmYD0nH2d/D0xf5P6Skbep97wO+szIqk6KCoPwEle4VyNq8Y8YPfXXKTN9Rnfrgzc/jhP5SH4PLpFHma47vLl7O+SJQCA8DR3qG6NnKNtsxVYlF19hXsiJLZHZrZypBUKQB6i8ugJecyerZRHWcErj+n1dujmlCFTpPv7KQ8/n0c47DRbmaRlmcpMff3ZZ9Z3SBv30Pooj2KTh0zJxCZMZgbkn/wEePNNZnPicPPNzGG3z/6SWahPY3zJQ2a2imqgQ+wHPRXTsPO4wwDYs0e+ZruYBkRWYCuuVWDvkXu7tjtACHDllXbbvv4V9iKXX3mYlx8iIVx71LU4bg/vaAFZqDPAwi8feICltDBhqqroONuXAonykJEHb87TdHlsp0Uemlt5WN/VgKqYu8ME9XkI3ShYv509mzkyVq9mg/fKlWwB7xS3z8qLPPiMEvWV9Z6lkj3JY5wRFryRC5IP4PMQlQf/PsjaJ3HNSm+Tx+6wzuMKQsiXASwE8ENKaWEJ/AsATx4HjT0It51wG/Ydta9n6VZZ5zYjJ8RoK/5mappt73X5PAC2iujtt5n99uijAQBkVzDl0Rtmq4raYcBdd7k/MHDVVezvyib3YBryWXmdz2wlk/n6sOGAEaWZOuwgtEbYE2WaIvjKiV1WunSGiKTAVmBccgkLFY1EEJ1/CvBVAKnavF8r5F5UxrxHgAuF6GJTPfAmuKBmKwd55OSOfksleGUjBrsfd53xV5x1o3u72E7xvIDbyRvI5xGNAgcdBLz2GgudymSAmTOh1VS6dvUiD75Nohri4UkeZsoabrLXHZ9HT5SH+P0BYbYihLxMCFkm+Xc6gD8D2APAXABbAdzqcYxLCCELCSELm1xLroODJw9CCL570Hdx+ITDPff3Ux51caf09VUeps8jVst2ME0ia9bYX+qB8pARSlBIySNS4d4oAV8bxATxma/4LWiSmq00gEbsBqbnH2HViuADHv5xzj+w/+j98bV9v+b4vmxSEKgAkrnjmWcCp5xSUPK5QsKmZbN4L5h+C2uFO4KbrfhBPORBHtZvFfYnj0MnHCTdLrYTcJsRRfIITLTHHsv+/uEP7O/nPicNvvAiDx58vjkRnuRhWgrWr7cHgG74PHriMAcGoNmKUnospXS25N+TlNLtlNIcpVQH8DcAB3gc405K6TxK6bxhwmrOQsCTRxD4+TxqIrWO7X6hulLlAdipphMJkE4u46+P8mhpcb6PRKS+7MCQklE42KAmndlLZsMmvByagIfDXHPOvlK5tJXmhbdjnzvrXHzwjQ9cCS9l5JEva60M+cjjgQfs14Uoj+q4TdIPPumTcQC2OciRGVr3Vx4mUQ6KDQLm3g0AGH/sM67vANxv5RMmrmlycuS38f4EMYCh2+Tx5S87dz7tNGk7/Mjj3FnnAgAu3e9Sz334UzgG/qoq5qdMp5npLJVisbOhkO86j5NPZn8PMvi2p8rDkchyIJCHHwghfAzjmQCWee1bDJi+h6Dk4ac8RlaNdmwXycMRbcU56gG4lce6dcKJvclDzKq6dKl3+4NAqjzCPVAektmwCbHqGe+4lfo8RPLIprC5nc34Rtc4f38ZvMyRhSKfWuEf5ELIoyZmm15OOs4/bbtpehGVx+23s9dmPkep8ogOAk77OvDdiZh21IfyawhIHrIZP3/Nw6vsPE+D44Ox7772Z93yeQDMr/DTn7IvXHABcPjhBSuPe06/Bw+d9RB+dsTPPPfxjLYCnKYrY60JJk70jT//7W+Bv/0NeMqIqO5JehKxTQPCbJUHvyaELCWELAEwH8D3S3kya5U3N2v1g5/yEFcY86YjL7OVS3l89hl70pcvx0W4BwCwz/y1eOic+z3bJM5G/GbzQSAzeQU1WzlWohuQ2eFNiG3lv+9ptnIojxS2tLOIF74ioheKRR55z9NN8jj+YNvZ6hU+asI0B/E+D+gavvMdpka/8x22ydNsFaJA3QbpAkEguM8jn/Iw66MDbLL04ov2Z/G4vYiPb18gXHcdWwv10EOeGa/9yKMyUokL5lwgT0kjaY8neaxdy5z3gB1G7IGqKuDrX7dTXxWSnkTWhgFntvIDpfRLlNI5lNK9KKWnUUo9iiz0HF2ZLiSzScS0GCojbmebDH7KQ0y7zA/CYqiu6ci1ooFqalj4XzLJVMfHH2MfLEbL96/Hh69MxgV7ea9BEKNme1oMxjWDqd6CuBbMbCVTHmJtdx6HHeZ8z5u9vBzmPHmkc2lsaGOp1McOyr+yt7fIgx+0CvF5fP6sKO64wz+XlQlzbQa/4DFSx5L11dXZg0kq5cxtBTiVtpfqlvk8TjnFuY+myQct/pr3HbUvNKLhtOmnoSJSgaFcFpFw2EkeBafV4R6y7vo8/OBptgIcqfqtNSd8dacAKNRsJe6jyKNMsFKiV9Y7InT84Kc8RGeWqDz4aKuuLCMPB2nttx/7u2ABW8kNoO6g6XlnJFOm2IlfgZ53Igd5hLuArx0aXHlIfB5+Zqsf/Uj4Pq88gpitcimsal4FAJg+NP+D21vkwSuqQmbThACXXw7MnZt/X9MPlcqm8MBza4DZD2P0uTdZn5v9IJ/D3Et5yMxWsmihaJR1169+1bndxMxhM9F8VTP+84X/uM5RVeWccRc9rU4PJ1K+ysNM1b94MbCK9cF8ykNEoWYrRR59BM1dLN6TL/qUD37Kw488xPQkpvJwkMf++7O/773H/gFwGIh9wPsOiqo8jrweqFtfMp9HNGpfNgBHvL0W0lyzvXjcSR6JTAKrm5nJYM+he+ZtXzmUR+BorgJhmpUS2QRmzm0Hzvki4rWt9ufGfcwXqptXeXBmK68V0nvtZaX/cpzHRG281jFBe/114LnnGHns2OH9vULgl+qmu/AljwOMWJ4FC+znde8864UEFFN59IbPY3dY59ErSOfSGD94PMYNktcBkMFPeeQ1W4XykIdZa9L0eE6caEvjPOA7TlHJg7CLEkMsvSDzefiZrQD/aCcZefCD8actnyKRTWBk9UjPTLU8HOQRTgDZCgd5FQv8PUh7+5t7BC2kIRKKIKNnkMgkrG0mTAJrb8+jPDxSpVi+gHoWxDFihH96DTFAxA9HHinfXmzl0VP4mq0mTACGDmWp4Zub2YNTYEpbpTx2Uxw49kBs+N4GPHOBPFRRBlkHNdNRicpDtOWaD2Mql5KTx5FHwmEQPvHEwNNW3xlSgXA4zI1stXzEjB+kK3Vltc85+JGHSISxmPMhWd7EUlRMHSKUavOAgzwun4H/95sN+Na3An21IPC3jc9vWWyYpivT78HXzhhixIC0tPiTh1ceN+u3mvUPjPv69/DGG8UjDy/0pO/ybfvWt+yo956Ab4+rbYQA8+fb7w86qODpf6EOc0UeuzFknftrxjo0kTwclQSjAcxWkQhLOGi+vvzyYjW7IDiVB7uooKa9EAm58iTRPMrjD39gD8Wf/uT+TKY8ZMpq/ODxgdrnII+6DfjCl9tLYu5w1Dgpoda3yMNIMc+bDf3Igy9RPLnOnRMK4M1WOYw68F1Mn+5PHt2NMONRLOVx5JGBRbsv8hLiN79pv77kkoKPX6jyENvAf0eZrfo4vG7wa6/Z5TtNiA9WXvIAWOjUfvsxx9ssZ4XAF18ETjihJ60PBpnZymt2KkMkFIEjuFP3J4+DD2amHU0Dfv4b4VgCUXiRR5BIK8Dt8yiVD4QQFkH6wQfAoYeW5BQAbPIww3V55WeSx6pVbvIYO2gsZg6biabOJswYNkN6bP63MfvpOMHCW2zlUSzyKBZh572m+fOBV19l2SBOP73g4/sqGwnKrTwUefQA/M2Kx1lkLWClo3JAXP0pC9V1RTFpmrTIEuCMqColePL40WE/xNhjp/hnnBUQDoWRqtkMtBvrLgJU2PN6cPKZrUz0RfK44AI7g32pEMRsBdiRpHyffP/r7yOn5zzD1PmcT2ZI+VVXseUVJopNHj0JLuAJo6drnUyIKYak4E1XBYI/5u5AHsps1QM4soXmKTQv3lh+hbmn8gh4PBHdSbHhBZ48JtdPwHcO/E7gUGbACNf96lH2hjw+Dx5mkSRzUAxqtupr5NFbEM1WvPKQDSZitlu/QAj+tzHXlFRV2XWxAO/Bq7vkIQadFAL+nMWqqlcMQvRDMZWHWmHex1HIzfJSHn2dPHiHeXfMCOFQGKi3vZX5fB48bjzmRkyrn4a7TmMZfGXKY3cgDzHtRqngpzwAZ/gsUNjMXma2Arxny/x96a75SazvUQj4dvGqqycoVBkUit3NbKXIowfwzLIpgZfPI5FNIJll9q6gCQfF45USPZ1BiuG6NBe8y50y7RSsvmI1LpjD7D3FNluJbSs2efz618D777tzdpUKZgSfORkR19n8/OfO/QsZ1B3Kg6uL4jXgdbffmFafgw/u2Ypw/tqKRd6BzFY9wO5mtlI+jx6Aj6jKt57CZbYyHnQzKWJFuEJawS3I8USYNUWKAb6DdseM4FooSLtvyBZ/Y0IkdaBD4cChxKVWHnPm2GvHegNm+03yEEOlxTxlhZAHn/OJVx5e5NFdE88//wk88wwrq9pTHH00C40uhc+jLygP0VSuzFa7Efjs7/nSnothdOaDbqZjL8RkBfh3rox30t0eobKwJgJwk0chZivXsSRTHTM0GtNYatKhlUMDk3CpyaO31KEJS80aiwTF374n5FGo8ujuQDtkCMuw3p2+JuLll4F33y3eqv5Sk0ehyuOmm4B99gEeecT9HWW26uOYMQN44QVgwwZvP8O4ccA773ibrXYH8nj1VeCHPwROPbXw74r5rWiueMoDYGsn164F8IWzADjLnOYDPyCGQ+GClF8Q9DZ5mEEYZq60YpIHH23F91V+xXyx13n0FIQUNx1Mqc1WhSqPceOAjz4CzjuPve/teh7KbNVDmGstxEWBJr75TWa/NatSAs5oq50JVvmuL5PH/Pndj0B0KY8Coq1EeJkGJ00CoDGb2sxhMwMfjyePUjjLy6U8vHweoimjkEG9vtKW1ma0FQD873/2PsUO1e1r6GvKQ4TX718qKPIoErzIQ/aA8okRTeURNFMtfwwvlMps1R24HOY98Hn4zfYeOPMB/P793+Pqw64OfLz+Th5itFVPlAefVcBL3RU7VLevoa/5PGTf/8Uv2H3tDbWnyKNIyEceohNbJA/ejhwEfp2jL5GHOPvNFdlsZeLCvS7EhXtdWNjxOJOaNIljD1HKVCQy5FMePSEPvnzsHnXyXB/FDtXta+jr5AG4I+pKiX5yW8uPfOQhRiqZ0SuJLHNuFmq2kuFLX2J/S5W5tTsQfR5ev1MQFHsw5n0chSx8lOHZZ1kq8p/+1N5WbuVRTLMVfx/5Wil33mnv09/NVvzvVYrU+qVeR1JsKOVRJHgNimYnmDABGDOG/QXcZpKeksellwK33cZeFys0sRhwK4/uH6tUtTCKgZNOYv+eftreVjaHeQmUBwB8eMmHaE+1O0xYEyfan/d3sxWPYi7ENVGKfFylxG7QxN0D+ZRHOMyissz3xSSPz38e+Mtf7PePPMLI5KabvL/TWxDNQT0hj90BxUyHXyhKabYCWAlZEbya6e/Kg0dPFLQXdjflocxWRUIQh7mm2bNnPvQR6B55nHIKq7z36KPO7bNmAW+95a4JXg5YA9jJlwHhpKWOuouTT+5xk0qKvkQeosO8J2Yrz3N6KIz+6PPgwa/xKhaK4fPoTSjlUSQceaSdrZSHVycohvJ4+mkmn/uyOccij/3/ionHvIR58z7r0fH68rUCfYs8ROUhxv4XY1D3Uh791WzV1AQkEj3Lu+UFpTwGKG69Vb7d6wEtltmqrw+mvKM1Gun5XMVcENWDzNdSEBTnh+zL5EGI03RVjL4TRHnsDgNhUAwd6q5jUizsbspDkUeRUFOTv44HDz5XEFCcaKu+CH4AK8ZaigsuABYsYNFNxYS0ZG430BfIw4QrrxicSqGUyqO/m61KAUUeAxheCwJlEB/0Qtd57C7gHebFWEtBCDBvnrMmfDGg0+J4QMtJHuKEREYevPIoRvu8lAf/O5QiMqk/QpmtBjBk5FFqs1VfR7GVR6nQH8hD/H1laoonj2JkXg3i2+jvEXbFAj8hUuQxwFAIeVSEnVPn/koeDp9HHySPujgrMzepdlJRjsf7EcpNHvnMVsXIf+RltuJRirDW/gg+xboijwGGQsgjokUcZpz+Sh5hYg9g4mrzvoBXvvwKTp9+Oh4484GiHK8vKY98ZqtiZF4N4hhXyiMY+PuxO/xmKlS3iJARhV9hpspIpVUMqt+SRx83W+0zah/857z/FO14fZ08+DoZxSCPIHb63WEg7GtIJMrdgvxQyqOIkJFHMum9P5/aur+SR6mTD/Y1lNVhruV3mPOmkWKYrYKQR18PJ++LUOQRAISQzxNClhNCdELIPOGzqwkhnxJCVhNCTihXG4OiYPKI9H/y6OvKo9joS8pDXGEOFN8p6xVhBQDXXsvq3fSFTAe7G3YH8ugLZqtlAM4C8Fd+IyFkJoDzAMwCMBrAy4SQaZTSPiuCZTOsVMp7f54wCqmAtzvBEarbB30exQY/gJYrJbt1fonyKHaFOb7PiyG511xT3HMNJMwMXtOsbCg7eVBKVwLSlNinA3iUUpoCsI4Q8imAAwC827stDI6emK2GVAwpQYvKD6U8eg/lIA8eaj1Hz7F2LbByJas+2tdRdvLwwRgA73HvG4xtfRaFkseg2CDrdV1FXQlaVH4on0fvodBQ3WJDkUfPMWmSUVZ5N0CvkAch5GUAIyUf/ZRS+qTX1yTbpN2TEHIJgEsAYPz48d1qYzEgIw8/s1VNtMZ6HQ/HvXfcjTHQlEc513kEWWGulIdCsdAr5EEpPbYbX2sAwKcgGwtgi8fx7wRwJwDMmzevbF2YJ49x44BNm+xEfjJk9D5UL7ZEKHWd8L6M3o4yCrLCvLa2dOdX5DGwUPZoKx88BeA8QkiMEDIJwFQAH5S5Tb7gyeP554EtW4ADDvDe/6AxBwEAptVPK3HLygdeUQ0Es1U5V1MHMVsNKaFrTa0kH1gou8+DEHImgD8AGAbgWULIYkrpCZTS5YSQxwCsAJAFcHlfjrQCnOQRiwGjRvnv/92DvouaWA3Om+0jT3Zz8OQxEJRHOWffQcijFEWMTCjlMbBQdvKglP4bwL89PrsBwA2926Luo1BnaTwcx7f2/1bpGtQH4FAeAyBUt6+Txxe+APz+96wKZbGhyGNgoezk0Z/Ak4dYL3qgQimP3kMQ8qiqAhYvLs35ldlqYKEv+zx2O/DkMXhw+drRl6DIo/cgpieRrTAvJZTyGFhQ5FFE8EkQi12saHcFTx79dRU9j76uPEoJRR4DC4o8ioj2dvu1SgbHoMij96CFNISI/Uj3Nnkos9XAgiKPIoInDwUGnjz4RZH9FeWeffPqQykPhVJCOcyLiM7Ocreg72GgKY/99gP23BPYd9/ynD+qRZHMspw4ijwUSglFHkVEOl3uFvQ9DKu0FxYMBPKIRIAVK8pntuSd5r1FHoccArzzDnDmmb1yOoU+AkUeRUSm/2cbKRgjq+2UZrw9vj+jnP4u3mwlS09SCrzxBtDUlH9RrEL/giKPIkKRhxuEEPzl5L/g/c3vY7/R+5W7Of0e5fB5hMOKOAYiFHkUEYo85Lh03qW4dN6l5W7GgEA5HeYKAwsDw47QS1A+D4VyQ5GHQm9BkUcRoZSHQrmhyEOht6DIo4hQ5KFQbvAFoXo7PYnCwIIijyJCkYdCuaGUh0JvQZFHEaF8HgrlxkCu3KjQu1DkUUQo5aFQbvCEURFR2TkVSgdFHkWEWXJ2//3L2w6FgQu+1K+Yol1BoZhQ5FFEPPYYcPXVwL+ldREVFEoPndqpbYlK7axQQiiPWhExejRw443lboXCQEaO5srdBIUBAqU8FBT6EXK6Ig+F3oEiDwWFfgSlPBR6C4o8FBT6EZTyUOgtKPJQUOhHGDNoTLmboDBAoBzmCgr9CLcefyvWt67H52d+vtxNUejnUOShoNCPMLRyKN746hvlbobCAIAyWykoKCgoFAxFHgoKCgoKBUORh4KCgoJCwVDkoaCgoKBQMMpOHoSQzxNClhNCdELIPG77REJIghCy2Pj3l3K2U0FBQUHBRl+ItloG4CwAf5V89hmldG7vNkdBQUFBIR/KTh6U0pWAygCqoKCgsDuh7GarPJhECFlECHmDEHJ4uRujoKCgoMDQK8qDEPIygJGSj35KKX3S42tbAYynlO4ghOwH4D+EkFmU0l2S418C4BLjbQchZHUPmjsUQHMPvr+7YiBe90C8ZkBd90BDT657gtcHvUIelNJju/GdFICU8fpDQshnAKYBWCjZ904Ad/a0nQBACFlIKZ2Xf8/+hYF43QPxmgF13eVuR2+jVNfdZ81WhJBhhBDNeD0ZwFQAa8vbKgUFBQUFoA+QByHkTEJIA4CDATxLCHnR+OgIAEsIIR8D+CeAyyilLeVqp4KCgoKCjb4QbfVvAK6q35TSJwA80fstKo75azfEQLzugXjNgLrugYaSXDehlJbiuAoKCgoK/RhlN1spKCgoKOx+UORhgBByIiFkNSHkU0LIj8vdnlKBEHI3IaSRELKM2zaEEPISIWSN8beunG0sBQgh4wghrxFCVhrpcL5rbO/X104IiRNCPiCEfGxc9y+M7f36ugGAEKIZ68SeMd4PhGteTwhZaqR0WmhsK8l1K/IA62QA/gjgcwBmAjifEDKzvK0qGe4FcKKw7ccAXqGUTgXwivG+vyEL4IeU0hkADgJwuXGP+/u1pwAcTSndG8BcACcSQg5C/79uAPgugJXc+4FwzQAwn1I6lwvPLcl1K/JgOADAp5TStZTSNIBHAZxe5jaVBJTS/wEQo9ZOB3Cf8fo+AGf0Zpt6A5TSrZTSj4zX7WCDyhj082unDB3G24jxj6KfXzchZCyAkwH8ndvcr6/ZByW5bkUeDGMAbOLeNxjbBgpGUEq3AmyQBTC8zO0pKQghEwHsA+B9DIBrN8w3iwE0AniJUjoQrvs2AFcB0Llt/f2aATYx+C8h5EMj8wZQousue6huH4EsK6MKQ+uHIIRUg4WAf49SumsgJOSklOYAzCWE1AL4NyFkdpmbVFIQQk4B0GhkpjiqzM3pbRxKKd1CCBkO4CVCyKpSnUgpD4YGAOO492MBbClTW8qB7YSQUQBg/G0sc3tKAkJIBIw4HqKU/svYPCCuHQAopa0AXgfzefXn6z4UwGmEkPVgJuijCSEPon9fMwCAUrrF+NsItn7uAJTouhV5MCwAMJUQMokQEgVwHoCnytym3sRTAL5ivP4KAK9klbstCJMYdwFYSSn9LfdRv752I81PrfG6AsCxAFahH183pfRqSulYSulEsGf5VUrphejH1wwAhJAqQkiN+RrA8WD1kkpy3WqRoAFCyElgdlINwN2U0hvK26LSgBDyCICjwDJtbgdwDYD/AHgMwHgAGwF8vr+lgiGEHAbgTQBLYdvBfwLm9+i3104I2QvMSaqBTRYfo5ReRwipRz++bhOG2epKSukp/f2ajRyAZraOMICHKaU3lOq6FXkoKCgoKBQMZbZSUFBQUCgYijwUFBQUFAqGIg8FBQUFhYKhyENBQUFBoWAo8lBQUFBQKBiKPBQUFBQUCoYiDwUFBQWFgqHIQ0GhSCCEjCWEfMHn878SQg7tzTYpKJQKijwUFIqHYwDs6/P5gQDe66W2KCiUFIo8FBSKACP9yW8BnGNUcZskfD4DwCdGhltz20RCyCpCyH2EkCWEkH8SQiqNHEXPGtX/lvmpGQWFckGlZFdQKAIopW8RQhaA5VFaJtnlcwBekGyfDuBrlNK3CSF3A/gWgHUAtlBKTwYAQsjgUrVbQaG7UMpDQaF4mA5gtcdnJ0BOHpsopW8brx8EcBhY8sZjCSE3E0IOp5S2Fb+pCgo9gyIPBYUiwMhc2kYpzUg+qwRQa9ZaECBmJqWU0k8A7AdGIr8ihPy86A1WUOghFHkoKBQHk+BdQGw+gNc8PhtPCDnYeH0+gLcIIaMBdFFKHwRwC/yd8AoKZYEiDwWF4mAVgKGGg/sQ4TMvfwcArATwFULIEgBDAPwZwBwAHxh1x38K4JelabKCQveh6nkoKJQYhJCPABwomrQIIRMBPEMp7dc1xRX6J1S0lYJCiUEpVWYnhX4HpTwUFBQUFAqG8nkoKCgoKBQMRR4KCgoKCgVDkYeCgoKCQsFQ5KGgoKCgUDAUeSgoKCgoFAxFHgoKCgoKBUORh4KCgoJCwVDkoaCgoKBQMP4//O9nuoK3fXAAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 340.157x226.772 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Timeline\n", "tmin    = 0\n", "tmax    = 50\n", "Nsteps  = 1000\n", "tvalues = np.linspace( 0, tmax, Nsteps )\n", "dt      = tvalues[1] - tvalues[0]\n", "print(dt)\n", "\n", "# Number of oscillators\n", "Noscillators = np.array([20, 2000, 200000])        \n", "\n", "R = np.zeros((<PERSON><PERSON><PERSON>,3))\n", "\n", "for i,N in enumerate(Noscillators):\n", "\n", "    omega    = np.random.uniform( 0, 1, N ) * N ** a\n", "    k_spring = ang_freqs(omega, N)\n", "    m        = k_spring / omega ** 2\n", "    \n", "    # noise term\n", "    xi     = np.random.normal(0, 1, N)\n", "    # Q0     = 0\n", "    # q0     = Q0 + xi * np.sqrt( 1 / beta / k_spring )\n", "\n", "    eta    = np.random.normal(0, 1, N)\n", "    # p0     = eta * np.sqrt(m / beta )\n", "    \n", "    for k,t in tqdm(enumerate(tvalues)):\n", "        R[k,i] = noise_term( t, omega, k_spring, xi, eta )\n", "\n", "W = sqrt( 2 * M * gamma / beta ) * np.random.normal(0,1,Nsteps)\n", "    \n", "fig, (ax1) = plt.subplots(1, 1,figsize=(12*in2cm, 8*in2cm))  \n", "ax1.plot(tvalues, R[:,0], 'r-',    linewidth = 2,   label =r'$R(t),\\, N=20$')\n", "ax1.plot(tvalues, R[:,1], 'g-',    linewidth = 2,   label =r'$R(t),\\, N=2000$')\n", "ax1.plot(tvalues, R[:,2], 'b-',    linewidth = 2,   label =r'$R(t),\\, N=200000$')\n", "\n", "ax1.set_xlabel(r'$t$ / ps')\n", "ax1.set_ylabel(r'$R(t)$')\n", "\n", "ax1.set_title('Noise term')\n", "ax1.legend()\n", "ax1.set_ylim((-15, 15))\n", "\n", "#ax2.plot(tvalues, W, 'k-',    linewidth = 2,   label ='White noise')\n", "\n", "#ax2.set_xlabel(r'$t$ / ps')\n", "#ax2.set_ylabel(r'$R(t)$')\n", "\n", "#ax2.set_title('White noise')\n", "#ax2.legend()\n", "#ax2.set_ylim((-15, 15))\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3)\n", "\n", "#fig.savefig('figures/noise_term1.png', format='png', dpi=900, bbox_inches='tight')\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "67271a88", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 10/10 [00:00<00:00, 93.14it/s]\n", "100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 10/10 [00:00<00:00, 10.84it/s]\n", "100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 10/10 [01:30<00:00,  9.02s/it]\n"]}], "source": ["# Generate and ensemble of random processes R(t)\n", "Noscillators     = np.array([20, 2000, 200000])\n", "Nreps = 10\n", "\n", "R     = np.zeros((<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 3))\n", "K     = np.zeros((<PERSON><PERSON><PERSON>, 3))\n", "\n", "for j in range(3):\n", "    \n", "    N        = Noscillators[j]\n", "    omega    = np.random.uniform( 0, 1, N ) * N ** a\n", "    k_spring = ang_freqs(omega, N)\n", "    m        = k_spring / omega ** 2\n", "        \n", "    for k,t in enumerate(tvalues):\n", "        K[k,j] = memory_kernel1( t, omega, k_spring )\n", "    \n", "    for r in tqdm(range(Nreps)):\n", "\n", "        # noise term\n", "        xi     = np.random.normal(0, 1, N)\n", "        eta    = np.random.normal(0, 1, N)\n", "\n", "        for k,t in enumerate(tvalues):\n", "            R[k,r,j] = noise_term( t, omega, k_spring, xi, eta )\n", "        "]}, {"cell_type": "code", "execution_count": 6, "id": "0fda2779", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 3/3 [00:08<00:00,  2.96s/it]\n"]}], "source": ["#R = sqrt( 2 * M * gamma / beta ) * np.random.normal(0,1,(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>))\n", "\n", "# Autocorrelation\n", "Atot  = np.zeros((<PERSON><PERSON><PERSON>, 3))\n", "Ntau  = np.arange(Nsteps)\n", "\n", "for j in tqdm(range(3)):\n", "    for r in range(Nreps):\n", "\n", "        A = np.zeros(Nsteps)\n", "\n", "        for t,tau in enumerate(Ntau):\n", "            for k in range(Nsteps - tau):\n", "                A[t] = A[t] + R[k,r,j] * R[k+tau,r,j]\n", "\n", "            A[t] = A[t] / (Nsteps - tau)\n", "\n", "        Atot[:,j] = Atot[:,j] + A\n", "    \n", "    Atot[:,j] = Atot[:,j] / Nreps "]}, {"cell_type": "code", "execution_count": 8, "id": "3a5954dd", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 340.157x226.772 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 1,figsize=(12*in2cm, 8*in2cm))  \n", "\n", "ax.plot(tvalues, f(tvalues) / beta, 'k',   label=r'$\\frac{1}{\\beta}f(t)$')\n", "ax.plot(tvalues, Atot[:,0],  'r--' , label=r\"$\\langle R(0) R(t) \\rangle, \\, N=20$\")\n", "ax.plot(tvalues, Atot[:,1],  'g--' , label=r\"$\\langle R(0) R(t) \\rangle, \\, N=2000$\")\n", "ax.plot(tvalues, Atot[:,2],  'b--' , label=r\"$\\langle R(0) R(t) \\rangle, \\, N=200000$\")\n", "\n", "#ax.plot(tvalues, K[:,0],  'r-.' , label=r\"$K(t), \\, N=200$\")\n", "#ax.plot(tvalues, K[:,1],  'g-.' , label=r\"$K(t), \\, N=2000$\")\n", "#ax.plot(tvalues, K[:,2],  'b-.' , label=r\"$K(t), \\, N=20000$\")\n", "\n", "ax.set_xlabel(r'$t$ / ps')\n", "ax.set_ylabel(r'$K(t)$')\n", "ax.set_title(r'Autocorrelation $\\langle R(0) R(t) \\rangle$')\n", "ax.legend()\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3)\n", "#fig.savefig('figures/noise_term2.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}