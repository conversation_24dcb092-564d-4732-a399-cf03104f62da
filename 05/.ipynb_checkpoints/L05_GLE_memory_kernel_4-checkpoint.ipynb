{"cells": [{"cell_type": "code", "execution_count": 1, "id": "98db341d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import sympy as sp\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "import scipy.linalg\n", "import scipy.sparse\n", "import scipy.sparse.linalg\n", "from sympy import *\n", "from tqdm import tqdm\n", "\n", "from integrator_functions import euler_integrator_step, vel_verlet_integrator_step, leap_frog_integrator_step, acf\n", "\n", "\n", "np.random.seed(0)\n", "\n", "font = {'size'   : 10}\n", "plt.rc('font', **font)\n", "in2cm = 1/2.54  # centimeters in inches"]}, {"cell_type": "code", "execution_count": 2, "id": "b82a2782", "metadata": {}, "outputs": [], "source": ["# System parameters\n", "kB    = 0.008314463 # kJ mol-1 K\n", "T     = 300         # K\n", "beta  = 1 / kB / T  # kJ-1 mol\n", "M     = 1           # amu"]}, {"cell_type": "markdown", "id": "a048b617", "metadata": {}, "source": ["# Convergence of the memory kernel to $e^{-\\alpha t}$\n", "\n", "The $i$th particle, with mass $m_i$ and spring constant $k_i$, oscillates with frequency\n", "\n", "$$\n", "\\omega_i = \\sqrt{\\frac{k_i}{m_i}} \\, .\n", "$$\n", "\n", "The frequencies are drawn from a uniform distribution:\n", "\n", "$$\n", "\\omega_i = N^a \\nu_i\\,; \\quad \\nu_i \\in \\mathcal{U}(0,1)\\,; \\quad a \\in[0,1]\n", "$$\n", "\n", "The spring constants are written as\n", "\n", "$$\n", "    k_i = \\frac{2 }{\\pi} \\frac{\\alpha^2 \\gamma}{\\alpha^2 + \\omega_i^2} \\Delta \\omega \\, ,\n", "$$\n", "\n", "where \n", "\n", "$$\n", "\\Delta \\omega = \\frac{N^a}{N} \\, .\n", "$$\n", "\n", "The memory kernel is written as\n", "\n", "$$\n", "K(t) = \\sum_{i=1}^N \n", "\\frac{2}{\\pi} \n", "\\frac{\\alpha^2 M \\gamma}{\\alpha^2 + \\omega_i^2} \\, \\Delta \\omega \n", "$$\n", "\n", "We show that \n", "\n", "$$\n", "\\lim_{N\\rightarrow \\infty}\n", "\\sum_{i=1}^N \\frac{2}{\\pi} \n", "\\frac{\\alpha^2 M \\gamma}{\\alpha^2 + \\omega_i^2} \\, \\Delta \\omega \n", "=\n", "\\alpha M \\gamma e^{-\\alpha t}\n", "$$"]}, {"cell_type": "code", "execution_count": 3, "id": "25282355", "metadata": {}, "outputs": [], "source": ["a       = 0.3333\n", "\n", "gamma   = 1.5\n", "alpha   = 1\n", "\n", "# Angular frequencies\n", "def ang_freqs(omega, N):\n", "    \n", "    domega = N**a / N\n", "    return ( 2 / np.pi ) * ( alpha ** 2 * M * gamma ) / ( alpha ** 2  + omega ** 2) * domega\n", "\n", "# Memory kernel\n", "def memory_kernel1( t, omega, k_spring ):\n", "\n", "    return np.sum(  k_spring * np.cos( omega * t ) )\n", "\n", "def f(t):\n", "    return alpha * M * gamma * np.exp( - alpha * t )"]}, {"cell_type": "code", "execution_count": 4, "id": "3a5954dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.10101010101010101\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100it [00:00, ?it/s]\n", "100it [00:00, 12500.53it/s]\n", "100it [00:00, 211.40it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 472.441x314.961 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Timeline\n", "tmin    = 0\n", "tmax    = 10\n", "Nsteps  = 100\n", "tvalues = np.linspace( 0, tmax, Nsteps )\n", "dt      = tvalues[1] - tvalues[0]\n", "print(dt)\n", "\n", "# Number of oscillators\n", "Noscillators = np.array([20, 2000, 200000])        \n", "\n", "K = np.zeros((<PERSON><PERSON><PERSON>,3))\n", "\n", "for i,N in enumerate(Noscillators):\n", "\n", "    omega    = np.random.uniform( 0, 1, N ) * N ** a\n", "    k_spring = ang_freqs(omega, N)\n", "    \n", "    for k,t in tqdm(enumerate(tvalues)):\n", "        K[k,i] = memory_kernel1( t, omega, k_spring )\n", "\n", "    \n", "fig, ax = plt.subplots(1, 1,figsize=(12*in2cm, 8*in2cm))  \n", "ax.plot(tvalues, f(tvalues), 'k-', linewidth = 2,   label =r'$f(t)$')\n", "ax.plot(tvalues, K[:,0], 'r--',    linewidth = 2,   label =r'$K(t),\\, N=20$')\n", "ax.plot(tvalues, K[:,1], 'g--',    linewidth = 2,   label =r'$K(t),\\, N=2000$')\n", "ax.plot(tvalues, K[:,2], 'b--',    linewidth = 2,   label =r'$K(t),\\, N=200000$')\n", "\n", "ax.set_xlabel(r'$t$ / ps')\n", "ax.set_ylabel(r'$K(t)$')\n", "\n", "ax.set_title('Memory kernel')\n", "ax.legend()\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3)\n", "\n", "fig.savefig('figures/memory_kernel1.png', format='png', dpi=900, bbox_inches='tight')"]}, {"cell_type": "markdown", "id": "30ac73ad", "metadata": {}, "source": ["# Convergence of $e^{-\\alpha t}$ to a delta function\n", "In what follows, we assume an infinite number of oscillators, then the memory kernel can be written as\n", "\n", "$$\n", "K(t) = \\alpha M \\gamma e^{-\\alpha t} \n", "$$"]}, {"cell_type": "code", "execution_count": 5, "id": "326baf5d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100it [00:00, ?it/s]\n", "100it [00:00, 24998.83it/s]\n", "100it [00:00, 25009.27it/s]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAh0AAAGRCAYAAADB+gHZAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAABPZElEQVR4nO3dd3gU1f4G8Hd2N9kUUkgo6SFIqAkkEODSBJSOgFRBRcSrF5RqEIFroaiJgiIIF/wBV7ECFpqgQgSkSAtNELgUDRBqqAmkZ/f8/hiz2SWbumV2w/t5nnnYnZ3ynU3YfXPmzBlJCCFAREREZGMqpQsgIiKiBwNDBxEREdkFQwcRERHZBUMHERER2QVDBxEREdkFQwcRERHZBUMHERER2QVDBxEREdkFQwcRERHZBUMHVTnLly+HJEklTr/++qvSJSoqKysLM2bMMPs+FL53586dq/B2LVmXzHv22WdRrVo1pcuolE6dOqFTp05Kl0EORqN0AUS28umnn6Jhw4bF5jdu3FiBahxHVlYWZs6cCQDFvhR69+6NPXv2IDAwUIHKiKiqY+igKisqKgpxcXFKl+FUatasiZo1aypdRpWQlZUFDw8PpcsoVX5+PiRJgkbDrwKyD55eoQfWypUrIUkSFi5caDJ/+vTpUKvVSEpKAgCcO3cOkiRh9uzZeOeddxAWFgY3NzfExcVhy5Ytxba7a9cuPProo/Dy8oKHhwfatm2LjRs3mixTeCpi27ZtePHFF1GjRg34+/tjwIABuHz5crFtrlq1Cm3atIGnpyeqVauG7t274/DhwybLFDbFnz17Fr169UK1atUQGhqKSZMmITc313AshaFi5syZhlNOzz77rEldxqdIkpKS0K9fP4SEhMDNzQ316tXDqFGjcOPGjYq94X+bMWMGJEnC0aNHMXjwYPj4+MDPzw/x8fEoKCjAqVOn0KNHD3h5eaFOnTqYPXt2sW1kZGTglVdeQUREBFxdXREcHIyJEyciMzPTZDlJkjB27Fh8+umnaNCgAdzd3REXF4e9e/dCCIE5c+YgIiIC1apVwyOPPIKzZ88W29cnn3yCZs2awc3NDX5+fujfvz9Onjxp9r0/duwYunXrBi8vLzz66KN46623oNFokJqaWmy7zz33HPz9/ZGTk1Oh9++3335DjRo18NhjjxmO98yZM3jyySdRq1YtaLVaNGrUCP/5z39M1vv1118hSRK++OILTJo0CcHBwdBqtTh79my5fncK5eXl4e2330bDhg2h1WpRs2ZNjBw5EtevX6/QcdADShBVMZ9++qkAIPbu3Svy8/NNpoKCApNlR48eLVxdXUVycrIQQogtW7YIlUolXn/9dcMyKSkpAoAIDQ0V7du3F99//7349ttvRcuWLYWLi4vYvXu3Ydlff/1VuLi4iBYtWohVq1aJtWvXim7duglJksTKlSuL1Vi3bl0xbtw4sWnTJrFs2TJRvXp10blzZ5Ma33nnHSFJknjuuefEhg0bxOrVq0WbNm2Ep6enOH78uGG5ESNGCFdXV9GoUSPx/vvvi19++UW8+eabQpIkMXPmTCGEEDk5OeLnn38WAMQ///lPsWfPHrFnzx5x9uxZk7pSUlIM2128eLFITEwU69evF9u3bxefffaZaNasmWjQoIHIy8srdkzG65ozffp0AUA0aNBAvPXWWyIpKUm8+uqrAoAYO3asaNiwofjoo49EUlKSGDlypAAgvv/+e8P6mZmZIiYmRtSoUUPMnTtX/PLLL2L+/PnCx8dHPPLII0Kv1xuWBSDCw8NF27ZtxerVq8WaNWtE/fr1hZ+fn3j55ZdFv379xIYNG8RXX30lateuLZo2bWqyfkJCggAghg0bJjZu3Cg+//xzUbduXeHj4yNOnz5t8t67uLiIOnXqiMTERLFlyxaxadMmce3aNaHVasVrr71m8h7cvHlTuLu7i8mTJ5f6Xo0YMUJ4enoanq9atUpotVrx4osvGn6Xjx8/Lnx8fER0dLT4/PPPxebNm8WkSZOESqUSM2bMMKy7bds2AUAEBweLQYMGifXr14sNGzaImzdvlut3RwghdDqd6NGjh/D09BQzZ84USUlJYtmyZSI4OFg0btxYZGVlGZbt2LGj6NixY6nHRw8ehg6qcgq//MxNarXaZNmcnBwRGxsrIiIixIkTJ0Tt2rVFx44dTcJJYegICgoS2dnZhvkZGRnCz89PdOnSxTDvH//4h6hVq5a4e/euYV5BQYGIiooSISEhhi+0whpfeuklk3pmz54tAIgrV64IIYS4cOGC0Gg0Yty4cSbL3b17VwQEBIghQ4YY5o0YMUIAEN98843Jsr169RINGjQwPL9+/boAIKZPn17ie1dScNDr9SI/P1+cP39eABDr1q0r97qFCkPHBx98YDI/JiZGABCrV682zMvPzxc1a9YUAwYMMMxLTEwUKpXKEBQLfffddwKA+PHHHw3zAIiAgABx7949w7y1a9cKACImJsYkYMybN08AEEePHhVCCHH79m3h7u4uevXqZbKfCxcuCK1WK5588knDvML3/pNPPil2vCNGjBC1atUSubm5hnnvvfeeUKlUZb5XxqHj3XffFWq1Wrz33nsmy3Tv3l2EhISI9PR0k/ljx44Vbm5u4tatW0KIotDx8MMPm91PeX53VqxYUSwECiFEcnKyACAWLVpkmMfQQebw9ApVWZ9//jmSk5NNpn379pkso9Vq8c033+DmzZto3rw5hBBYsWIF1Gp1se0NGDAAbm5uhudeXl7o06cPduzYAZ1Oh8zMTOzbtw+DBg0yueJArVZj+PDhuHjxIk6dOmWyzb59+5o8b9q0KQDg/PnzAIBNmzahoKAAzzzzDAoKCgyTm5sbOnbsWOwKFEmS0KdPn2LbLNxeZaSlpWH06NEIDQ2FRqOBi4sLwsPDAaDYaYaKeOyxx0yeN2rUCJIkoWfPnoZ5Go0G9erVM6l/w4YNiIqKQkxMjMl70r17d7NXJ3Xu3Bmenp4m+wGAnj17QpKkYvML97Vnzx5kZ2cbTj0VCg0NxSOPPGL21NrAgQOLzZswYQLS0tLw7bffAgD0ej0WL16M3r17o06dOiW9PQZCCIwaNQrTp0/H119/jVdffdXwWk5ODrZs2YL+/fvDw8PD5P3o1asXcnJysHfv3jJrBMr3u7Nhwwb4+vqiT58+JvuKiYlBQEDAA39lGJWNvYeoymrUqFG5OpLWq1cPHTp0wMaNG/Hiiy+WeOVGQECA2Xl5eXm4d+8e7t69CyGE2fWDgoIAADdv3jSZ7+/vb/Jcq9UCALKzswEA165dAwC0bNnSbE0qlenfDR4eHibBqHCbFe03UEiv16Nbt264fPky3njjDURHR8PT0xN6vR7/+Mc/DHVWhp+fn8lzV1dXs/W7uroiIyPD8PzatWs4e/YsXFxczG73/r4m5vZT2vzC96rwZ1XSz7Owz08hDw8PeHt7F1s2NjYWHTp0wH/+8x889dRT2LBhA86dO4f/+7//M1v//fLy8rBq1So0adLEJJAV1lhQUIAFCxZgwYIFZte///0o6fe7PL87165dw507dwzvVVn7IrofQwc98JYtW4aNGzeiVatWWLhwIZ544gm0bt262HJXr141O8/V1RXVqlWDRqOBSqXClStXii1X2Dm0Ro0aFaqtcPnvvvvO0LpgT3/88Qd+//13LF++HCNGjDDMN9fh0l5q1KgBd3d3fPLJJyW+bg2FgbCkn+f9+zFuNbnf+PHjMXjwYBw6dAgLFy5E/fr10bVr13LVodVqsW3bNnTv3h1dunTBzz//jOrVqwMAqlevbmhJGzNmjNn1IyIiyl1nWQo7PP/8889mX/fy8qr0tunBwNBBD7Rjx45h/PjxeOaZZ7B06VK0bdsWTzzxBA4fPmz4YC+0evVqzJkzx/DX4N27d/HDDz+gQ4cOUKvV8PT0ROvWrbF69Wq8//77cHd3ByC3Fnz55ZcICQlB/fr1K1Rf9+7dodFo8Oeff5bYLF5R97emlKbwC6pwnULl/SvdFh577DEkJCTA39+/2BeqNbVp0wbu7u748ssvMXjwYMP8ixcvYuvWrRg0aFC5t9W/f3+EhYVh0qRJ2L59Oz788MMKffnHxsZi+/bt6NKlCzp16oSkpCTUqlULHh4e6Ny5Mw4fPoymTZuW2AJhLY899hhWrlwJnU5nNpgTlYWhg6qsP/74AwUFBcXmP/TQQ6hZsyYyMzMxZMgQREREYNGiRXB1dcU333yD5s2bY+TIkVi7dq3Jemq1Gl27dkV8fDz0ej3ee+89ZGRkGAbaAoDExER07doVnTt3xiuvvAJXV1csWrQIf/zxB1asWFHhvzLr1KmDWbNm4bXXXsNff/2FHj16oHr16rh27Rr2798PT09Pk/2Xh5eXF8LDw7Fu3To8+uij8PPzQ40aNcz2L2jYsCEeeughTJ06FUII+Pn54Ycffih2asGeJk6ciO+//x4PP/wwXn75ZTRt2hR6vR4XLlzA5s2bMWnSJKt8Ifr6+uKNN97Av//9bzzzzDMYNmwYbt68iZkzZ8LNzQ3Tp08v97bUajXGjBmDKVOmwNPTs1g/kfJo1KgRdu7ciS5duuDhhx/GL7/8gpCQEMyfPx/t27dHhw4d8OKLL6JOnTq4e/cuzp49ix9++AFbt26t8L5KMnToUHz11Vfo1asXJkyYgFatWsHFxQUXL17Etm3b0K9fP/Tv399q+6Oqh6GDqqyRI0eanb906VI8//zzGD16NC5cuIDk5GRDR8O6deti2bJlGDx4MObNm4eJEyca1hs7dixycnIwfvx4pKWloUmTJti4cSPatWtnWKZjx47YunUrpk+fjmeffRZ6vR7NmjXD+vXri3WcLK9p06ahcePGmD9/PlasWIHc3FwEBASgZcuWGD16dKW2+d///heTJ09G3759kZubixEjRmD58uXFlnNxccEPP/yACRMmYNSoUdBoNOjSpQt++eUXhIWFVWrflvL09MTOnTvx7rvvYsmSJUhJSYG7uzvCwsLQpUuXcnXOLK9p06ahVq1a+Oijj7Bq1Sq4u7ujU6dOSEhIQGRkZIW29cQTT2DKlCkYPnw4fHx8KlVP3bp1DcGjQ4cO2LJlCxo3boxDhw7hrbfewuuvv460tDT4+voiMjISvXr1qtR+SqJWq7F+/XrMnz8fX3zxBRITE6HRaBASEoKOHTsiOjraqvujqkcSQgiliyByZOfOnUNERATmzJmDV155RelyyEktWLAA48ePxx9//IEmTZooXQ6RItjSQURkQ4cPH0ZKSgpmzZqFfv36MXDQA42hg4jIhvr374+rV6+iQ4cO+Pjjj5Uuh0hRPL1CREREdsERSYmIiMguGDqIiIjILhg6iIiIyC4emI6ker0ely9fhpeXl0XDABMREVERIQTu3r2LoKCgYveDut8DEzouX76M0NBQpcsgIiKqklJTUxESElLqMg9M6Ci8EVFqaqrZO0ESERFRxWVkZCA0NLRcN/x7YEJH4SkVb29vhg4iIiIrK0/XBXYkJSIiIrtg6CAiIiK7YOggIiIiu3hg+nQQEVHVodPpkJ+fr3QZDwy1Wg2NRmPxkBMMHURE5FTu3buHixcvgrcOsy8PDw8EBgbC1dW10ttg6CAiIqeh0+lw8eJFeHh4oGbNmhzs0Q6EEMjLy8P169eRkpKCyMjIMgcBKwlDBxEROY38/HwIIVCzZk24u7srXc4Dw93dHS4uLjh//jzy8vLg5uZWqe2wIykRETkdtnDYX2VbN0y2YYU6iIiIiMrE0EFERER2wdBBREREdsHQYYGWLQF/fyAyUulKiIiIZDt27ECfPn0QFBQESZKwdu1apUsyYOiwwJ07wK1bwO3bSldCREQky8zMRLNmzbBw4UKlSymGocMCheOj5OYqWwcRETmH/fv3o1OnTnB3d0fDhg2RnJyMJUuWoG/fvlbbR8+ePfH2229jwIABVtumtTB0WKAwdOTlKVsHEdEDb+5cICSk7Mncl3vfvuVbd+5ci0rcu3cvOnbsiB49euDo0aNo3LgxZsyYgTlz5mDmzJkmyyYkJKBatWqlTjt37rSoHiVwcDALGIcOIQBeNk5EpJCMDODSpbKXCw0tPu/69fKtm5FR8bqMxMfHY+DAgZg6dSoAYOjQoRg2bBj69euH2NhYk2VHjx6NIUOGlLq94OBgi+pRAkOHBYyHny8oAFxclKuFiOiB5u0NlOdLuGZN8/PKs663d8Xr+tvFixexZ88ezJkzxzDP1dUVQohirRwA4OfnBz8/v0rvz1ExdFhAqy16nJfH0EFEpJj4eHmqjPXrrVuLGSdPngQAxMXFGeadOnUKrVq1QnR0dLHlExISkJCQUOo2f/rpJ3To0MG6hdoYQ4cFjFs68vIAT0/laiEiIseVnp4OtVpteH7r1i3Mnj0bUVFRZpfn6RUq5v7QQUREZE5MTAx0Oh1mz56NwYMHY8KECQgPD8fJkydx/vx5hIeHmyxvyemVe/fu4ezZs4bnKSkpOHLkCPz8/BAWFmbRcViKV69YwDh08LJZIiIqSb169TBr1izMnz8fsbGxCAwMxObNmxEaGoouXbpYdV8HDhxAbGysoXNqfHw8YmNj8eabb1p1P5UhCSGE0kXYQ0ZGBnx8fJCeng5vCzoDGdu+HUhNlft29OrF0ytERLaWk5ODlJQUREREVPr26lQ5Jb33Ffl+5ekVC3TsqHQFREREzoOnV4iIiMguGDqIiIjILnh6xQJXrgCXL8tXrjRuDPj4KF0RERGR43KIlo6K3IZ31KhRkCQJ8+bNs1t9JVm4EIiLA9q2BQ4eVLoaIiIix+YQoaO8t+Fdu3Yt9u3bh6CgIDtVVjqO00FERFR+DnF6pWfPnujZs2epy1y6dAljx47Fpk2b0Lt3bztVVjqGDiIiovJziNBRFr1ej+HDh2Py5Mlo0qRJudbJzc1FrtGIXRkW3h3QHIYOIiKi8nOI0ytlee+996DRaDB+/Phyr5OYmAgfHx/DFGrudsYW4oikRERE5efwoePgwYOYP38+li9fDkmSyr3etGnTkJ6ebphSU1OtXhtbOoiIiMrP4UPHzp07kZaWhrCwMGg0Gmg0Gpw/fx6TJk1CnTp1SlxPq9XC29vbZLI2hg4iInI0M2bMgCRJJlNAQIDSZQFwgj4dw4cPL3YznO7du2P48OEYOXKkQlXJGDqIiMgRNWnSBL/88ovhuVqtVrCaIg4ROsq6Da+/v7/J8i4uLggICECDBg3sXaoJrbboMUMHERGVZf/+/Xj11Vexb98+hIeH44svvsDhw4exYcMGrF+/3mr70Wg0DtO6YcwhQseBAwfQuXNnw/P4+HgAwIgRI7B8+XKFqiobWzqIiBzD3D1zMXfP3DKXax7YHOuHmX65913RF4euHCpz3fg28YhvE1/pGvfu3YvOnTtj+vTpWLp0KaZMmYIZM2bg9OnT+Oabb0yWTUhIQEJCQqnb++mnn9ChQwezr505cwZBQUHQarVo3bo1EhISULdu3UrXbi0OETo6deoEIUS5lz937pztiqmAbt2Aa9fk8OHhoXQ1REQProzcDFy6e6nM5UJ9il/JeD3rernWzci1bOiF+Ph4DBw4EFOnTgUADB06FMOGDUO/fv0QGxtrsuzo0aMxZMiQUrcXHBxsdn7r1q3x+eefo379+rh27RrefvtttG3bFsePHy925sDeHCJ0OCs3N3kiIiJleWu9Eexl/kvYWE2PmmbnlWddb23lL0i4ePEi9uzZgzlz5hjmubq6QgiBmTNnFlvez88Pfn5+ldqX8WCb0dHRaNOmDR566CF89tlnhjMJSmHoICIip2fJqY/7T7fYwsmTJwEAcXFxhnmnTp1Cq1atEB0dXWx5S0+vGPP09ER0dDTOnDlTwaqtj6GDiIjIxtLT002uILl16xZmz56NqKgos8tbcnrlfrm5uTh58mS5AoqtMXRYIC0N+O9/5U6kMTFAv35KV0RERI4oJiYGOp0Os2fPxuDBgzFhwgSEh4fj5MmTOH/+PMLDw02Wt+T0yiuvvII+ffogLCwMaWlpePvtt5GRkYERI0ZY41As4vCDgzmyGzeAf/8bmDEDWLdO6WqIiMhR1atXD7NmzcL8+fMRGxuLwMBAbN68GaGhocXGorLUxYsXMWzYMDRo0AADBgyAq6sr9u7dWyzYKIEtHRbgJbNERFReb7zxBt544w2TeQcPHrT6flauXGn1bVoLWzoswNBBRERUfgwdFmDoICIiKj+GDgswdBAREZUfQ4cFGDqIiIjKj6HDAgwdRERE5cfQYQEXl6LHDB1ERESlY+iwgCQVtXYwdBAREZWO43RYKC4OKCgAGjRQuhIiIiLHxtBhod9+U7oCIiIi58DTK0RERGQXDB1ERERVyI4dO9CnTx8EBQVBkiSsXbvW7HKLFi1CREQE3Nzc0KJFC+zcudPmtTF0EBERVSGZmZlo1qwZFi5cWOIyq1atwsSJE/Haa6/h8OHD6NChA3r27IkLFy7YtDaGDgs99xzQrh3QsaPSlRARkaPbv38/OnXqBHd3dzRs2BDJyclYsmQJ+vbta7V99OzZE2+//TYGDBhQ4jJz587FP//5Tzz//PNo1KgR5s2bh9DQUCxevNhqdZjD0GGhI0eA3buBvXuVroSI6ME1dy4QElL2ZO67vW/f8q07d65lNe7duxcdO3ZEjx49cPToUTRu3BgzZszAnDlzMHPmTJNlExISUK1atVKnyp4OycvLw8GDB9GtWzeT+d26dcPu3bsrfXzlwatXLGQ8TocQ8tgdRERkXxkZwKVLZS8XGlp83vXr5Vs3I6PidRmLj4/HwIEDMXXqVADA0KFDMWzYMPTr1w+xsbEmy44ePRpDhgwpdXvBwcGVquPGjRvQ6XSoXbu2yfzatWvj6tWrldpmeTF0WMh4KPSCAtNRSomIyD68vYHyfAfXrGl+XnnW9faueF2FLl68iD179mDOnDmGea6urhBCFGvlAAA/Pz/4+flVfoflIN33V7IQotg8a2PosND9919h6CAisr/4eHmqjPXrrVuLOSdPngQAxMXFGeadOnUKrVq1QnR0dLHlExISkJCQUOo2f/rpJ3To0KHCtdSoUQNqtbpYq0ZaWlqx1g9rY+iw0P2hw9NTuVqIiMgxpaenQ61WG57funULs2fPRlRUlNnlbXl6xdXVFS1atEBSUhL69+9vmJ+UlIR+/fpVapvlxdBhId5ploiIyhITEwOdTofZs2dj8ODBmDBhAsLDw3Hy5EmcP38e4eHhJstbcnrl3r17OHv2rOF5SkoKjhw5Aj8/P4SFhQGQ+5cMHz4ccXFxaNOmDZYsWYILFy5g9OjRlT/IcuDVKxZi6CAiorLUq1cPs2bNwvz58xEbG4vAwEBs3rwZoaGh6NKli1X3deDAAcTGxho6p8bHxyM2NhZvvvmmYZknnngC8+bNw6xZsxATE4MdO3bgxx9/LBZ+rE0SQgib7sFBZGRkwMfHB+np6fC2pDfQfZ5+GvjqK/nx2bPAQw9ZbdNERHSfnJwcpKSkGEbSJPsp6b2vyPerQ7R0lDZka35+PqZMmYLo6Gh4enoiKCgIzzzzDC5fvqxcwUaMWzpyc5Wrg4iIyNE5RJ+OwiFbR44ciYEDB5q8lpWVhUOHDuGNN95As2bNcPv2bUycOBF9+/bFgQMHFKq4yGOPyZdauboC/v5KV0NEROS4HCJ09OzZEz179jT7mo+PD5KSkkzmLViwAK1atcKFCxcMnWKUMmCAPBEREVHpHCJ0VFR6ejokSYKvr2+Jy+Tm5iLX6HxHhqVDyREREZFFHKJPR0Xk5ORg6tSpePLJJ0vtsJKYmAgfHx/DFGpu7FsiInJKD8g1EA7FGu+5U4WO/Px8DB06FHq9HosWLSp12WnTpiE9Pd0wpaam2qQmnQ7IzARu32ZHUiIiWyscYCuPYxTYXVZWFgDAxYKht53m9Ep+fj6GDBmClJQUbN26tczLcrRaLbRarc3rmj0b+Pe/5cfr1pm/gyEREVmHRqOBh4cHrl+/DhcXF6hUTvW3s1MSQiArKwtpaWnw9fU1GVm1opwidBQGjjNnzmDbtm3wd6DLRDg4GBGR/UiShMDAQKSkpOD8+fNKl/NA8fX1RUBAgEXbcIjQUdqQrUFBQRg0aBAOHTqEDRs2QKfTGW5S4+fnB1fjb30FMHQQEdmXq6srIiMjeYrFjlxcXCxq4SjkEKHjwIED6Ny5s+F5/N+3ChwxYgRmzJiB9X/fAjAmJsZkvW3btqFTp072KtMsDg5GRGR/KpWKI5I6IYcIHZ06dSq1V6wj91JmSwcREVH5sAeOhRg6iIiIyoehw0IMHUREROXD0GEh46tyGTqIiIhKxtBhIbZ0EBERlQ9Dh4UYOoiIiMrHIa5ecWbNmwM7dsjhIzhY6WqIiIgcF0OHhXx9gQ4dlK6CiIjI8fH0ChEREdkFQwcRERHZBU+vWCgzE0hKkodADw4G2rdXuiIiIiLHxNBhoZs3gf795ceDBzN0EBERlYSnVyzES2aJiIjKh6HDQhyRlIiIqHwYOizElg4iIqLyYeiwEEMHERFR+TB0WEhj1BU3N1e5OoiIiBwdQ4eFJKmotYMtHURERCVj6LAChg4iIqKyMXRYAUMHERFR2Tg4mBX4+AAFBYCnp9KVEBEROS6GDiv46y+lKyAiInJ8PL1CREREdsHQQURERHbB0EFERER2wT4dVjB7NnDqlHz1yuefy2N3EBERkSmGDivYsAHYuVN+/MkngIuLsvUQERE5Ip5esQLef4WIiKhsDB1WYBw6eP8VIiIi8xwidOzYsQN9+vRBUFAQJEnC2rVrTV4XQmDGjBkICgqCu7s7OnXqhOPHjytTrBls6SAiIiqbQ4SOzMxMNGvWDAsXLjT7+uzZszF37lwsXLgQycnJCAgIQNeuXXH37l07V2qeVlv0mKGDiIjIPIfoSNqzZ0/07NnT7GtCCMybNw+vvfYaBgwYAAD47LPPULt2bXz99dcYNWqUPUs1iy0dREREZXOIlo7SpKSk4OrVq+jWrZthnlarRceOHbF79+4S18vNzUVGRobJZCsMHURERGVz+NBx9epVAEDt2rVN5teuXdvwmjmJiYnw8fExTKGhoTarkaGDiIiobA4fOgpJ9424JYQoNs/YtGnTkJ6ebphSU1NtVhtDBxERUdkcok9HaQICAgDILR6BgYGG+WlpacVaP4xptVpojXt42lBsLDBokBw+fH3tsksiIiKn4/AtHREREQgICEBSUpJhXl5eHrZv3462bdsqWFmRZ58Fvv0W+OoroH59pashIiJyTA7R0nHv3j2cPXvW8DwlJQVHjhyBn58fwsLCMHHiRCQkJCAyMhKRkZFISEiAh4cHnnzySQWrJiIioopwiNBx4MABdO7c2fA8Pj4eADBixAgsX74cr776KrKzs/HSSy/h9u3baN26NTZv3gwvLy+lSiYiIqIKkoQQQuki7CEjIwM+Pj5IT0+Ht7e30uUQERFVCRX5fnX4Ph3O4PPPgdBQoHZtuW8HERERFecQp1ecXU4OcPGi/PjePWVrISIiclRs6bACjtNBRERUNoYOK2DoICIiKhtDhxUwdBAREZWNocMKGDqIiIjKxtBhBQwdREREZWPosAKGDiIiorIxdFgBQwcREVHZGDqsgKGDiIiobBwczAoiIoAlS+Tw0bix0tUQERE5JoYOK6hZE3jhBaWrICIicmw8vUJERER2wdBBREREdsHTK1ZQUACcOSN3IvXwACIjla6IiIjI8TB0WEFGRlEH0p49gR9/VLYeIiIiR8TTK1ag1RY95iWzRERE5jF0WAHH6SAiIiobQ4cVaIxOUjF0EBERmcfQYQWSVNTawdBBRERkHkOHlRSGjtxcZesgIiJyVAwdVsKWDiIiotIxdFgJQwcREVHpGDqshKGDiIiodAwdVlI4VgdDBxERkXkckdRKfv0VUKlMx+wgIiKiIgwdVhIUpHQFREREjs0pTq8UFBTg9ddfR0REBNzd3VG3bl3MmjULer1e6dKIiIionJyipeO9997Dxx9/jM8++wxNmjTBgQMHMHLkSPj4+GDChAlKl0dERETl4BShY8+ePejXrx969+4NAKhTpw5WrFiBAwcOKFxZkdWrgT//lDuSvvKK6U3giIiIyMLTK/n5+UhNTcWpU6dw69Yta9VUTPv27bFlyxacPn0aAPD7779j165d6NWrV4nr5ObmIiMjw2SypWXLgFdfBV5/HcjMtOmuiIiInFKFWzru3buHr776CitWrMD+/fuRazTud0hICLp164Z//etfaNmypdWKnDJlCtLT09GwYUOo1WrodDq88847GDZsWInrJCYmYubMmVaroSy80ywREVHpKtTS8eGHH6JOnTpYunQpHnnkEaxevRpHjhzBqVOnsGfPHkyfPh0FBQXo2rUrevTogTNnzlilyFWrVuHLL7/E119/jUOHDuGzzz7D+++/j88++6zEdaZNm4b09HTDlJqaapVaSsLQQUREVLoKtXTs3r0b27ZtQ3R0tNnXW7Vqheeeew6LFy/GJ598gu3btyMyMtLiIidPnoypU6di6NChAIDo6GicP38eiYmJGDFihNl1tFottHbsWGG8K4YOIiKi4ioUOr799lvD4zZt2mDTpk3w9vYutpybmxteeukly6v7W1ZWFlQq00YZtVrtUJfMsqWDiIiodJXuSLpv3z7k5OQUm5+RkYHJkydbVNT9+vTpg3feeQcbN27EuXPnsGbNGsydOxf9+/e36n4swdBBRERUugqHjgEDBuDdd9+FJElIS0sr9npmZibmzp1rleIKLViwAIMGDcJLL72ERo0a4ZVXXsGoUaPw1ltvWXU/lmDoICIiKl2Fr14JDw/Hhg0bIIRAs2bN4O/vj2bNmqFZs2Zo2rQpjh49isDAQKsW6eXlhXnz5mHevHlW3a41GYcOowt6iIiI6G8VDh0ffvghALmj5q5du3D58mUcPnwYR44cwZo1a6DX6zF79myrF+ro2NJBRERUukqPSJqZmQmNRl69X79+VivIWQUFAVFR8lUsHh5KV0NEROR4KhQ6Lly4gLCwMHlFTdmrXrp0CcHBwZWrzAmM3jAax68fhxACu8bswpgxSldERETkuCrUkbRly5Z44YUXsH///hKXSU9Px9KlSxEVFYXVq1dbXKAjO3D5AHZd2IW9F/dCLxzn8l0iIiJHVKGWjpMnTyIhIQE9evSAi4sL4uLiEBQUBDc3N9y+fRsnTpzA8ePHERcXhzlz5qBnz562qtsh1PCoAQDQCR3Sc9JR3b26whURERE5rgq1dPj5+eH999/H5cuXsXjxYtSvXx83btwwDHf+1FNP4eDBg/jtt9+qfOAAAH8Pf8PjG1k3FKyEiIjI8VW4I+miRYvw0ksvYcCAARgwYIAtanIaNdxrGB7/tDkfL30iX7kycSLgQOOWEREROYQKDw42efJk7Nmzp9Rlrl69WumCnIlxS8eFyzn45Rdgxw7g4kUFiyIiInJQFQ4d77zzDgYOHIhr166Zff3w4cNo1aqVxYU5g8I+HQCQrU83PObgYERERMVVOHRMnDgRnTt3xsCBA1FQUGDy2rp169ChQwe0a9fOagU6spJCBwcHIyIiKq5SN3xbtmwZMjMzMW7cOMO8OXPmYNCgQXj11VexYsUKqxXoyPzdi06vZOpvGx4zdBARERVXqRFJ3d3dsXr1arRs2RJNmzbFwYMHsXLlSqxcuRIDBw60do0Oy7il457uluExQwcREVFxFQ4dzz//PFq0aIHY2FgsW7YMgwYNQnBwMHbt2oWYmBgblOi4QrxDMKnNJPi7+8Pt8sP48e/5DB1ERETFVTh0nD59Gt9++y3u3r0LjUYDSZIQFRWFnTt3IjMzEzExMfD09LRFrQ7H38Mf73d7HwCQnFw0n6GDiIiouAqHjh07dgAAzpw5g4MHD+LQoUM4ePAgpk+fjjt37kClUqF+/fo4ceKE1Yt1ZLzLLBERUekqfZfZyMhIREZGYujQoYZ5KSkpOHDgAA4fPmyV4pwJQwcREVHpKh06zImIiEBERAQGDx5szc06NL3Q407OHaSrbuPll+tCq5UQF6d0VURERI7HqqHjQdTzq57Y/OdmAMCdhDvwcfNRuCIiIiLHVKlxOqiIn7uf4TFv+kZERFQyhg4LGd/07Wb2TQUrISIicmwMHRYyvulb2r0byM0FsrMVLIiIiMhBMXRYyDAqqU6DPg17wc0N6NZN2ZqIiIgcEUOHhQyhQ1V08zteMktERFQcQ4eFDDd9kwC1Sz4Ahg4iIiJzGDosZHzTN5VGbu1g6CAiIiqOocNCxqFD0rClg4iIqCQMHRYyvnoFajltMHQQEREV5zSh49KlS3j66afh7+8PDw8PxMTE4ODBg0qXBQ8XD+wcuRMnXjqB2t7VATB0EBERmeMUw6Dfvn0b7dq1Q+fOnfHTTz+hVq1a+PPPP+Hr66t0aQCA9mHtAQBuWvk5QwcREVFxThE63nvvPYSGhuLTTz81zKtTp45yBZWg8E6zDB1ERETFOcXplfXr1yMuLg6DBw9GrVq1EBsbi6VLl5a6Tm5uLjIyMkwmW2PoICIiKplThI6//voLixcvRmRkJDZt2oTRo0dj/Pjx+Pzzz0tcJzExET4+PoYpNDTUZvUduHwAnxz+BG1HfYk1P6Zj61ab7YqIiMhpSUIIoXQRZXF1dUVcXBx2795tmDd+/HgkJydjz549ZtfJzc1Fbm6u4XlGRgZCQ0ORnp4Ob29vq9Y3ZuMYLDqwCACw//n9aBnc0qrbJyIiclQZGRnw8fEp1/erU7R0BAYGonHjxibzGjVqhAsXLpS4jlarhbe3t8lkK8aXzfL29kREROY5Reho164dTp06ZTLv9OnTCA8PV6giU8YDhPH29kREROY5Reh4+eWXsXfvXiQkJODs2bP4+uuvsWTJEowZM0bp0gAYhY4rMfh1kze++w7IylK2JiIiIkfjFKGjZcuWWLNmDVasWIGoqCi89dZbmDdvHp566imlSwNgdNO3XVPx31f7YvBg4Pp1ZWsiIiJyNE4xTgcAPPbYY3jssceULsMsQ0uHuuha2fx8hYohIiJyUE7R0uHozIUOjtVBRERkiqHDCgxXrzB0EBERlYihwwo8XTyhVWsZOoiIiErB0GEFkiQh0j8S1b3dDPMyMxUsiIiIyAExdFjJsRePYcqjowzPb99WsBgiIiIHxNBhRdWrFz1m6CAiIjLF0GFFvr5Fjxk6iIiITDF0WFFhS4ePD+D4t9EjIiKyL4YOK1lzcg0+SnscbZZ2wIajuzB5stIVERERORaGDiu5kH4BG86uw55Lu3D+znmlyyEiInI4DB1WwjvNEhERlY6hw0oMo5ICuJF1Q8FKiIiIHBNDh5UYWjq2zsS3iT0wbpyy9RARETkahg4rMdze/vdn8L+ktvjmG2XrISIicjQMHVZiaOlwlwfouH2bl80SEREZY+iwkmqu1eCicgHc7gAA8vOB7GxlayIiInIkDB1WIkmS3NrhVjQUKUclJSIiKsLQYUX+Hv6G0ysAQwcREZExhg4rGt50OOLqRhqeM3QQEREVYeiwolfbvYp+MQ8bnjN0EBERFWHosDLj29vfuaNYGURERA6HocPKeHt7IiIi8zRKF1DVRNTVo1uvXPj6CjRo4KF0OURERA6DLR1WtO/iPnTe4o7NrTwQ/Mzr6NFD6YqIiIgcB0OHFQVUC0CeLg8AkHInReFqiIiIHAtDhxUFewdDLakBAOfunFO2GCIiIgfD0GFFGpUGYT5hAOTQwXuvEBERFXHK0JGYmAhJkjBx4kSlSymmjm8dYPER3Jl5Bs1iC5Quh4iIyGE4XehITk7GkiVL0LRpU6VLMSvCNwLIqgFk18C1NL3S5RARETkMpwod9+7dw1NPPYWlS5eiuvEoXA6kjm8dw03f0u841dtLRERkU071rThmzBj07t0bXbp0KXPZ3NxcZGRkmEz2EFE9wnDTt9xsDfLy7LJbIiIih+c0oWPlypU4dOgQEhMTy7V8YmIifHx8DFNoaKiNK5TJLR13DM85FDoREZHMKUJHamoqJkyYgC+//BJubm7lWmfatGlIT083TKmpqTauUhbhG2E4vQJwKHQiIqJCTjEM+sGDB5GWloYWLVoY5ul0OuzYsQMLFy5Ebm4u1Gq1yTparRZardbepSLQKxD9m3fGmqPyc4YOIiIimVOEjkcffRTHjh0zmTdy5Eg0bNgQU6ZMKRY4lKSSVIgOC8Wav58zdBAREcmcInR4eXkhKirKZJ6npyf8/f2LzXcExhfWMHQQERHJnKJPh7MxDh3sSEpERCRzipYOc3799VelSyhRWFQqnp3+F+6pLiK4RRMAMUqXREREpDinDR2O7KrrLiyXngQE0Fo3BwwdREREPL1iExHVIwyPebdZIiIiGUOHDdTxrWN4nHInRblCiIiIHAhPr9hALY/acL0Zi7x7Hjhx1wd4UumKiIiIlMfQYQOSJCFv0V5A54rzgb9DzBGQJEnpsoiIiBTF0ys2IEmAq2cWAEBke+N61nWFKyIiIlIeQ4eNuHvlyg+yqyPlNvt1EBERMXTYiLePTn6Q64u/bp5XthgiIiIHwNBhI/5+RX04Tl66rGAlREREjoGhw0Zq1yi6w62fFFHKkkRERA8Ghg4bqRvoZ3jcoVY/BSshIiJyDAwdNsI7zRIREZli6LARhg4iIiJTDB02Yhw67t0DhBDKFUNEROQAGDpsZOhQYN7WLxCzKA6T0vyw5+IepUsiIiJSFEOHjXh6AgXaNBxJO4jbObc5QBgRET3wGDpsiLe4JyIiKsLQYUMPVX/I8Pj49eMKVkJERKQ83mXWRvLzgXVLo6BOmgtdtQtI9tugdElERESKYkuHjajVwIzpauh+exk4Ngxnb53FrexbSpdFRESkGIYOG1GpAF/fv5/kyA8OXD6gVDlERESKY+iwoaLQIQ/asf/SfsVqISIiUhpDhw0ZBgjLrg4Ihg4iInqwMXTYkCF0CA2QVw3Jl5M5MikRET2wePWKDRkPhf5p93XoHRcNSZKUK4iIiEhBDB02ZBw6mld/BDU9lauFiIhIaTy9YkOGjqTgnWaJiIicInQkJiaiZcuW8PLyQq1atfD444/j1KlTSpdVJt7enoiIqIhThI7t27djzJgx2Lt3L5KSklBQUIBu3bohMzNT6dJKFRkJPPww0LcvkKm+gE8Pf4pxP46DTq9TujQiIiK7k4QTXk5x/fp11KpVC9u3b8fDDz9crnUyMjLg4+OD9PR0eHt727jC4gZ+MxCrT64GABx/6Tga12xs9xqIiIisrSLfr07R0nG/9PR0AICfn1+Jy+Tm5iIjI8NkUlLLoJaGxxyvg4iIHkROFzqEEIiPj0f79u0RFRVV4nKJiYnw8fExTKGhoXassrhWwa0Mj5MvJStYCRERkTKcLnSMHTsWR48exYoVK0pdbtq0aUhPTzdMqampdqrQvMa+LSBBHqNj/2W2dBAR0YPHqULHuHHjsH79emzbtg0hISGlLqvVauHt7W0yKeGll4CICKBeqA/q+zUEAPx+9XfkFOQoUg8REZFSnCJ0CCEwduxYrF69Glu3bkVERITSJZXblSvAuXNAZibQSNMbAJCvz8fvV39XtjAiIiI7c4rQMWbMGHz55Zf4+uuv4eXlhatXr+Lq1avIzs5WurQyNW1a9Ljm3c6Gx8mX2a+DiIgeLE4ROhYvXoz09HR06tQJgYGBhmnVqlVKl1Ym49CBa80MD3kFCxERPWic4t4rTjiUiIFx6LieEgCXWBfk6/OxO3U3hBC8ARwRET0wnCJ0OLO6dQEPDyArC/jjmBrt+7WHSlJhWNQw6IUeakmtdIlERER2wdBhKSGAP/4AoqPNvqxWA1FRwP79wJ9/Avsf34TqPi52LpKIiEh5TtGnw6F9/718DuWFF0q8q1vhKRYhgNP/Y+AgIqIHE0OHJdLTgXHj5MfLlgGNGgHffCOnCyPNivqP4uhRO9ZHRETkQBg6LOHlBbz+uvwvAFy7BjzxBNC7N3D6tGEx486khaFDp9dha8pWrPxjpR0LJiIiUo5T3mW2Mmx6l9mLF4GxY4F164rmaTRyK8ibbyJD5YtVq+QWjyZNAK17AeovqI+UOymo7VkbF+MvQqNi9xoiInI+Vf4usw4nJARYuxZYvRoIDpbnFRQAH34I1KsH71VL8cILQKtWgKcnoFFpEBsYCwC4lnkNv577VbHSiYiI7IWhw5r69wdOnQLefBNwc5Pn3bwJJBcffXRY1DDD4xXHSr95HRERUVXA0GFtnp7AzJly+Bg2DNBqgTfeMF2moAC96/WCl6vcF+T7k98jtyBXgWKJiIjsh6HDVsLCgK+/Bs6cAUJDkZkJJCUBH3wA/PDij3Bv2QaPa+XLWtJz0/Hz2Z8VLpiIiMi2GDpsLTQUgHyn2W7dgFdeAb76Qg8cPYphi3cZFvv6yBcKFUhERGQfDB12Ur8+4OoqPz4qyS0cXf4C/LPkeT/88T3uTosHLlxQqEIiIiLbYuiwExcXoHFj+fGpvDrI2bQdLl27Y/BxeV62C/D+3g+BiAhg4EBg69Zig4wRERE5M4YOOyocJEyvl3CixsPAzz/j5SlroBHynWYXtgIy1Xr50tsnngDy8xWsloiIyLoYOuzI3Mik9Ts8jvFtXsawhx7HEY94eNYIlF94+umi8zGFkpKAe/fsUywREZGVcRhMOzIOHceOFT2e020OVNLf+e/f78otHbGxpitfvQr06CFfgtunj3w5bo8eReOBEBEROTi2dNiRuZYOAEWBA5A7fzzxhNzz1Ng33wB6PZCdLT/u3x+oVUtedsUK+eZzREREDoyhw45q15ZzAgDs3g3cuGF+ubu5d5F86b5RTNu3B0aPBvz9jRa8KweQJ58EataUr8ldvNg2xRMREVmIocPOhgyR/83KAubNM31NCIHlR5aj/sL6eGzFY8jIzSh6sXlzOVBcuQL89BMwYgTg51f0en6+3OfD+KZzhTIzrX4cREREFcXQYWdTpgDVq8uDhI0bZ/qaJEnYeGYjrt67irTMNIxcNxIF+gLThVxc5L4cy5cD164B27YBEybII6AC8mvG8vOBoCAgJgZ4+WU5lFy/bqvDIyIiKhFvba+A7GzA3d38a+funEOTRU2QlS+PGja86XAsf3y5ab8Pc4QATpyQT7MUnsMBgL17gTZtii9fv758yqZtW/n2t40bA2p1JY+IiIgeVLy1vYMrKXAAQB3fOlg9ZDVc1fLlsl8c/QIvbXwJZWZDSQKaNDENHIB8Hqd5c/l1Y6dPA598Ajz/vNzD1ceHnVGJiMim2NLhAHS64o0M6/63DgO/GQid0AEAXv7Hy/ig2weQ7g8P5XXrFrBjB7BrF/Dbb8DBg6aDj9WpA6SkmK4zbhywbx/QrBkQFSVPTZrIPWIrWwcREVUpFfl+ZehQ0J07wEcfAZ99Bhw6JDc2GFtxbAWeWv0UBOQf0ZiWY/Bel/fg6epp+c6zs4EDB+RQsX8/EBgIzJ9vukxcnBxO7ufvDzRoIE8NGwJduxYfV4SIiB4IFfl+5eBgCpo2Dfj4Y/nxwoXAa6+Zvj4sehiy8rPw/A/PAwD+k/wf/O/G//DLM79YvnN3d6BDB3kyRwggL8/8azdvytf87t4tP1epTEPHnTvApEnyfWTq1Cn6NyCA/UaIiB5gDB0KmjQJWLJEHvPrgw/kgUaNBxADgH82/yd0QofxP41Hri4Xr7Z71T7FSZI8gtndu3IH1ePHgT/+kKcTJ4BLl4qWbdjQdN3C/iL302jkK2lCQ+UpOBiYORPwNGq5MXeuiYiIqgSeXlHYM88AX3whP9Zq5bE7Ro0y0+/z5mms+986TG432WT+prOb4Kp2Rcc6Hcu+wsWa7t6Vw8WpU0CXLqYdWFeskAcsK4+8PPky4EJTp8rNPgEB8lR4NU7NmkWTvz8QHl487BARkd1V2T4dixYtwpw5c3DlyhU0adIE8+bNQ4eSTg/cx1FDR1oa0L07cORI0bxBg4ClSwFf39LXFUKg2cfNcCztGCJ8IzC86XA8EvEIWgW3grtLKZfI2FpmJnDmjNwx9dw5+d/z54HUVHkqHIq1Vi15rBFjI0fKY5CUpV8/YO1a03ldu8phyNfXdPLxAby95cnHB2jRQm5lKaTXy/+qeDEXEVFFVcnQsWrVKgwfPhyLFi1Cu3bt8H//939YtmwZTpw4gbDCgbFK4aihAwBycoDJk+U/8AuFhck3mn3lFXkwMXMOXD6AlktbFpuvUWnQPLA52oW2Q5OaTVDPr57yQcRYVhZw8SKQkSF3VjU2dSqwfr18g7vbt0vexnPPAf/9r+m86tXl/iRl+eIL+c0tdOiQHEQ8PORTPdWqyf8WPi987OEBLFggv15o/365s627u3zzPTc3ucnq/n+9veVTSsb0egYdInJ6VTJ0tG7dGs2bN8dio3uLNGrUCI8//jgSExPLXN+RQ0ehNWvk79LC702tVv7eNR7XY+1aeTiNwEDAwysXe69uw/q/VmHn5c2AJhtQ6QBVAaDOBdQ6w3p/jf8LEdUjirbzv7XYeHoj/D384eXqBS+tl+Ffd4073DRucNO4oYZHDTSo0cCkzrTMNOiFHmpJDbVKDbWkhkpSQZIkqCSV/BgSNCoN1Kqi/hn3/6qVeflvbq7cKpKWJo+impYmd2K9cUMee6R//6Jl9XqgRo3Sg4rxm9ivX9HzHTuAjh3LXg+QW1KMQ8cbbwBvv132em3bypcqG2vdWr6CyNVVnlxciv41fjx2LPDCC6Y1DB0q95ExN6nVRf9Onlw0Wi0g99NZvVp+raRJpZKD1jPPmNa7Z4/cYqVSlTxJktyKdH/npN275f46klS03P2PJUnudGw8vH92NvDXX/Jjc8sX/g5JktxZ2bg/UHq6PJlb1vixq6vpPgH5P2FBgelyhYznabWm/0GFAO7dM7+sscJ1jevV6UwvYze3TiFXV9PXdDp53/czt437g25hS19J65T3NXpgVbnQkZeXBw8PD3z77bfob/RFM2HCBBw5cgTbt28vtk5ubi5yc3MNzzMyMhAaGurQoQOQP9P/+U9gyxa5q0RSkunrHTrIQ22U6dFpQId3AQAuKhekPJ+FkKD7+w3//WEj3fcrMKo5ECDfBrdr3a54WmzGc88VvawTBYC535pq14BJIYan83vMx/4F47FyZeEcAZ1eZ2ZFAM2+gPT4Pw1PT445iQ6NG+DWrcI19dALvdlVpQEjoIpeBQAI9wnHqujD+Ec3r79XFNBDb/p5bPS5qZocBMnjFqDT4V8pfgj5YSrevPNy0bHe/xlb+DzwENT/ait/YAuB774B5v9vK3aiQ5nrSu3fg+rR6fIXBYCrCe4IKCi6z45eMvP2/r2uakRXSHV2Ajod2l8AJi7vh0H4rux6NdlQv1Zd/mLS6zFzG3Bp53+wBP8qe90G66EeOsRwrHuWAcMun8E51AEg16ov6Vh7jYOq5VJAp0P1bOC3OZFojBPlO9bxDSFVPwfodHjiD6DN6jGYiHll1+t3Bupx0YZj/e864Pvf1+FH9CpzXanlIqh6xRt+NhfnAnXuZaPg7z73pR2ratgASPV/BHQ6NL0GfPB/ndAVRf+Bix2r8e/h69UgqfIBvR6v7AbwSyLexytlH2ud7VCP6G742Wz+Angl5SCOommZ9UqPvgZV+w8AnQ5qPXD+7doIwcWS6zU+1lGtIAUcBXQ69DwDDF4xHM+hqPN4ifV6XoP6lXDDz+bDn4F9+7/ASgwtc12p6ZdQ9X/ecKzHFwEdblzDLfiVfayFnxE6HcLvAKsWtMQ/xO7yHevkIEjuNwG9Hv86CIT8+G+8KWaWfaz3f0Z8p8L8E1uwE+3LPtb7PyPe9URA3p3y1Xv/Z8Tn/TFI/43RugJ6vcv9a1ukyl0ye+PGDeh0OtSuXdtkfu3atXH16lWz6yQmJmLmzJlmX3Nk4eHAL7/I93Uz90f7lSvl286bnV5H26GdcPbWWdzOuQ2V2R/133/xlBI73TRuEHmG3/2/lfBroze96kSCBL3eeF2p5HWFyjAeCSC3guh0xuuqUNIAukIvDIOo6aEHvL3v2+d96xkdr17oAKEDVIB+yGCIBpOhe938sibP9Sp5n5K8CzFqFHQLIqH7S1PmukJAXrewrKgo6I6Usp7RPON6dRIgIEGHsvcJvdrwHkEFCAnQQ1W+dYVkcqwAoIO6XOsKIQzHqlMVrlvxY9Xb6VhF4bEa/cqU91j1en3lfjaFxwq94VhR3p+N3vRnIypQr/HPpuhYneBnU/h7KCpwrPr7fg+FvmLHavSzEaKM96mkzwi9HrpyH+t9nxH6CtZr/HuoF/eta/6PN3txitBR6P7meCFEiU3006ZNQ3x8vOF5YUuHswgMlKf7zZ0rt4ZcuSJ3icjOLppyc+Uv6YICoEl9T3Sv1x3d0R2AfEaideui7eTr8pGny0O+Tge90EGn10EndNDp9RjQ7l+oFngZOQU5aFyzMfwvm3a9OH/nHPL1+fLvuBCGsKCploGYiEeg0+sgIBDsHYy6dYvWzdfl4eztP//eijD5xycsDyHBrQz7cNO4ITa2aGT229m3cOWe+YAZHFIDPkHyToK8guDhYVpvakYq7ubeNbtu/aCm0HjIzeHhPuGoHWi67v9unITeTGOge+A1RAQVLejb5Qk0PBGC7L9b6bPyM3Huznmz+6wRqkEto3U1O3cgrnPR62mZabiRdcPsuuHBYfAMjAN0BWjYqAl8e3yIuHdy5U8pAaTcS0W2rrCFT/zdlC5BpclDw6A4IC8fyM5C4MieUIc/jLjdtwHIH8inco0SrQRALX88eIXdQ2hQnPzDyMmBx8vD0HS5DjXS5eXTRQ4uifSidVUqw7qBId6oHhQHXL4Mb1cNtC+MQNz6C4byLqvu4Y5U1CIJjQb4+5RcvaBGcPXwAq5cQd2wINTq2Bhxf6Sg8JfmtEsGCow/fbWuACS4+l1FvaA4ObXfugX/2EaIvAfE3TwLCCBH0uEvrdGdlyWV4XSFX6geAUFxcp+j7GxoHm2FFtv/hE7In/431Xm45mI0fo1GDWjkvxpDQwLhFdACOHMGkWoPeLVshLhTpwy/4+fdspGpMvrAd3U1nOpoFNQCUlY6cOkSQmrVBh5yQ1za/wrfJpz0ML5TtCT3FQLgGX4L4UFxf/eBugOvRg+hyb00uGafBADcU+twwc3o/VWrARf5WGuFuqNGUBxw9k+o8/KhadkccSdOGha9qs3DLRejG066uBh+rnWD68GtOoA//0Skqxf8G9RE3MWiFqyzHjnIUxn/bNwASYKmWjrqB8UBt24DV6+iVmAg6gblIi5dXjdfEjjjmWN0qJK8LgCf0FwEB8UBF1KBe/fg1qQeYo/8iXSd3Bn9tksBrmiNTk+pNYYr4wyfESdPIjDXBR5RDyHur6J6U93zcFdj9JeVq6vh97B+UFNoxDXg/HmEV6uO2nXcEJdWtO7/qmWbtrC4uQGQij4jrqUBN2/ANzwEDW/cRHaWvG6WWo9zHkY/G5UKcNUCMPqM+OsvICcHmpimiDtRtM80bT5uuBr9bDQu8v8d/P0ZUSsGOH0aDSUP+NavbfKzSfHIAdAcSqmyp1fu5wx9OoiIiJxNlbvhm6urK1q0aIGk+zo4JCUloW3btgpVRURERBXhNKdX4uPjMXz4cMTFxaFNmzZYsmQJLly4gNGjRytdGhEREZWD04SOJ554Ajdv3sSsWbNw5coVREVF4ccff0R4eLjSpREREVE5OEWfDmtgnw4iIiLrq3J9OoiIiMj5MXQQERGRXTB0EBERkV0wdBAREZFdMHQQERGRXTB0EBERkV0wdBAREZFdOM3gYJYqHI4kIyOjjCWJiIiovAq/V8sz7NcDEzru3pXvMupMd5olIiJyFnfv3oWPj0+pyzwwI5Lq9XpcvnwZXl5ekCSp7BXKKSMjA6GhoUhNTeVIp1bC99Q2+L7aBt9X2+D7ahu2eF+FELh79y6CgoKgUpXea+OBaelQqVQICQmx2fa9vb35H8PK+J7aBt9X2+D7aht8X23D2u9rWS0chdiRlIiIiOyCoYOIiIjsgqHDQlqtFtOnT4dWq1W6lCqD76lt8H21Db6vtsH31TaUfl8fmI6kREREpCy2dBAREZFdMHQQERGRXTB0EBERkV0wdBAREZFdMHRYYNGiRYiIiICbmxtatGiBnTt3Kl2SU0tMTETLli3h5eWFWrVq4fHHH8epU6eULqvKSUxMhCRJmDhxotKlOL1Lly7h6aefhr+/Pzw8PBATE4ODBw8qXZZTKygowOuvv46IiAi4u7ujbt26mDVrFvR6vdKlOZUdO3agT58+CAoKgiRJWLt2rcnrQgjMmDEDQUFBcHd3R6dOnXD8+HGb18XQUUmrVq3CxIkT8dprr+Hw4cPo0KEDevbsiQsXLihdmtPavn07xowZg7179yIpKQkFBQXo1q0bMjMzlS6tykhOTsaSJUvQtGlTpUtxerdv30a7du3g4uKCn376CSdOnMAHH3wAX19fpUtzau+99x4+/vhjLFy4ECdPnsTs2bMxZ84cLFiwQOnSnEpmZiaaNWuGhQsXmn199uzZmDt3LhYuXIjk5GQEBASga9euhvuU2YygSmnVqpUYPXq0ybyGDRuKqVOnKlRR1ZOWliYAiO3btytdSpVw9+5dERkZKZKSkkTHjh3FhAkTlC7JqU2ZMkW0b99e6TKqnN69e4vnnnvOZN6AAQPE008/rVBFzg+AWLNmjeG5Xq8XAQEB4t133zXMy8nJET4+PuLjjz+2aS1s6aiEvLw8HDx4EN26dTOZ361bN+zevVuhqqqe9PR0AICfn5/ClVQNY8aMQe/evdGlSxelS6kS1q9fj7i4OAwePBi1atVCbGwsli5dqnRZTq99+/bYsmULTp8+DQD4/fffsWvXLvTq1UvhyqqOlJQUXL161eQ7TKvVomPHjjb/DntgbvhmTTdu3IBOp0Pt2rVN5teuXRtXr15VqKqqRQiB+Ph4tG/fHlFRUUqX4/RWrlyJQ4cOITk5WelSqoy//voLixcvRnx8PP79739j//79GD9+PLRaLZ555hmly3NaU6ZMQXp6Oho2bAi1Wg2dTod33nkHw4YNU7q0KqPwe8rcd9j58+dtum+GDgtIkmTyXAhRbB5VztixY3H06FHs2rVL6VKcXmpqKiZMmIDNmzfDzc1N6XKqDL1ej7i4OCQkJAAAYmNjcfz4cSxevJihwwKrVq3Cl19+ia+//hpNmjTBkSNHMHHiRAQFBWHEiBFKl1elKPEdxtBRCTVq1IBarS7WqpGWllYsOVLFjRs3DuvXr8eOHTsQEhKidDlO7+DBg0hLS0OLFi0M83Q6HXbs2IGFCxciNzcXarVawQqdU2BgIBo3bmwyr1GjRvj+++8VqqhqmDx5MqZOnYqhQ4cCAKKjo3H+/HkkJiYydFhJQEAAALnFIzAw0DDfHt9h7NNRCa6urmjRogWSkpJM5iclJaFt27YKVeX8hBAYO3YsVq9eja1btyIiIkLpkqqERx99FMeOHcORI0cMU1xcHJ566ikcOXKEgaOS2rVrV+yS7tOnTyM8PFyhiqqGrKwsqFSmX01qtZqXzFpRREQEAgICTL7D8vLysH37dpt/h7Glo5Li4+MxfPhwxMXFoU2bNliyZAkuXLiA0aNHK12a0xozZgy+/vprrFu3Dl5eXoaWJB8fH7i7uytcnfPy8vIq1i/G09MT/v7+7C9jgZdffhlt27ZFQkIChgwZgv3792PJkiVYsmSJ0qU5tT59+uCdd95BWFgYmjRpgsOHD2Pu3Ll47rnnlC7Nqdy7dw9nz541PE9JScGRI0fg5+eHsLAwTJw4EQkJCYiMjERkZCQSEhLg4eGBJ5980raF2fTamCruP//5jwgPDxeurq6iefPmvLTTQgDMTp9++qnSpVU5vGTWOn744QcRFRUltFqtaNiwoViyZInSJTm9jIwMMWHCBBEWFibc3NxE3bp1xWuvvSZyc3OVLs2pbNu2zezn6YgRI4QQ8mWz06dPFwEBAUKr1YqHH35YHDt2zOZ18db2REREZBfs00FERER2wdBBREREdsHQQURERHbB0EFERER2wdBBREREdsHQQURERHbB0EFERER2wdBBREREdsHQQURERHbB0EFERER2wdBBRDYzceJEPP7442Uu9+yzz2Lq1Km2L4iIFMXQQUQ2k5ycjFatWpW6jF6vx8aNG9GvXz87VUVESmHoICKry8/Ph6urK3bv3o3XXnsNkiShdevWZpf97bffoFKpSny9U6dOGDt2LMaOHQtfX1/4+/vj9ddfh/G9Kr/77jtER0fD3d0d/v7+6NKlCzIzM21ybERUeQwdRGR1arUau3btAgAcOXIEV65cwaZNm8wuu379evTp0wcqVckfR5999hk0Gg327duHjz76CB9++CGWLVsGALhy5QqGDRuG5557DidPnsSvv/6KAQMGgDfQJnI8GqULIKKqR6VS4fLly/D390ezZs1KXXb9+vV4//33S10mNDQUH374ISRJQoMGDXDs2DF8+OGHeOGFF3DlyhUUFBRgwIABCA8PBwBER0db7ViIyHrY0kFENnH48OEyA8fJkydx8eJFdOnSpdTl/vGPf0CSJMPzNm3a4MyZM9DpdGjWrBkeffRRREdHY/DgwVi6dClu375tlWMgIuti6CAimzhy5Ei5Wjm6du0Kd3f3Su9HrVYjKSkJP/30Exo3bowFCxagQYMGSElJqfQ2icg2GDqIyCaOHTuGpk2blrrMunXr0Ldv3zK3tXfv3mLPIyMjoVarAQCSJKFdu3aYOXMmDh8+DFdXV6xZs6byxRORTbBPBxHZhF6vx9GjR3H58mV4enrCx8fH5PW0tDQkJydj7dq1ZW4rNTUV8fHxGDVqFA4dOoQFCxbggw8+AADs27cPW7ZsQbdu3VCrVi3s27cP169fR6NGjWxxWERkAbZ0EJFNvP3221i1ahWCg4Mxa9asYq//8MMPaN26NWrVqlXmtp555hlkZ2ejVatWGDNmDMaNG4d//etfAABvb2/s2LEDvXr1Qv369fH666/jgw8+QM+ePa1+TERkGUnwujIiUkDfvn3Rvn17vPrqq6Uu16lTJ8TExGDevHn2KYyIbIYtHUSkiPbt22PYsGFKl0FEdsQ+HUSkiLJaOIio6uHpFSIiIrILnl4hIiIiu2DoICIiIrtg6CAiIiK7YOggIiIiu2DoICIiIrtg6CAiIiK7YOggIiIiu2DoICIiIrtg6CAiIiK7YOggIiIiu/h/HA0pA97bSTAAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 472.441x314.961 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["a       = 0.3333\n", "\n", "gamma   = 1.5\n", "alphas   = np.array([1,5,10])\n", "\n", "def memory_kernel2(t, alpha):\n", "    return alpha * M * gamma * np.exp( - alpha * t )\n", "\n", "K = np.zeros((<PERSON><PERSON><PERSON>,3))\n", "\n", "for i,alpha in enumerate(alphas):\n", "\n", "    for k,t in tqdm(enumerate(tvalues)):\n", "        K[k,i] = memory_kernel2( t, alpha )\n", "\n", "    \n", "fig, ax = plt.subplots(1, 1,figsize=(12*in2cm, 8*in2cm))  \n", "ax.plot(tvalues, K[:,0], 'r--',    linewidth = 2,   label =r'$\\alpha = 1$')\n", "ax.plot(tvalues, K[:,1], 'g--',    linewidth = 2,   label =r'$\\alpha = 5$')\n", "ax.plot(tvalues, K[:,2], 'b--',    linewidth = 2,   label =r'$\\alpha = 10$')\n", "\n", "ax.set_xlabel(r'$t$ / ps')\n", "ax.set_ylabel(r'$K(t)$')\n", "\n", "ax.set_title('Exponential memory kernel')\n", "ax.legend()\n", "plt.subplots_adjust(left=0, bottom=0, right=1, top=1, wspace=0.3, hspace=0.3)\n", "\n", "fig.savefig('figures/memory_kernel2.png', format='png', dpi=900, bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}