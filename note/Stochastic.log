This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.7.26)  1 AUG 2025 23:47
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**Stochastic
(./Stochastic.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2025/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaTeX and XeLaTeX
\babel@savecnt=\count270
\U@D=\dimen142
\l@unhyphenated=\language90
 (/usr/local/texlive/2025/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count271
 (/usr/local/texlive/2025/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language22). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language22). Reported on input line 108.
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/babel/locale/en/babel-english.tex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 11.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
 (/usr/local/texlive/2025/texmf-dist/tex/latex/ucs/utf8x.def
File: utf8x.def 2023/11/09 UCS: Input encoding UTF-8


Package ucs Info: utf8x disabled, assuming standard utf8 processing
(ucs)             load ucs package to force utf8x processing.

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/utf8.def
File: utf8.def 2022/06/07 v1.3c UTF-8 support
Now handling font encoding OML ...
... no UTF-8 mapping file for font encoding OML
Now handling font encoding OMS ...
... processing UTF-8 mapping file for font encoding OMS
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/omsenc.dfu
File: omsenc.dfu 2022/06/07 v1.3c UTF-8 support
   defining Unicode char U+00A7 (decimal 167)
   defining Unicode char U+00B6 (decimal 182)
   defining Unicode char U+00B7 (decimal 183)
   defining Unicode char U+2020 (decimal 8224)
   defining Unicode char U+2021 (decimal 8225)
   defining Unicode char U+2022 (decimal 8226)
)
Now handling font encoding OT1 ...
... processing UTF-8 mapping file for font encoding OT1
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ot1enc.dfu
File: ot1enc.dfu 2022/06/07 v1.3c UTF-8 support
   defining Unicode char U+00A0 (decimal 160)
   defining Unicode char U+00A1 (decimal 161)
   defining Unicode char U+00A3 (decimal 163)
   defining Unicode char U+00AD (decimal 173)
   defining Unicode char U+00B8 (decimal 184)
   defining Unicode char U+00BF (decimal 191)
   defining Unicode char U+00C5 (decimal 197)
   defining Unicode char U+00C6 (decimal 198)
   defining Unicode char U+00D8 (decimal 216)
   defining Unicode char U+00DF (decimal 223)
   defining Unicode char U+00E6 (decimal 230)
   defining Unicode char U+00EC (decimal 236)
   defining Unicode char U+00ED (decimal 237)
   defining Unicode char U+00EE (decimal 238)
   defining Unicode char U+00EF (decimal 239)
   defining Unicode char U+00F8 (decimal 248)
   defining Unicode char U+0131 (decimal 305)
   defining Unicode char U+0141 (decimal 321)
   defining Unicode char U+0142 (decimal 322)
   defining Unicode char U+0152 (decimal 338)
   defining Unicode char U+0153 (decimal 339)
   defining Unicode char U+0174 (decimal 372)
   defining Unicode char U+0175 (decimal 373)
   defining Unicode char U+0176 (decimal 374)
   defining Unicode char U+0177 (decimal 375)
   defining Unicode char U+0218 (decimal 536)
   defining Unicode char U+0219 (decimal 537)
   defining Unicode char U+021A (decimal 538)
   defining Unicode char U+021B (decimal 539)
   defining Unicode char U+0237 (decimal 567)
   defining Unicode char U+2013 (decimal 8211)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+FB00 (decimal 64256)
   defining Unicode char U+FB01 (decimal 64257)
   defining Unicode char U+FB02 (decimal 64258)
   defining Unicode char U+FB03 (decimal 64259)
   defining Unicode char U+FB04 (decimal 64260)
   defining Unicode char U+FB05 (decimal 64261)
   defining Unicode char U+FB06 (decimal 64262)
)
Now handling font encoding T1 ...
... processing UTF-8 mapping file for font encoding T1
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/t1enc.dfu
File: t1enc.dfu 2022/06/07 v1.3c UTF-8 support
   defining Unicode char U+00A0 (decimal 160)
   defining Unicode char U+00A1 (decimal 161)
   defining Unicode char U+00A3 (decimal 163)
   defining Unicode char U+00AB (decimal 171)
   defining Unicode char U+00AD (decimal 173)
   defining Unicode char U+00BB (decimal 187)
   defining Unicode char U+00BF (decimal 191)
   defining Unicode char U+00C0 (decimal 192)
   defining Unicode char U+00C1 (decimal 193)
   defining Unicode char U+00C2 (decimal 194)
   defining Unicode char U+00C3 (decimal 195)
   defining Unicode char U+00C4 (decimal 196)
   defining Unicode char U+00C5 (decimal 197)
   defining Unicode char U+00C6 (decimal 198)
   defining Unicode char U+00C7 (decimal 199)
   defining Unicode char U+00C8 (decimal 200)
   defining Unicode char U+00C9 (decimal 201)
   defining Unicode char U+00CA (decimal 202)
   defining Unicode char U+00CB (decimal 203)
   defining Unicode char U+00CC (decimal 204)
   defining Unicode char U+00CD (decimal 205)
   defining Unicode char U+00CE (decimal 206)
   defining Unicode char U+00CF (decimal 207)
   defining Unicode char U+00D0 (decimal 208)
   defining Unicode char U+00D1 (decimal 209)
   defining Unicode char U+00D2 (decimal 210)
   defining Unicode char U+00D3 (decimal 211)
   defining Unicode char U+00D4 (decimal 212)
   defining Unicode char U+00D5 (decimal 213)
   defining Unicode char U+00D6 (decimal 214)
   defining Unicode char U+00D8 (decimal 216)
   defining Unicode char U+00D9 (decimal 217)
   defining Unicode char U+00DA (decimal 218)
   defining Unicode char U+00DB (decimal 219)
   defining Unicode char U+00DC (decimal 220)
   defining Unicode char U+00DD (decimal 221)
   defining Unicode char U+00DE (decimal 222)
   defining Unicode char U+00DF (decimal 223)
   defining Unicode char U+00E0 (decimal 224)
   defining Unicode char U+00E1 (decimal 225)
   defining Unicode char U+00E2 (decimal 226)
   defining Unicode char U+00E3 (decimal 227)
   defining Unicode char U+00E4 (decimal 228)
   defining Unicode char U+00E5 (decimal 229)
   defining Unicode char U+00E6 (decimal 230)
   defining Unicode char U+00E7 (decimal 231)
   defining Unicode char U+00E8 (decimal 232)
   defining Unicode char U+00E9 (decimal 233)
   defining Unicode char U+00EA (decimal 234)
   defining Unicode char U+00EB (decimal 235)
   defining Unicode char U+00EC (decimal 236)
   defining Unicode char U+00ED (decimal 237)
   defining Unicode char U+00EE (decimal 238)
   defining Unicode char U+00EF (decimal 239)
   defining Unicode char U+00F0 (decimal 240)
   defining Unicode char U+00F1 (decimal 241)
   defining Unicode char U+00F2 (decimal 242)
   defining Unicode char U+00F3 (decimal 243)
   defining Unicode char U+00F4 (decimal 244)
   defining Unicode char U+00F5 (decimal 245)
   defining Unicode char U+00F6 (decimal 246)
   defining Unicode char U+00F8 (decimal 248)
   defining Unicode char U+00F9 (decimal 249)
   defining Unicode char U+00FA (decimal 250)
   defining Unicode char U+00FB (decimal 251)
   defining Unicode char U+00FC (decimal 252)
   defining Unicode char U+00FD (decimal 253)
   defining Unicode char U+00FE (decimal 254)
   defining Unicode char U+00FF (decimal 255)
   defining Unicode char U+0100 (decimal 256)
   defining Unicode char U+0101 (decimal 257)
   defining Unicode char U+0102 (decimal 258)
   defining Unicode char U+0103 (decimal 259)
   defining Unicode char U+0104 (decimal 260)
   defining Unicode char U+0105 (decimal 261)
   defining Unicode char U+0106 (decimal 262)
   defining Unicode char U+0107 (decimal 263)
   defining Unicode char U+0108 (decimal 264)
   defining Unicode char U+0109 (decimal 265)
   defining Unicode char U+010A (decimal 266)
   defining Unicode char U+010B (decimal 267)
   defining Unicode char U+010C (decimal 268)
   defining Unicode char U+010D (decimal 269)
   defining Unicode char U+010E (decimal 270)
   defining Unicode char U+010F (decimal 271)
   defining Unicode char U+0110 (decimal 272)
   defining Unicode char U+0111 (decimal 273)
   defining Unicode char U+0112 (decimal 274)
   defining Unicode char U+0113 (decimal 275)
   defining Unicode char U+0114 (decimal 276)
   defining Unicode char U+0115 (decimal 277)
   defining Unicode char U+0116 (decimal 278)
   defining Unicode char U+0117 (decimal 279)
   defining Unicode char U+0118 (decimal 280)
   defining Unicode char U+0119 (decimal 281)
   defining Unicode char U+011A (decimal 282)
   defining Unicode char U+011B (decimal 283)
   defining Unicode char U+011C (decimal 284)
   defining Unicode char U+011D (decimal 285)
   defining Unicode char U+011E (decimal 286)
   defining Unicode char U+011F (decimal 287)
   defining Unicode char U+0120 (decimal 288)
   defining Unicode char U+0121 (decimal 289)
   defining Unicode char U+0122 (decimal 290)
   defining Unicode char U+0123 (decimal 291)
   defining Unicode char U+0124 (decimal 292)
   defining Unicode char U+0125 (decimal 293)
   defining Unicode char U+0128 (decimal 296)
   defining Unicode char U+0129 (decimal 297)
   defining Unicode char U+012A (decimal 298)
   defining Unicode char U+012B (decimal 299)
   defining Unicode char U+012C (decimal 300)
   defining Unicode char U+012D (decimal 301)
   defining Unicode char U+012E (decimal 302)
   defining Unicode char U+012F (decimal 303)
   defining Unicode char U+0130 (decimal 304)
   defining Unicode char U+0131 (decimal 305)
   defining Unicode char U+0132 (decimal 306)
   defining Unicode char U+0133 (decimal 307)
   defining Unicode char U+0134 (decimal 308)
   defining Unicode char U+0135 (decimal 309)
   defining Unicode char U+0136 (decimal 310)
   defining Unicode char U+0137 (decimal 311)
   defining Unicode char U+0139 (decimal 313)
   defining Unicode char U+013A (decimal 314)
   defining Unicode char U+013B (decimal 315)
   defining Unicode char U+013C (decimal 316)
   defining Unicode char U+013D (decimal 317)
   defining Unicode char U+013E (decimal 318)
   defining Unicode char U+0141 (decimal 321)
   defining Unicode char U+0142 (decimal 322)
   defining Unicode char U+0143 (decimal 323)
   defining Unicode char U+0144 (decimal 324)
   defining Unicode char U+0145 (decimal 325)
   defining Unicode char U+0146 (decimal 326)
   defining Unicode char U+0147 (decimal 327)
   defining Unicode char U+0148 (decimal 328)
   defining Unicode char U+014A (decimal 330)
   defining Unicode char U+014B (decimal 331)
   defining Unicode char U+014C (decimal 332)
   defining Unicode char U+014D (decimal 333)
   defining Unicode char U+014E (decimal 334)
   defining Unicode char U+014F (decimal 335)
   defining Unicode char U+0150 (decimal 336)
   defining Unicode char U+0151 (decimal 337)
   defining Unicode char U+0152 (decimal 338)
   defining Unicode char U+0153 (decimal 339)
   defining Unicode char U+0154 (decimal 340)
   defining Unicode char U+0155 (decimal 341)
   defining Unicode char U+0156 (decimal 342)
   defining Unicode char U+0157 (decimal 343)
   defining Unicode char U+0158 (decimal 344)
   defining Unicode char U+0159 (decimal 345)
   defining Unicode char U+015A (decimal 346)
   defining Unicode char U+015B (decimal 347)
   defining Unicode char U+015C (decimal 348)
   defining Unicode char U+015D (decimal 349)
   defining Unicode char U+015E (decimal 350)
   defining Unicode char U+015F (decimal 351)
   defining Unicode char U+0160 (decimal 352)
   defining Unicode char U+0161 (decimal 353)
   defining Unicode char U+0162 (decimal 354)
   defining Unicode char U+0163 (decimal 355)
   defining Unicode char U+0164 (decimal 356)
   defining Unicode char U+0165 (decimal 357)
   defining Unicode char U+0168 (decimal 360)
   defining Unicode char U+0169 (decimal 361)
   defining Unicode char U+016A (decimal 362)
   defining Unicode char U+016B (decimal 363)
   defining Unicode char U+016C (decimal 364)
   defining Unicode char U+016D (decimal 365)
   defining Unicode char U+016E (decimal 366)
   defining Unicode char U+016F (decimal 367)
   defining Unicode char U+0170 (decimal 368)
   defining Unicode char U+0171 (decimal 369)
   defining Unicode char U+0172 (decimal 370)
   defining Unicode char U+0173 (decimal 371)
   defining Unicode char U+0174 (decimal 372)
   defining Unicode char U+0175 (decimal 373)
   defining Unicode char U+0176 (decimal 374)
   defining Unicode char U+0177 (decimal 375)
   defining Unicode char U+0178 (decimal 376)
   defining Unicode char U+0179 (decimal 377)
   defining Unicode char U+017A (decimal 378)
   defining Unicode char U+017B (decimal 379)
   defining Unicode char U+017C (decimal 380)
   defining Unicode char U+017D (decimal 381)
   defining Unicode char U+017E (decimal 382)
   defining Unicode char U+01C4 (decimal 452)
   defining Unicode char U+01C5 (decimal 453)
   defining Unicode char U+01C6 (decimal 454)
   defining Unicode char U+01C7 (decimal 455)
   defining Unicode char U+01C8 (decimal 456)
   defining Unicode char U+01C9 (decimal 457)
   defining Unicode char U+01CA (decimal 458)
   defining Unicode char U+01CB (decimal 459)
   defining Unicode char U+01CC (decimal 460)
   defining Unicode char U+01CD (decimal 461)
   defining Unicode char U+01CE (decimal 462)
   defining Unicode char U+01CF (decimal 463)
   defining Unicode char U+01D0 (decimal 464)
   defining Unicode char U+01D1 (decimal 465)
   defining Unicode char U+01D2 (decimal 466)
   defining Unicode char U+01D3 (decimal 467)
   defining Unicode char U+01D4 (decimal 468)
   defining Unicode char U+01E2 (decimal 482)
   defining Unicode char U+01E3 (decimal 483)
   defining Unicode char U+01E6 (decimal 486)
   defining Unicode char U+01E7 (decimal 487)
   defining Unicode char U+01E8 (decimal 488)
   defining Unicode char U+01E9 (decimal 489)
   defining Unicode char U+01EA (decimal 490)
   defining Unicode char U+01EB (decimal 491)
   defining Unicode char U+01F0 (decimal 496)
   defining Unicode char U+01F4 (decimal 500)
   defining Unicode char U+01F5 (decimal 501)
   defining Unicode char U+0218 (decimal 536)
   defining Unicode char U+0219 (decimal 537)
   defining Unicode char U+021A (decimal 538)
   defining Unicode char U+021B (decimal 539)
   defining Unicode char U+0232 (decimal 562)
   defining Unicode char U+0233 (decimal 563)
   defining Unicode char U+0237 (decimal 567)
   defining Unicode char U+02D9 (decimal 729)
   defining Unicode char U+02DB (decimal 731)
   defining Unicode char U+1E02 (decimal 7682)
   defining Unicode char U+1E03 (decimal 7683)
   defining Unicode char U+1E0D (decimal 7693)
   defining Unicode char U+1E1E (decimal 7710)
   defining Unicode char U+1E1F (decimal 7711)
   defining Unicode char U+1E25 (decimal 7717)
   defining Unicode char U+1E30 (decimal 7728)
   defining Unicode char U+1E31 (decimal 7729)
   defining Unicode char U+1E37 (decimal 7735)
   defining Unicode char U+1E8E (decimal 7822)
   defining Unicode char U+1E8F (decimal 7823)
   defining Unicode char U+1E43 (decimal 7747)
   defining Unicode char U+1E45 (decimal 7749)
   defining Unicode char U+1E47 (decimal 7751)
   defining Unicode char U+1E5B (decimal 7771)
   defining Unicode char U+1E63 (decimal 7779)
   defining Unicode char U+1E6D (decimal 7789)
   defining Unicode char U+1E90 (decimal 7824)
   defining Unicode char U+1E91 (decimal 7825)
   defining Unicode char U+1E9E (decimal 7838)
   defining Unicode char U+1EF2 (decimal 7922)
   defining Unicode char U+1EF3 (decimal 7923)
   defining Unicode char U+200C (decimal 8204)
   defining Unicode char U+2010 (decimal 8208)
   defining Unicode char U+2011 (decimal 8209)
   defining Unicode char U+2012 (decimal 8210)
   defining Unicode char U+2013 (decimal 8211)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2015 (decimal 8213)
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201A (decimal 8218)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+201E (decimal 8222)
   defining Unicode char U+2030 (decimal 8240)
   defining Unicode char U+2031 (decimal 8241)
   defining Unicode char U+2039 (decimal 8249)
   defining Unicode char U+203A (decimal 8250)
   defining Unicode char U+2423 (decimal 9251)
   defining Unicode char U+1E20 (decimal 7712)
   defining Unicode char U+1E21 (decimal 7713)
   defining Unicode char U+FB00 (decimal 64256)
   defining Unicode char U+FB01 (decimal 64257)
   defining Unicode char U+FB02 (decimal 64258)
   defining Unicode char U+FB03 (decimal 64259)
   defining Unicode char U+FB04 (decimal 64260)
   defining Unicode char U+FB05 (decimal 64261)
   defining Unicode char U+FB06 (decimal 64262)
)
Now handling font encoding TS1 ...
... processing UTF-8 mapping file for font encoding TS1
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ts1enc.dfu
File: ts1enc.dfu 2022/06/07 v1.3c UTF-8 support
   defining Unicode char U+00A2 (decimal 162)
   defining Unicode char U+00A3 (decimal 163)
   defining Unicode char U+00A4 (decimal 164)
   defining Unicode char U+00A5 (decimal 165)
   defining Unicode char U+00A6 (decimal 166)
   defining Unicode char U+00A7 (decimal 167)
   defining Unicode char U+00A8 (decimal 168)
   defining Unicode char U+00A9 (decimal 169)
   defining Unicode char U+00AA (decimal 170)
   defining Unicode char U+00AC (decimal 172)
   defining Unicode char U+00AE (decimal 174)
   defining Unicode char U+00AF (decimal 175)
   defining Unicode char U+00B0 (decimal 176)
   defining Unicode char U+00B1 (decimal 177)
   defining Unicode char U+00B2 (decimal 178)
   defining Unicode char U+00B3 (decimal 179)
   defining Unicode char U+00B4 (decimal 180)
   defining Unicode char U+00B5 (decimal 181)
   defining Unicode char U+00B6 (decimal 182)
   defining Unicode char U+00B7 (decimal 183)
   defining Unicode char U+00B9 (decimal 185)
   defining Unicode char U+00BA (decimal 186)
   defining Unicode char U+00BC (decimal 188)
   defining Unicode char U+00BD (decimal 189)
   defining Unicode char U+00BE (decimal 190)
   defining Unicode char U+00D7 (decimal 215)
   defining Unicode char U+00F7 (decimal 247)
   defining Unicode char U+0192 (decimal 402)
   defining Unicode char U+02C7 (decimal 711)
   defining Unicode char U+02D8 (decimal 728)
   defining Unicode char U+02DD (decimal 733)
   defining Unicode char U+0E3F (decimal 3647)
   defining Unicode char U+2016 (decimal 8214)
   defining Unicode char U+2020 (decimal 8224)
   defining Unicode char U+2021 (decimal 8225)
   defining Unicode char U+2022 (decimal 8226)
   defining Unicode char U+2030 (decimal 8240)
   defining Unicode char U+2031 (decimal 8241)
   defining Unicode char U+203B (decimal 8251)
   defining Unicode char U+203D (decimal 8253)
   defining Unicode char U+2044 (decimal 8260)
   defining Unicode char U+204E (decimal 8270)
   defining Unicode char U+2052 (decimal 8274)
   defining Unicode char U+20A1 (decimal 8353)
   defining Unicode char U+20A4 (decimal 8356)
   defining Unicode char U+20A6 (decimal 8358)
   defining Unicode char U+20A9 (decimal 8361)
   defining Unicode char U+20AB (decimal 8363)
   defining Unicode char U+20AC (decimal 8364)
   defining Unicode char U+20B1 (decimal 8369)
   defining Unicode char U+2103 (decimal 8451)
   defining Unicode char U+2116 (decimal 8470)
   defining Unicode char U+2117 (decimal 8471)
   defining Unicode char U+211E (decimal 8478)
   defining Unicode char U+2120 (decimal 8480)
   defining Unicode char U+2122 (decimal 8482)
   defining Unicode char U+2126 (decimal 8486)
   defining Unicode char U+2127 (decimal 8487)
   defining Unicode char U+212E (decimal 8494)
   defining Unicode char U+2190 (decimal 8592)
   defining Unicode char U+2191 (decimal 8593)
   defining Unicode char U+2192 (decimal 8594)
   defining Unicode char U+2193 (decimal 8595)
   defining Unicode char U+2329 (decimal 9001)
   defining Unicode char U+3008 (decimal 12296)
   defining Unicode char U+232A (decimal 9002)
   defining Unicode char U+3009 (decimal 12297)
   defining Unicode char U+2422 (decimal 9250)
   defining Unicode char U+25E6 (decimal 9702)
   defining Unicode char U+25EF (decimal 9711)
   defining Unicode char U+266A (decimal 9834)
   defining Unicode char U+27E8 (decimal 10216)
   defining Unicode char U+27E9 (decimal 10217)
   defining Unicode char U+FEFF (decimal 65279)
)
Now handling font encoding OMX ...
... no UTF-8 mapping file for font encoding OMX
Now handling font encoding U ...
... no UTF-8 mapping file for font encoding U
   defining Unicode char U+00A9 (decimal 169)
   defining Unicode char U+00AA (decimal 170)
   defining Unicode char U+00AE (decimal 174)
   defining Unicode char U+00BA (decimal 186)
   defining Unicode char U+02C6 (decimal 710)
   defining Unicode char U+02DC (decimal 732)
   defining Unicode char U+200C (decimal 8204)
   defining Unicode char U+2026 (decimal 8230)
   defining Unicode char U+2122 (decimal 8482)
   defining Unicode char U+2423 (decimal 9251)
   defining Unicode char U+FEFF (decimal 65279)
))) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (./scribe.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count272
\Gm@cntv=\count273
\c@Gm@tempcnt=\count274
\Gm@bindingoffset=\dimen143
\Gm@wd@mp=\dimen144
\Gm@odd@mp=\dimen145
\Gm@even@mp=\dimen146
\Gm@layoutwidth=\dimen147
\Gm@layoutheight=\dimen148
\Gm@layouthoffset=\dimen149
\Gm@layoutvoffset=\dimen150
\Gm@dimlist=\toks20
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks21
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks22
\ex@=\dimen151
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen152
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count275
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count276
\leftroot@=\count277
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count278
\DOTSCASE@=\count279
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen153
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count280
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count281
\dotsspace@=\muskip17
\c@parentequation=\count282
\dspbrk@lvl=\count283
\tag@help=\toks23
\row@=\count284
\column@=\count285
\maxfields@=\count286
\andhelp@=\toks24
\eqnshift@=\dimen154
\alignsep@=\dimen155
\tagshift@=\dimen156
\tagwidth@=\dimen157
\totwidth@=\dimen158
\lineht@=\dimen159
\@envbody=\toks25
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks26
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks27
\thm@bodyfont=\toks28
\thm@headfont=\toks29
\thm@notefont=\toks30
\thm@headpunct=\toks31
\thm@preskip=\skip54
\thm@postskip=\skip55
\thm@headsep=\skip56
\dth@everypar=\toks32
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/latexsym.sty
Package: latexsym 1998/08/17 v2.2e Standard LaTeX package (lasy symbols)
\symlasy=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `lasy' in version `bold'
(Font)                  U/lasy/m/n --> U/lasy/b/n on input line 52.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/epsfig.sty
Package: epsfig 2024/01/14 v1.7b (e)psfig emulation (SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen160
\Gin@req@width=\dimen161
)
\epsfxsize=\dimen162
\epsfysize=\dimen163
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup7
\symboldletters=\mathgroup8
\symboldsymbols=\mathgroup9
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
\symboldlasy=\mathgroup10
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+ptm on input line 116.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/t1ptm.fd
File: t1ptm.fd 2001/06/04 font definitions for T1/ptm.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count287
\lst@gtempboxa=\box54
\lst@token=\toks33
\lst@length=\count288
\lst@currlwidth=\dimen164
\lst@column=\count289
\lst@pos=\count290
\lst@lostspace=\dimen165
\lst@width=\dimen166
\lst@newlines=\count291
\lst@lineno=\count292
\lst@maxwidth=\dimen167
 (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count293
\lst@skipnumbers=\count294
\lst@framebox=\box55
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstlang1.sty
File: lstlang1.sty 2024/09/23 1.10c listings language file
)
\c@theorem=\count295
 (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
\headerwidth=\dimen168
) (/usr/local/texlive/2025/texmf-dist/tex/latex/nomencl/nomencl.sty
Package: nomencl 2021/11/10 v5.6 Nomenclature package
 (/usr/local/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks34
\XKV@tempa@toks=\toks35
)
\XKV@depth=\count296
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/tocbasic.sty
Package: tocbasic 2024/10/24 v3.43 KOMA-Script package (handling toc-files)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/scrbase.sty
Package: scrbase 2024/10/24 v3.43 KOMA-Script package (KOMA-Script-independent basics and keyval usage)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2024/10/24 v3.43 KOMA-Script package (file load hooks)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2024/10/24 v3.43 KOMA-Script package (using LaTeX hooks)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2024/10/24 v3.43 KOMA-Script package (logo)
)))
Applying: [2021/05/01] Usage of raw or classic option list on input line 254.
Already applied: [0000/00/00] Usage of raw or classic option list on input line 370.
)
\scr@dte@tocline@numberwidth=\skip57
\scr@dte@tocline@numbox=\box56
)
Package tocbasic Info: setting babel extension for `nlo' on input line 187.
Package tocbasic Info: setting babel extension for `nls' on input line 188.
\nomlabelwidth=\dimen169
\nom@tempdim=\dimen170
\nomitemsep=\skip58
)
\@nomenclaturefile=\write3
\openout3 = `Stochastic.nlo'.

Package nomencl Info: Writing nomenclature file Stochastic.nlo on input line 8.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count297
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count298
) (/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen171
\Hy@linkcounter=\count299
\Hy@pagecounter=\count300
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count301
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count302
 (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen172
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count303
\Field@Width=\dimen173
\Fld@charsize=\dimen174
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring ON on input line 6060.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count304
\c@Item=\count305
\c@Hfootnote=\count306
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count307
\c@bookmark@seq@number=\count308
 (/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip59
) (/usr/local/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2024/10/04 v1.31 mathematical typesetting tools
 (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count309
\calc@Bcount=\count310
\calc@Adimen=\dimen175
\calc@Bdimen=\dimen176
\calc@Askip=\skip60
\calc@Bskip=\skip61
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count311
\calc@Cskip=\skip62
) (/usr/local/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count312
\l_MT_multwidth_dim=\dimen177
\origjot=\skip63
\l_MT_shortvdotswithinadjustabove_dim=\dimen178
\l_MT_shortvdotswithinadjustbelow_dim=\dimen179
\l_MT_above_intertext_sep=\dimen180
\l_MT_below_intertext_sep=\dimen181
\l_MT_above_shortintertext_sep=\dimen182
\l_MT_below_shortintertext_sep=\dimen183
\xmathstrut@box=\box57
\xmathstrut@dim=\dimen184
) (/usr/local/texlive/2025/texmf-dist/tex/latex/bbm-macros/bbm.sty
Package: bbm 1999/03/15 V 1.2 provides fonts for set symbols - TH
LaTeX Font Info:    Overwriting math alphabet `\mathbbm' in version `bold'
(Font)                  U/bbm/m/n --> U/bbm/bx/n on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathbbmss' in version `bold'
(Font)                  U/bbmss/m/n --> U/bbmss/bx/n on input line 35.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip64
\bibsep=\skip65
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count313
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count314
\float@exts=\toks36
\float@box=\box58
\@float@everytoks=\toks37
\@floatcapt=\box59
)
\@float@every@algorithm=\toks38
\c@algorithm=\count315
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count316
\c@ALG@rem=\count317
\c@ALG@nested=\count318
\ALG@tlm=\skip66
\ALG@thistlm=\skip67
\c@ALG@Lnr=\count319
\c@ALG@blocknr=\count320
\c@ALG@storecount=\count321
\c@ALG@tmpcounter=\count322
\ALG@tmplength=\skip68
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

Document Style - pseudocode environments for use with the `algorithmicx' style
) (/usr/local/texlive/2025/texmf-dist/tex/latex/wrapfig/wrapfig.sty
\wrapoverhang=\dimen185
\WF@size=\dimen186
\c@WF@wrappedlines=\count323
\WF@box=\box60
\WF@everypar=\toks39
Package: wrapfig 2003/01/31  v 3.6
) (/usr/local/texlive/2025/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
) (/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip69
\enit@outerparindent=\dimen187
\enit@toks=\toks40
\enit@inbox=\box61
\enit@count@id=\count324
\enitdp@description=\count325
) (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count326
\l__pdf_internal_box=\box62
) (./Stochastic.aux)
\openout1 = `Stochastic.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 33.
LaTeX Font Info:    ... okay on input line 33.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count327
\scratchdimen=\dimen188
\scratchbox=\box63
\nofMPsegments=\count328
\nofMParguments=\count329
\everyMPshowfont=\toks41
\MPscratchCnt=\count330
\MPscratchDim=\dimen189
\MPnumerator=\count331
\makeMPintoPDFobject=\count332
\everyMPtoPDFconversion=\toks42
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
\c@lstlisting=\count333
Package tocbasic Info: setting babel extension for `lol' on input line 33.
Package hyperref Info: Link coloring ON on input line 33.
 (./Stochastic.out) (./Stochastic.out)
\@outlinefile=\write4
\openout4 = `Stochastic.out'.

 (./Stochastic.toc
LaTeX Font Info:    Trying to load font information for U+msa on input line 3.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 3.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+lasy on input line 3.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ulasy.fd
File: ulasy.fd 1998/08/17 v2.2e LaTeX symbol font definitions
)
LaTeX Font Info:    Font shape `U/lasy/b/n' in size <8> not available
(Font)              Font shape `U/lasy/m/n' tried instead on input line 3.
LaTeX Font Info:    Font shape `U/lasy/b/n' in size <6> not available
(Font)              Font shape `U/lasy/m/n' tried instead on input line 3.
)
\tf@toc=\write5
\openout5 = `Stochastic.toc'.



[1

{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/base/8r.enc}]

[2]
LaTeX Font Info:    Trying to load font information for U+euf on input line 158.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/ueuf.fd
File: ueuf.fd 2013/01/14 v3.01 Euler Fraktur
)

[3]

[4]

[5]

[6]

[7]

[8]

[9]

[10]

[11]

[12]

[13]

[14] (./Stochastic.bbl

Package natbib Warning: Empty `thebibliography' environment on input line 8.

)

[15] (./Stochastic.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
Package rerunfilecheck Info: File `Stochastic.out' has not changed.
(rerunfilecheck)             Checksum: 4305134838B2582FFC16740D627C411C;3107.
 ) 
Here is how much of TeX's memory you used:
 16841 strings out of 473190
 266079 string characters out of 5715800
 747357 words of memory out of 5000000
 39638 multiletter control sequences out of 15000+600000
 590867 words of font info for 87 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 110i,21n,107p,543b,540s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/euler/eufm10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/latxfont/line10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/symbols/msam10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on Stochastic.pdf (15 pages, 258382 bytes).
PDF statistics:
 409 PDF objects out of 1000 (max. 8388607)
 352 compressed objects within 4 object streams
 146 named destinations out of 1000 (max. 500000)
 153 words of extra memory for PDF output out of 10000 (max. 10000000)

