\documentclass[a4paper, 11pt]{article}
\usepackage[english]{babel}
\usepackage[utf8x]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{scribe}
\usepackage{listings}
\usepackage{nomencl}
\makenomenclature
\usepackage{color}
\usepackage[colorlinks, linkcolor=blue, anchorcolor=green, citecolor=magenta]{hyperref}
\usepackage{mathtools}
\usepackage{amsmath}
\usepackage{bbm}
\usepackage{natbib}
\usepackage{algorithm, algorithmicx, algpseudocode}
\usepackage{wrapfig} % wrap figure in texts

\usepackage{cancel}
\renewcommand{\CancelColor}{\color{red}}

% \usepackage{amsmath}
% \newtheorem{theorem}{Theorem}
% \newtheorem{lemma}{Lemma}
% \newtheorem{proof}{Proof}[section]

% \usepackage{enumerate}[itemindent=4em] % narrow down the space before enumate
\usepackage{enumitem}
\setlength{\abovecaptionskip}{-.5cm} % skip blanck space before caption
\numberwithin{equation}{section} % number equation with section

\lstset{style=mystyle}

\begin{document}

%#############################################################
%#############################################################
\Scribe{Shen Fang}
\Lecturer{Dr. Luca Donati}
\LectureDate{\today}
\LectureTitle{Stochastic and Diffusive Processes}
\MakeScribeTop
\setlength{\abovedisplayskip}{5pt}
\setlength{\belowdisplayskip}{5pt}
\tableofcontents


%#############################################################
%#############################################################
\newpage


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Brownian Motion}

\subsection{Einstein's theory}
Consider the $n$ particles moves in $x$-coordinate as $\Delta$ in interval $\tau$, the number $\mathrm{d}n$ of particles experiencing a displacement between $\Delta$ and $\Delta + \mathrm{d}\Delta$ is
\begin{equation}
\mathrm{d}n = n \varphi(\Delta) \mathrm{d}\Delta 
\end{equation}
the probability $\varphi(\Delta)$ has the properties that (1) $\varphi(-\Delta) = \varphi(\Delta)$ and (2) $\int_{-\infty}^{\infty} \varphi(\Delta) \mathrm{d}\Delta = 1$. Denote $f(x,t)$ as the density of particles, we have
\begin{equation}
f(x, t+\tau) = \langle f(x+\Delta, t) \rangle_\varphi
= \int_{-\infty}^{\infty} \underbrace{f(x+\Delta, t)}_{\color{red}{\mathrm{Markovianity}}} \varphi(\Delta) \mathrm{d}\Delta
\end{equation}
We can now derive the diffusion equation by taking the Taylor's expansion
\begin{equation}
\begin{aligned}
f(x, t) + \frac{\partial f}{\partial t} \tau 
&= \int_{-\infty}^{\infty} \left[ f(x, t) + \frac{\partial f}{\partial x} \Delta + \frac{1}{2} \frac{\partial^2 f}{\partial x^2} \Delta^2 + \cdots \right] \varphi(\Delta) \mathrm{d}\Delta \\
&\approx f(x, t) + \frac{\partial f}{\partial x} \underbrace{\int_{-\infty}^{\infty} \Delta \varphi(\Delta) \mathrm{d}\Delta}_{\color{red}{=0}} + \frac{1}{2} \frac{\partial^2 f}{\partial x^2} \underbrace{\int_{-\infty}^{\infty} \Delta^2 \varphi(\Delta) \mathrm{d}\Delta}_{\color{red}{=2D\tau}} \\
&= f(x, t) + D \frac{\partial^2 f}{\partial x^2} \tau
\end{aligned}
\end{equation}
The resulting diffusion equation reads
\begin{equation}
\frac{\partial f}{\partial t} = D \frac{\partial^2 f}{\partial x^2}
\end{equation}
which can be expressed as the Fick's law
\begin{equation}
\frac{\partial f}{\partial t} = -\frac{\partial J}{\partial x}, 
J = -D \frac{\partial f}{\partial x}
\end{equation}


\subsection{Solutions of diffusion equations}
We consider solutions of diffusion equations with two different boundary conditions (BC). They share the same initial condition as
\begin{equation}
f(x, 0) = \delta(x-x_0)
\end{equation}

Firstly, we consider the Neumann BC as
\begin{equation}
\left\{ \begin{aligned}
& \left. J \right|_{x=\pm L/2} 
= -D \left. \frac{\partial f}{\partial x} \right|_{x=\pm L/2} = 0 \\
& f\left(-\frac{L}{2},t\right) = f\left(+\frac{L}{2},t\right)
\end{aligned} \right.
\end{equation}
We solve the equation with \textcolor{red}{variable separation} method.
\begin{proof}
The Ansatz of the solution is $f(x,t) = X(x) T(t)$. The diffusion equation reads
\begin{equation}
\begin{aligned}
X(x) T'(t) = D X''(x) T(t) &\Rightarrow 
\frac{T'(t)}{T(t)} = D \frac{X''(x)}{X(x)} = -k^2 \\
&\Rightarrow 
\left\{ \begin{aligned}
T'(t) + k^2 D T(t) = 0 \\
X''(x) + k^2 X(x) = 0
\end{aligned} \right.
\end{aligned}
\end{equation}
(1) For the spatial ODE, we have the general solution as $X(x) = A \cos(kx) + B \sin(kx)$. The BC reads
\begin{equation}
X'(x) = \left. -Ak \sin(kx) + Bk \cos(kx) \right|_{x=\pm L/2} = 0 
\Rightarrow B = 0, k = \frac{2n\pi}{L} \quad n = 0, 1, 2, \cdots 
\end{equation}
(2) for the temporal ODE, we have the general solution as $T_n(t) = C \exp(-k_n^2 D t)$. Integrating the wave number, we have
\begin{equation}
T_n(t) = C \exp\left(-\frac{4\pi^2 n^2}{L^2}D t\right)
\end{equation}
(3) The general solution for the diffusion equation reads
\begin{equation}
f(x,t) = \sum_{n=0}^\infty A_n \exp\left(-\frac{4\pi^2 n^2}{L^2}D t\right) \cos\left(\frac{2n\pi}{L}x\right)
\end{equation}
(4) The initial condition reads
\begin{equation}
f(x, 0) = \delta(x-x_0) = \sum_{n=0}^\infty A_n \cos\left(\frac{2n\pi}{L}x_0\right)
\end{equation}
Use the \textcolor{red}{orthogonality} of the cosine function. For $n = 0$, we have
\begin{equation}
\int_{-L/2}^{L/2} A_0 \cos(0x) \cos(0x) \mathrm{d}x = \int_{-L/2}^{L/2} \delta(x-x_0) \cos(0x) \mathrm{d}x 
\rightarrow A_0 L = 1 \rightarrow A_0 = \frac{1}{L}
\end{equation}
For $n \neq 0$, we have
\begin{equation}
\begin{aligned}
& \int_{-L/2}^{L/2} A_n \cos\left(\frac{2n\pi}{L}x\right) \cos\left(\frac{2n\pi}{L}x\right) \mathrm{d}x = \int_{-L/2}^{L/2} \delta(x-x_0) \cos\left(\frac{2n\pi}{L}x\right) \mathrm{d}x \\
& \rightarrow A_n \frac{L}{2} = \cos\left(\frac{2n\pi}{L}x_0\right) \rightarrow A_n = \frac{2}{L} \cos\left(\frac{2n\pi}{L}x_0\right)
\end{aligned}
\end{equation}
The final solution reads
\begin{equation}
f(x,t) = \frac{1}{L} + \frac{2}{L} \sum_{n=1}^\infty \cos\left(\frac{2n\pi}{L}x\right) \exp\left(-\frac{4\pi^2 n^2}{L^2}D t\right) \cos\left(\frac{2n\pi}{L}x_0\right)
\end{equation}
\end{proof}

Next we consider the zero BC as
\begin{equation}
\lim_{x\rightarrow \pm \infty} f(x,t) = 0
\end{equation}
We solve this equation with \textcolor{red}{Fourier transform} method\dots
\begin{proof}
Use the Fourier tranform on the diffusion equation
\begin{equation}
\left\{ \begin{aligned}
& \mathfrak{F}\left[\frac{\partial f}{\partial t}\right] = \frac{\mathrm{d}F(k, t)}{\mathrm{d}t} \\
& \mathfrak{F}\left[\frac{\partial^2 f}{\partial x^2}\right] = -k^2 F(k, t)
\end{aligned} \right. 
\Rightarrow \frac{\mathrm{d}F(k, t)}{\mathrm{d}t} = -k^2 D F(k, t)
\end{equation}
The general solution reads
\begin{equation}
F(k, t) = A(k) \exp(-k^2 D t)
\end{equation}
Integrating into the IC, we have
\begin{equation}
A(k) = F(k, 0) = \mathfrak{F}\left[\delta(x-x_0)\right] = \int_{-\infty}^{\infty} \delta(x-x_0) \exp(-ikx) \mathrm{d}x = \exp(-ikx_0)
\end{equation}
The solution in Fourier space reads
\begin{equation}
F(k, t) = \exp(-ikx_0) \exp(-k^2 D t)
\end{equation}
The solution in real space reads
\begin{equation}
\begin{aligned}
f(x,t) &= \mathfrak{F}^{-1}\left[F(k, t)\right] \\
&= \frac{1}{2\pi} \int_{-\infty}^{\infty} \exp(-ikx_0) \exp(-k^2 D t) \exp(ikx) \mathrm{d}k \\
&= \frac{1}{2\pi} \int_{-\infty}^{\infty} \exp \left[ -Dt \left( k^2 - \frac{ik}{Dt}(x-x_0) - \frac{(x-x_0)^2}{4 D^2 t^2} + \frac{(x-x_0)^2}{4 D^2 t^2} \right) \right] \\
&= \frac{1}{2\pi} \int_{-\infty}^{\infty} \exp \left[ -Dt \left( k - \underbrace{\frac{i(x-x_0)}{2Dt}}_{\color{red}{k_0}} \right)^2 - \frac{(x-x_0)^2}{4Dt} \right] \mathrm{d}k \\
&= \frac{1}{2\pi} \exp \left[ - \frac{(x-x_0)^2}{4Dt} \right] \underbrace{\int_{-\infty}^{\infty} \exp \left[ -Dt \left( k - k_0 \right)^2 \right] \mathrm{d}k}_{\color{red}{\int_\infty^\infty e^{-ay^2} \mathrm{d}y = \sqrt{\frac{\pi}{a}}}} \\
&= \frac{1}{2\pi} \exp \left[ - \frac{(x-x_0)^2}{4Dt} \right] \sqrt{\frac{\pi}{Dt}} \\
&= \frac{1}{\sqrt{4\pi D t}} \exp\left(-\frac{(x-x_0)^2}{4Dt}\right)
\end{aligned}
\end{equation}
\end{proof}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Probability Theory}

\subsection{Probability space}
A probability space composes of three parts:  \par 
(1) sample space $\Omega$ -- the set of all possible outcomes; \par 
(2) $\sigma$-algebra $\mathcal{F}$ -- collection of subsets of $\Omega$ which satisfy three properties 
\begin{enumerate}[itemsep=-4pt, itemindent=2em]
\vspace{-6pt}
\item $\emptyset \in \mathcal{F}$
\item $\mathcal{A} \in \Omega, \mathcal{A} \in \mathcal{F} \rightarrow \mathcal{A}^C \in \mathcal{F}$
\item $\mathcal{A}_i \in \mathcal{F} \, \forall i \rightarrow \cup_{i=1}^\infty \mathcal{A}_i \in \mathcal{F}$
\vspace{-6pt}
\end{enumerate} \par
(3) probability measure $P : \mathcal{F} \rightarrow [0,1]$. 

\subsection{Random variable}
The probability density function (pdf) $f_P : \Omega \rightarrow \mathbb{R}$ and $\int_\Omega f_P(x) \mathrm{d}x = 1$. Radon-Nikodym derivative $f_P = \frac{\mathrm{d}P}{\mathrm{d}\lambda}$, where $\lambda$ is the Lebesgue measure.

\subsection{Random walk}
Considering one-dimensional random walk problem, the particle moves to the right next position with probability $p$ and to the left next position with probability $1-p$. 

Firstly, we consider the probability that the particle moves to the right next $m$ position after $N$ random walks. Denote the number of steps to the right as $n_R$ and to the left as $n_L$, we have the relations
\begin{equation}
\left\{\begin{aligned}
n_R + n_L &= N \\ n_R - n_L &= m
\end{aligned}\right. \rightarrow
\left\{\begin{aligned}
n_R &= \frac{N+m}{2} \\ n_L &= \frac{N-m}{2}
\end{aligned}\right.
\end{equation}
The number of possible combinations are $C(N, n_R) = \frac{N!}{n_R!n_L!}$. The probability reads
\begin{equation}
P_d(m,N) = C(N, n_R) p^{n_R} (1-p)^{n_L} = \frac{N!}{\left(\frac{N+m}{2}\right)! \left(\frac{N-m}{2}\right)!} p^{\frac{N+m}{2}} (1-p)^{\frac{N-m}{2}}
\end{equation}

Next, we consider the specific condition where $p=\frac{1}{2}$. The probability reads
\begin{equation}
P_d(m,N) = \frac{N!}{\left(\frac{N+m}{2}\right)! \left(\frac{N-m}{2}\right)!} \left(\frac{1}{2}\right)^N
\end{equation}
We want to derive the continuous limit of the probability when $N\rightarrow \infty \color{red}{\left( x = \frac{m}{N} \ll 1 \right)}$ as 
\begin{equation}
P_c = \sqrt{\frac{2}{\pi N}} \exp\left(-\frac{m^2}{2N}\right)
\end{equation}
\begin{proof}
Take the logarithm of the probability
\begin{equation}
\ln P_d = \ln N! - \ln \left(\frac{N+m}{2}\right)! - \ln \left(\frac{N-m}{2}\right)! - N \ln 2
\end{equation}
Using \textcolor{red}{Stirling's approximation} $\ln x! \approx x \ln x - x + \frac{1}{2} \ln (2 \pi x)$, we have
\begin{equation}
\begin{aligned}
\ln P_d 
=& N \ln N - \bcancel{N} + \frac{1}{2} \ln (2 \pi N) - \left(n_R \ln n_R - \bcancel{n_R} + \frac{1}{2} \ln (2 \pi n_R)\right) \\
&- \left(n_L \ln n_L - \bcancel{n_L} + \frac{1}{2} \ln (2 \pi n_L)\right) - N \ln 2 \\
=& N \ln N - n_R \ln n_R - n_L \ln n_L + \frac{1}{2} \left( \ln (2 \pi N) - \ln (2 \pi n_R) - \ln (2 \pi n_L)\right) - N \ln 2 \\
=& -n_R \ln \frac{n_R}{N} -n_L \ln \frac{n_L}{N} + \frac{1}{2} \ln \frac{N}{2\pi n_Rn_L} - N \ln 2 \\
=& -\frac{N}{2} (1+x)\ln\frac{1+x}{2} -\frac{N}{2} (1-x)\ln\frac{1-x}{2} + \frac{1}{2} \ln \left(\frac{N}{2\pi} \frac{1}{\frac{N^2}{4} (1-x^2)}\right) - N \ln 2 \\
=& -\frac{N}{2} [\underbrace{ (1+x)\underbrace{\ln(1+x)}_{\color{blue}{\approx x - \frac{x^2}{2}}} + (1-x)\underbrace{\ln(1-x)}_{\color{blue}{\approx -x - \frac{x^2}{2}}} }_{\color{blue}{\approx x + x^2 - \frac{x^2}{2} - \frac{x^3}{2} - x + x^2 - \frac{x^2}{2} + \frac{x^3}{2} = x^2}} - \bcancel{2\ln 2}] + \frac{1}{2} \ln \frac{2}{\pi N (1-x^2)} - \bcancel{N \ln 2} \\
\approx & -\frac{N}{2} x^2 + \frac{1}{2} \ln \frac{2}{\pi N} 
= -\frac{m^2}{2N} + \frac{1}{2} \ln \frac{2}{\pi N}
\end{aligned}
\end{equation}
We can reach the results by taking the exponents of the above expression.
\end{proof}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Master Equation}

\subsection{Pure birth process}
Consider the pure birth process where the number of individuals $n$ increases by one with a rate $\mu$
\begin{equation}
n \xrightarrow{\mu} n+1
\end{equation}
The master equation reads
\begin{equation}
\frac{\mathrm{d}P_n(t)}{\mathrm{d}t} = \mu P_{n-1}(t) - \mu P_n(t)
\end{equation}

Firstly, we derive the solution of the master equation\dots
\begin{proof}
We introduce the \textcolor{red}{probability generating function} as
\begin{equation}
G(z, t) = \sum_{n=0}^\infty P(n, t) z^n
\end{equation}
The time derivative reads
\begin{equation}
\begin{aligned}
\frac{\partial G(z, t)}{\partial t}
&= \frac{\partial}{\partial t} \sum_{n=0}^\infty P(n, t) z^n 
= \sum_{n=0}^\infty \frac{\partial P(n, t)}{\partial t} z^n \\
&= \sum_{n=0}^\infty (\mu P_{n-1}(t) - \mu P_n(t)) z^n 
= \mu \sum_{n=0}^\infty P_{n-1}(t) z^n - \mu \sum_{n=0}^\infty P_n(t) z^n \\
&= \mu z \sum_{n=0}^\infty \underbrace{P_{n-1}(t)}_{\color{red}{P(-1,\tau)=0}} z^{n-1} - \mu \sum_{n=0}^\infty P_n(t) z^n \\
&= \mu z G(z, t) - \mu G(z, t) 
= \mu (z - 1) G(z, t)
\end{aligned}
\end{equation}
The general solution reads
\begin{equation}
G(z,t) = A \exp \left(\mu(z-1)t\right)
\end{equation}
From the initial conditions $P(0, 0) = 1$ and $P(n, 0) = 0 \forall n > 0$, we have $A = 1$. Therefore, the solution reads
\begin{equation}
G(z,t) = \exp \left(\mu(z-1)t\right) 
= e^{-\mu t} \sum_{n=0}^{\infty} \frac{(\mu zt)^n}{n!}
= \sum_{n=0}^{\infty} \frac{(\mu t)^n}{n!} z^n
\end{equation}
The coefficient of $z^n$ gives the solution of the master equation
\begin{equation}
P_n(t) = \frac{(\mu t)^n}{n!} e^{-\mu t}
\end{equation}
\end{proof}

Secondly, we derive the probability that a new event happens for the event-based simulation methods such as Gillespie's algorithm. Denote $P_0(\tau)$ as the \textcolor{red}{probability of waiting time $\tau > t$} which is $P(\tau > t)$, we have
\begin{equation}
P_0(t + \Delta t) = P_0(t) (1 - \mu \Delta t)
\end{equation}
where $(1 - \Delta t)$ is the probability that no event happens in the time interval $\Delta t$. With the initial condition $P_0(0) = 1$, we have
\begin{equation}
P_0(t) = \exp(-\mu t) = P(\tau > t)
\end{equation}
The culmuative distribution function (CDF) reads
\begin{equation}
P(\tau < t) = 1 - P(\tau > t) = 1 - \exp(-\mu t)
\end{equation}
The probability density function (PDF) reads
\begin{equation}
P(\tau) = \frac{\mathrm{d}P(\tau < t)}{\mathrm{d}t} = \mu \exp(-\mu t)
\end{equation}
So the event-based simulation follows the above distribution.

\subsection{Chemical reaction dynamics}
Consider the dynamics of chemical reaction with three chemical species $A$, $B$ and $AB$ that can react according to the chemical reaction
\begin{equation}
A + B \stackrel{r_F}{\underset{r_B}{\rightleftharpoons}} AB
\end{equation}
where $r_F$ and $r_B$ are the forward (dimerization) and backward (dissociation) reaction rates. To reduce the dimensionality, we let $n_A(t) = n_B(t) = n(t)$ and introduce the constant $N = n_A(t) + n_{AB}(t)$. The forward and backward reaction rates read
\begin{equation}
\left\{ \begin{aligned}
W_- &= r_F \frac{n^2}{N} \\
W_+ &= r_B n_{AB} = r_B (N - n)
\end{aligned} \right.
\end{equation}
The master equation reads
\begin{equation}
\begin{aligned}
\frac{\mathrm{d}P_n(t)}{\mathrm{d}t} 
&= W_-(n+1) P_{n+1}(t) + W_+(n-1) P_{n-1}(t) - (W_-(n) + W_+(n)) P_n(t) \\
&= r_F \frac{(n+1)^2}{N} P_{n+1}(t) + r_B (N - n + 1) P_{n-1}(t) - \left(r_F \frac{n^2}{N} + r_B (N - n)\right) P_n(t)
\end{aligned}
\end{equation}
We want to solve this equation with via a linear noise approximation method called \textit{system size expansion}.

\begin{proof}
Use the \textcolor{red}{van Kampen's Ansatz} of the solution as the linear combination of deterministic term $\varphi(t)$ and a stochastic term $x(t)$ as
\begin{equation}
n(t) = \Omega \varphi(t) + \sqrt{\Omega} x(t) 
\rightarrow x = \frac{n - N \varphi}{\sqrt{N}}
\end{equation}
where $\omega = N$ is the parameter that represents the size of the system. Make a transformation of the PDF
\begin{equation}
P(n, t) \mathrm{d}n = \Pi(x, t) \mathrm{d}x 
\rightarrow P(n, t) = \frac{1}{\sqrt{N}} \Pi(x, t)
\end{equation}
The relation between the two PDFs reads
\begin{equation}
\begin{aligned}
\frac{\partial P(n, t)}{\partial t} 
&= \frac{1}{\sqrt{N}} \left. \frac{\partial \Pi(x, t)}{\partial t} \right|_n \\
&= \frac{1}{\sqrt{N}} \left( \frac{\partial \Pi}{\partial t} + \frac{\partial \Pi}{\partial x} \underbrace{\left. \frac{\partial x}{\partial t} \right|_n}_{\color{blue}{= \left. \frac{\partial}{\partial t} \frac{n - N\varphi(t)}{\sqrt{N}} = -\sqrt{N}\frac{\partial \varphi(t)}{\partial t}{} \right|_n}} \right) \\
&= \frac{1}{\sqrt{N}} \frac{\partial \Pi}{\partial t} - \frac{\partial \varphi(t)}{\partial t} \frac{\partial \Pi}{\partial x}
\end{aligned}
\end{equation}
Define the shifting operators and take the Taylor's expansion
\begin{equation}
\mathbb{E}^k = \exp\left(k \frac{\partial}{\partial n}\right)
= \exp\left(k \frac{1}{\sqrt{N}} \frac{\partial}{\partial x}\right)
= 1 + k \frac{1}{\sqrt{N}} \frac{\partial}{\partial x} + \frac{k^2}{2N} \frac{\partial^2}{\partial x^2} + \cdots
\end{equation}
The master equation reads
\begin{equation}
\begin{aligned}
\frac{\partial P(n, t)}{\partial t}
&= \left(\mathbb{E}^{-1} - 1\right) W_+(n-1) P_n(t) + \left(\mathbb{E}^1 - 1\right) W_-(n) P_n(t) \\
\frac{\partial \Pi}{\partial t} - \sqrt{N} \frac{\partial \varphi(t)}{\partial t} \frac{\partial \Pi}{\partial x}
&\approx \left(-\frac{1}{\sqrt{N}} \frac{\partial}{\partial x} + \frac{1}{2N} \frac{\partial^2}{\partial x^2} \right) W_+(n) \Pi + \left(\frac{1}{\sqrt{N}} \frac{\partial}{\partial x} + \frac{1}{2N} \frac{\partial^2}{\partial x^2} \right) W_-(n) \Pi \\
&= -\frac{1}{\sqrt{N}} \frac{\partial}{\partial x} \left( W_+(n) - W_-(n) \right) \Pi + \frac{1}{2N} \frac{\partial^2}{\partial x^2} \left( W_+(n) + W_-(n) \right) \Pi
\end{aligned}
\end{equation}
The rate coefficients are
\begin{equation}
\begin{aligned}
W_+(n) &= r_B (N - n) = r_B \left(N - N \varphi - \sqrt{N} x\right) = r_B (N - N \varphi) - r_B \sqrt{N} x \\
W_-(n) &= r_F \frac{n^2}{N} = r_F \frac{\left(N \varphi + \sqrt{N} x\right)^2}{N} = r_F \left(N \varphi^2 + 2 \sqrt{N} x \varphi + x^2\right) \\
W_+(n) - W_-(n) &\approx r_B (1 - \varphi) - r_F N \varphi^2 - \left(r_B - 2r_F \varphi\right) \sqrt{N} x \\
W_+(n) + W_-(n) &\approx r_B (1 - \varphi) + r_F N \varphi^2 + \left(-r_B + 2r_F \varphi\right) \sqrt{N} x
\end{aligned}
\end{equation}
Take the terms above $N^0$ order from the master equation, we have
\begin{equation}
\begin{aligned}
\frac{\partial \Pi}{\partial t} - \sqrt{N} {\color{blue} \frac{\partial \varphi(t)}{\partial t}} \frac{\partial \Pi}{\partial x}
\approx & -\sqrt{N} {\color{blue} \left(r_B (1 - \varphi) - r_F \varphi^2\right)} \frac{\partial \Pi}{\partial x} + (r_B + 2r_F \varphi) \frac{\partial (x\Pi)}{\partial x} \\
&+ \frac{1}{2} \left(r_B (1 - \varphi) + r_F \varphi^2\right) \frac{\partial^2 \Pi}{\partial x^2}
\end{aligned}
\end{equation}
From the $\sqrt{N}$-order terms, we have the ordinary differential equation (ODE) for $\varphi(t)$ as
\begin{equation}
\frac{\partial \varphi(t)}{\partial t} = r_B (1 - \varphi) - r_F \varphi^2
\end{equation}
From the $N^0$-order terms, we have the Fokker-Planck equation for $x(t)$ as
\begin{equation}
\frac{\partial \Pi}{\partial t} = \left(r_B + 2r_F \varphi\right) \frac{\partial (x \Pi)}{\partial x} + \frac{1}{2} \left( r_B (1 - \varphi) + r_F \varphi^2 \right)\frac{\partial^2 \Pi}{\partial x^2}
\end{equation}
Compared to the standard Fokker-Planck equation form
\begin{equation}
\frac{\partial \Pi}{\partial t} = -\frac{\partial}{\partial x} \left( A(x) \Pi \right) + \frac{1}{2} \frac{\partial^2}{\partial x^2} \left( B(x) \Pi \right)
\end{equation}
The drift and diffusion coefficients read
\begin{equation}
\left\{ \begin{aligned}
A(x) &= -(r_B + 2r_F \varphi) x \\
B(x) &= r_B (1 - \varphi) + r_F \varphi^2
\end{aligned} \right.
\end{equation}
The moments of the PDF follows the ODEs as
\begin{equation}
\left\{ \begin{aligned}
\frac{\partial \langle x \rangle}{\partial t} &= \langle A(x) \rangle = -(r_B + 2r_F \varphi) \langle x \rangle \\
\frac{\partial \langle x^2 \rangle}{\partial t} &= 2 \langle x A \rangle + \langle B \rangle = -2 (r_B + 2r_F \varphi) \langle x^2 \rangle + r_B (1 - \varphi) + r_F \varphi^2
\end{aligned} \right.
\end{equation}
The solution of the Fokker-Planck equation reads
\begin{equation}
\Pi(x, t) = \frac{1}{\sqrt{2\pi \langle x^2 \rangle}} \exp
\left(-\frac{(x-\langle x \rangle)^2}{2\langle x^2 \rangle}\right)
\end{equation}
\end{proof}



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Equations of Motion for Stochastic Processes}

\subsection{Langevin equation}
The Langevin equation used to describe the Brownian motion of particles suspended in a fluid reads
\begin{equation}
\dot{P} = M \ddot{Q} = \underbrace{- \nabla V(Q)}_{\text{potential}} \,\underbrace{- M \gamma \dot{Q}}_{\text{damping}} \, \underbrace{+ f_r(t)}_{\text{random}}
\end{equation}
The random force $f_r(t)$ is a stochastic process that has two properties as
\begin{equation}
\left\{ \begin{aligned}
\langle f_r(t) \rangle &= 0 \\
\langle f_r(t), f_r(t') \rangle &= \delta(t-t')
\end{aligned} \right.
\end{equation}

\subsection{Generalized Langevin equation}
We can derive the Langevin equation from the generalized Langevin equation, which is used to describe the deterministic model. This model, known as the \textbf{Caldeira-Leggett Model} or \textbf{System-Bath Model}, studies the dynamics of a \textit{particle} coupled with a \textit{heat bath represented by a set of oscillators} that exchange energy with the particle. It is a canonical model used to study the \textit{open quantum systems} and \textit{quantum dissipation} in theoretical physics.

The system of interest is the paticle described by the position $Q$ and momentum $P = M\dot{Q}$. The heat bath is described by a set of oscillators with positions $q_i$ and momenta $p_i$. The Hamiltonian of the system reads
\begin{equation}
H(Q, P, \{q_i, p_i\}) = \frac{P^2}{2M} + \sum_{i=1}^N \frac{p_i^2}{2m_i} + V(Q) + \frac{1}{2 }  \sum_{i=1}^N k_i \,  (q_i - Q)^2
\end{equation}
The equation of motion (EOM) of the particle reads
\begin{equation}
\left\{ \begin{aligned}
\dot{Q} &= \frac{P}{M} \\
\dot{P} &= -\nabla V(Q) - \sum_{i=1}^N k_i \, (Q - q_i)
\end{aligned} \right.
\end{equation}
The EOM of the oscillators reads
\begin{equation}
\left\{ \begin{aligned}
\dot{q_i} &= \frac{p_i}{m_i} \\
\dot{p_i} &= -k_i \, (q_i - Q)
\end{aligned} \right.
\end{equation}
We want to solve the EOM of the particle without solving the EOM of the oscillators. 

\begin{proof}
From the EOM of oscillators, we have 
\begin{equation}
\ddot{q}_i + \omega^2 q_i = \omega^2 Q, \quad \omega_i = \sqrt{\frac{k_i}{m_i}}
\end{equation}
This can be solved with the method of \textcolor{red}{variation of parameters}. Split the solution into a \textit{homogeneous} part and a \textit{particular} part as
\begin{equation}
q_i(t) = q_i^H(t) + q_i^P(t) \label{equ:q_i}
\end{equation}
The homogeneous part reads
\begin{equation}
q_i^H(t) = c_1 \cos(\omega_i t) + c_2 \sin(\omega_i t)
\end{equation}
The particular part reads
\begin{equation}
q_i^P(t) = c_1(t) \cos(\omega_i t) + c_2(t) \sin(\omega_i t)
\end{equation}
Taking the derivative over the particular part, we have
\begin{equation}
\begin{aligned}
\dot{q}_i^P(t) &= \underbrace{\dot{c}_1(t) \cos(\omega_i t) + \dot{c}_2(t) \sin(\omega_i t)}_{\color{blue}{\text{take as }0}} + c_1(t) (-\omega_i \sin(\omega_i t)) + c_2(t) (\omega_i \cos(\omega_i t)) \\
&= -c_1(t) \omega_i \sin(\omega_i t) + c_2(t) \omega_i \cos(\omega_i t) \\
\ddot{q}_i^P(t) &= -\dot{c}_1(t) \omega_i \sin(\omega_i t) + \dot{c}_2(t) \omega_i \cos(\omega_i t) - c_1(t) \omega_i^2 \cos(\omega_i t) - c_2(t) \omega_i^2 \sin(\omega_i t) \\
&= -\dot{c}_1(t) \omega_i \sin(\omega_i t) + \dot{c}_2(t) \omega_i \cos(\omega_i t) - \omega_i^2 Q
\end{aligned}
\end{equation}
Integrating into the EOM of the oscillators \eqref{equ:q_i}, we have
\begin{equation}
\left\{ \begin{aligned}
-\dot{c}_1(t) \omega_i \sin(\omega_i t) + \dot{c}_2(t) \omega_i \cos(\omega_i t) &= \omega_i^2 Q \\
\dot{c}_1(t) \cos(\omega_i t) + \dot{c}_2(t) \sin(\omega_i t) &= 0
\end{aligned} \right.
\end{equation}
We have the solution for coefficients of the particular part as
\begin{equation}
\left\{ \begin{aligned}
\dot{c}_1(t) &= -\omega_i Q \sin(\omega_i t) \\
\dot{c}_2(t) &= \omega_i Q \cos(\omega_i t)
\end{aligned} \right. \Rightarrow 
\left\{ \begin{aligned}
c_1(t) &= \int_0^t -\omega_i Q \sin(\omega_i s) \mathrm{d}s \\
c_2(t) &= \int_0^t \omega_i Q \cos(\omega_i s) \mathrm{d}s
\end{aligned} \right.
\end{equation}
The full solution reads
\begin{equation}
\begin{aligned}
q_i(t) &= c_1 \cos(\omega_i t) + c_2 \sin(\omega_i t) + \underbrace{c_1(t) \cos(\omega_i t) + c_2(t) \sin(\omega_i t)}_{\color{blue}{\sin(\omega_i t) \cos(\omega_i s) - \cos(\omega_i t) \sin(\omega_i s) = \sin(\omega_i (t-s))}} \\
&= c_1 \cos(\omega_i t) + c_2 \sin(\omega_i t) + \underbrace{\int_0^t \omega_i Q \sin(\omega_i (t-s)) \mathrm{d}s}_{\color{blue}{= \left. Q(s)\cos(\omega_i(t-s)) \right|_0^t} - \int_0^t \dot{Q}(s) \cos(\omega_i (t-s)) \mathrm{d}s} \\
&= q(0) \cos(\omega_i t) + \frac{p(0)}{\omega_i m_i} \sin(\omega_i t) + Q(t) - Q(0) \cos(\omega_i t) - \int_0^t \dot{Q}(s) \cos(\omega_i (t-s)) \mathrm{d}s
\end{aligned}
\end{equation}
The momentum equation for the particle reads
\begin{equation}
\begin{aligned}
\dot{P} &= -\nabla V(Q) - \sum_{i=1}^N k_i \, (Q - q_i) \\
&= -\nabla V(Q) - \sum_{i=1}^N k_i \, \left[ \underbrace{-[q(0) - Q(0)] \cos(\omega_i t) - \frac{p(0)}{\omega_i m_i} \sin(\omega_i t)}_{\color{blue}{R(t)}} + \int_0^t \dot{Q}(s) \cos(\omega_i (t-s)) \mathrm{d}s \right] \\
&= \underbrace{-\nabla V(Q)}_{\color{blue}{\text{potential}}} \, - \int_0^t \mathrm{d}s \, \dot{Q}(s) \underbrace{\sum_{i=1}^N k_i \, \cos(\omega_i (t-s))}_{\color{blue}{\text{memory kernel}}} \, \underbrace{- \sum_{i=1}^N k_i \, R(t)}_{\color{blue}{\text{noise term } \mathcal{R}(t)}} 
\end{aligned} \label{equ:GLE}
\end{equation}
where the terms are the deterministic version of the stochastic terms in Langevin equation.
\end{proof}

\subsubsection{Heat bath}
The heat bath is an important component in the Caldeira-Leggett model. We want to answer the question that how to define the frequencies $\omega_i$ and spring constants $k_i$ of the oscillators such that the collective behavior of the oscillators can simulate a thermal bath.

We consider three different kinds of heat baths.

(1) Resonant single-frequency bath : $\omega_i = \omega_0$, $k_i = k_0$, $m_i = m_0$, spectral density $J(\omega) = \delta(\omega - \omega_0)$. The motion of particle with frequency $\omega_0$ is not damped and will be oscillating between the particle and heat bath.

(2) Uniform frequency-band bath : $\omega_i = \nu_i \omega_{\text{max}}$, $k_i = \omega_0^2 m_i$, $m_i = m_0$, spectral density $J(\omega) = 1 / \omega_{\text{max}}$. The energy is damped in short time interval but will be recurred in long time interval.

(3) Ohmic-Drude bath : The frequencies are drawn from a uniform distribution
\begin{equation}
\omega_i = N^a \nu_i\,; \quad \nu_i \in \mathcal{U}(0,1)\,; \quad a \in[0,1]
\end{equation}
The spring constants are defined as
\begin{equation}
k_i = f^2(\omega_i) \Delta \omega\, ; \quad \Delta \omega = \frac{N^a}{N}
\end{equation}
and the spectral density is defined by \textcolor{red}{Ohmic-Drude} spectrum
\begin{equation}
f(\omega) = \sqrt{\frac{2}{\pi} \frac{\alpha^2 M \gamma}{\alpha^2 + \omega_i^2}}
\end{equation}
The memory kernel is written as
\begin{equation}
K(t) = \sum_{i=1}^N k_i \cos(\omega_i t)
= \sum_{i=1}^N \frac{2}{\pi} \frac{\alpha^2 M \gamma}{\alpha^2 + \omega_i^2} \, \Delta \omega \cos (\omega_i t)
\end{equation}
At the limit of infinite number of oscillators, we show that 
\begin{equation}
\lim_{N\rightarrow \infty} K(t) = \int_0^\infty \mathrm{d}\omega \, \frac{2}{\pi} \frac{\alpha^2 M \gamma}{\alpha^2 + \omega^2} \cos(\omega t) = \alpha M \gamma e^{-\alpha t}
\end{equation}

\subsubsection{Noise term}
We now show that $\mathcal{R}(t)$ in \eqref{equ:GLE} is a Gaussian white noise.

\begin{proof}
The noise term can be rewritten as 
\begin{equation}
\begin{aligned}
\mathcal{R}(t)
&= \sum_{i=1}^N \left[ k_i (q(0) - Q(0)) \cos(\omega_i t) + p(0) \underbrace{\frac{k_i}{\omega_i m_i}}_{\color{blue}{\sqrt{k_i/m_i}}} \sin(\omega_i t) \right] \\
&= \underbrace{\sqrt{\frac{1}{\beta}}}_{\color{blue}{\beta = 1 / (k_B T)}} \sum_{i=1}^N \sqrt{k_i} \left[ \sqrt{k_i \beta} (q(0) - Q(0)) \cos(\omega_i t) + p(0) \sqrt{\frac{\beta}{m_i}} \sin(\omega_i t) \right]
\end{aligned}
\end{equation}
Assume that the oscillators are in thermal equilibrium, their initial positions and momenta are drawn from the Boltzmann distribution as
\begin{equation}
\left\{ \begin{aligned}
\pi_q(q_i) &= \frac{1}{Z_q} \exp\left( - \beta \frac{k_i}{2} (q_i - Q(0))^2 \right) \\
\pi_p(p_i) &= \frac{1}{Z_p} \exp\left( - \beta \frac{p_i^2}{2m_i} \right)
\end{aligned} \right.
\end{equation}
Comparing with the Gaussian distribution, we have the initial positions and momenta as
\begin{equation}
\left\{ \begin{aligned}
q_i(0) &= Q(0) + \xi_i \sqrt{\frac{1}{\beta k_i}} \\
p_i(0) &= \eta_i \sqrt{\frac{m_i}{\beta}}
\end{aligned} \right.
\end{equation}
where $\xi_i, \eta_i \in \mathcal{N}(0,1)$. The noise term reads
\begin{equation}
\mathcal{R}(t) = \sqrt{\frac{1}{\beta}} \sum_{i=1}^N \sqrt{k_i} \left[ \xi_i \cos(\omega_i t) + \eta_i \sin(\omega_i t) \right]
\end{equation}
which is a Gaussian white noise.
\end{proof}



\section{Escape Rate Theory}

\subsection{Overdamped Langevin equation}
The underdampmed Langevin equation reads
\begin{equation}
\left\{ \begin{aligned}
\dot{x}_t &= v_t \\
\dot{v}_t &= - \nabla V(x_t) - m \gamma v_t + \bar{\sigma} \eta_t
\end{aligned} \right.
\end{equation}
where $\bar{\sigma} = \sqrt{2 m k_B T \gamma}$ is the standard deviation of the momentum and $\eta_t$ is a Gaussian white noise that satisfies
\begin{equation}
\left\{ \begin{aligned}
\langle \eta_t \rangle &= 0 \\
\langle \eta_t \eta_{t'} \rangle &= \delta(t-t')
\end{aligned} \right.
\end{equation}
In the region of high friction, the particle is overdamped and the overdamped Langevin equation reads
\begin{equation}
\dot{x}_t = - \frac{1}{m \gamma} \nabla V(x_t) + \sigma \eta_t, \quad
\sigma = \sqrt{\frac{2 k_B T}{m \gamma}} = \sqrt{\frac{2}{\beta m \gamma}} = \sqrt{2 D}
\end{equation}

\subsection{Kolmogorov equation}
We firstly discuss the \textit{Fokker-Planck equation} or the \textit{forward Kolmogorov equation} that describes the time evolution of the probability density function (PDF) of the particle position wich answer the question that : \textcolor{blue}{Given the initial distribution of the particle at time $t_0$, what is the probability of finding the particle at position $x$ at time $t$?} Which is, how the probability density function of the particle position evolves with time.

\begin{proof}
The probability flow defined by the overdamped Langevin equation reads
\begin{equation}
\begin{aligned}
J(x, t) 
&= J_\mathrm{drift} + J_\mathrm{diffusion} \\
&= v(x, t) P(x, t) - D(x, t) \frac{\partial P(x, t)}{\partial x} \\
&= -\frac{1}{m \gamma} \nabla V(x) P(x, t) - D \frac{\partial P(x, t)}{\partial x}
\end{aligned}
\end{equation}
From the continuity equation, we have
\begin{equation}
\frac{\partial P(x, t)}{\partial t} = -\frac{\partial J(x, t)}{\partial x}
\end{equation}
Substituting the probability flow, we have
\begin{equation}
\frac{\partial P(x, t|x_0, t_0)}{\partial t} 
= \frac{1}{m \gamma} \frac{\partial}{\partial x} \left( \nabla V(x) P(x, t|x_0, t_0) \right) + D \frac{\partial^2 P(x, t|x_0, t_0)}{\partial x^2}
\end{equation}
\end{proof}

Next, we discuss the \textit{backward Kolmogorov equation} that describes the time evolution of the probability of the particle reaching a certain position at a certain time which answer the question that : \textcolor{blue}{If we know the particle is at position $x$ at time $t$, what is the probability that the particle was at position $x_0$ at time $t_0$?} Which is, how the probability of the particle reaching a certain position changes with the initial time.

\begin{proof}
From the Champman-Kolmogorov equation
\begin{equation}
P(x, t|x_0, t_0)
= \int_{-\infty}^{\infty} P(x, t|x_1, t_1) P(x_1, t_1|x_0, t_0) \mathrm{d}x_1
\end{equation}
Taking the Taylor's expansion, we have
\begin{equation}
P(x, t|x_1, t_1) = P(x, t|x_0, t_0) + (x_1 - x_0) \frac{\partial P}{\partial x_0} + \frac{1}{2} (x_1 - x_0)^2 \frac{\partial^2 P}{\partial x_0^2} + (t_1 - t_0) \frac{\partial P}{\partial t_0} + \cdots
\end{equation}
Substituting the Taylor's expansion, we have
\begin{equation}
\begin{aligned}
P(x, t|x_0, t_0)
\approx \, & P(x, t|x_0, t_0) + \int_\infty^\infty (x_1 - x_0) \frac{\partial P}{\partial x_0} P(x_1, t_1|x_0, t_0) \mathrm{d}x_1 \\
&+ \int_\infty^\infty \frac{1}{2} (x_1 - x_0)^2 \frac{\partial^2 P}{\partial x_0^2} P(x_1, t_1|x_0, t_0) \mathrm{d}x_1 + (t_1 - t_0) \frac{\partial P}{\partial t_0} \\
= \, & P(x, t|x_0, t_0) + \underbrace{\langle x_1 - x_0 \rangle}_{\color{blue}{=-\frac{1}{m \gamma} \nabla V(x)|_{x=x_0} \Delta t}} \frac{\partial P}{\partial x_0} + \frac{1}{2} \underbrace{\langle (x_1 - x_0)^2 \rangle}_{\color{blue}{=2D \Delta t}} \frac{\partial^2 P}{\partial x_0^2} + \Delta t \, \frac{\partial P}{\partial t_0} \\
= \, & P(x, t|x_0, t_0) - \frac{1}{m \gamma} \nabla V(x)|_{x=x_0} \Delta t \, \frac{\partial P}{\partial x_0} + D \Delta t \, \frac{\partial^2 P}{\partial x_0^2} + \Delta t \, \frac{\partial P}{\partial t_0}
\end{aligned}
\end{equation} 
We have the backward Kolmogorov equation
\begin{equation}
- \frac{\partial P(x, t|x_0, t_0)}{\partial t_0} = - \frac{1}{m \gamma} \nabla V(x)|_{x=x_0} \frac{\partial P(x, t|x_0, t_0)}{\partial x_0} + D \frac{\partial^2 P(x, t|x_0, t_0)}{\partial x_0^2}
\end{equation}
Since the potential and diffusion are not time-dependent, the system is temporally homogeneous
\begin{equation}
P(x, t|x_0, t_0) = P(x, t - t_0|x_0, 0)
\end{equation}
It follows that
\begin{equation}
\frac{\partial P(x, t|x_0, t_0)}{\partial t_0} 
= \frac{\partial P(x, t - t_0|x_0, 0)}{\partial t_0} 
= - \frac{\partial P(x, t - t_0|x_0, 0)}{\partial (t - t_0)} 
= - \frac{\partial P(x, t|x_0, t_0)}{\partial t}
\end{equation}
Another form of the backward Kolmogorov equation reads
\begin{equation}
\frac{\partial P(x, t|x_0, t_0)}{\partial t} = - \frac{1}{m \gamma} \nabla V(x)|_{x=x_0} \frac{\partial P(x, t|x_0, t_0)}{\partial x_0} + D \frac{\partial^2 P(x, t|x_0, t_0)}{\partial x_0^2} \coloneqq \mathcal{L} P(x, t|x_0, t_0)
\label{equ:bKE2}
\end{equation}
\end{proof}

\subsection{Pontrjagin's theory for escape rate}
Within Pontrjagin's theory, the escape rate is defined as $k=1/\langle\tau\rangle$, where $\langle\tau\rangle$ is the \textit{mean first passage time (MFPT)}. The MFPT is defined as the average time for the particle to reach a certain position $x_F$ for the first time
\begin{equation}
\langle\tau(x_F|x_0, t_0)\rangle = \int_{t_0}^{\infty} \mathrm{d}\tau \, \tau \, \Pi(x_F, \tau|x_0, t_0)
\end{equation}
where $\Pi(x_F, \tau|x_0, t_0)$ is the probability that the particle reaches $x_F$ \textit{at the first time} at time $t$. To define $\Pi(x_F, \tau|x_0, t_0)$, we introduce the fraction of trajectories that \textit{did not reach} $x_F$ at time time $t$ as
\begin{equation}
\Phi(x_F, t|x_0, t_0) = \int_{-\infty}^{x_F} \mathrm{d}x \, P(x, t|x_0, t_0)
\end{equation}
where $P(x, t|x_0, t_0)$ is the solution of Fokker-Planck equation with the absorbing boundary condition
\begin{equation}
P(x_F, t|x_0, t_0) = 0
\end{equation}
The PDF of trajectories that reach $x_F$ at time $t$ for the first time reads
\begin{equation}
\lim_{\Delta t \rightarrow 0} \frac{\Phi(x_F, t|x_0, t_0) - \Phi(x_F, t+\Delta t|x_0, t_0)}{\Delta t} = -\frac{\partial \Phi(x_F, t|x_0, t_0)}{\partial t}
\end{equation}
We have the identity
\begin{equation}
\Pi(x_F, t|x_0, t_0) = -\frac{\partial \Phi(x_F, t|x_0, t_0)}{\partial t}
\end{equation}
The MPFT reads
\begin{equation}
\begin{aligned}
\langle\tau(x_F|x_0, t_0)\rangle 
&= \int_{t_0}^{\infty} \mathrm{d}\tau \, \tau \, \Pi(x_F, \tau|x_0, t_0) \\
&= \int_{t_0}^{\infty} \mathrm{d}\tau \, \tau \, \left(-\frac{\partial \Phi(x_F, t|x_0, t_0)}{\partial t}\right) \\
&= - \left[ \tau \Phi(x_F, t|x_0, t_0) \right]_{t_0}^{\infty} + \int_{t_0}^{\infty} \mathrm{d}\tau \, \Phi(x_F, t|x_0, t_0) \\
&= \int_{t_0}^{\infty} \mathrm{d}\tau \, \Phi(x_F, t|x_0, t_0) \\
&= \int_{t_0}^{\infty} \mathrm{d}\tau \, \int_{-\infty}^{x_F} \mathrm{d}x \, P(x, t|x_0, t_0) \\
\end{aligned}
\end{equation}
Applying the operator $\mathcal{L}$ in Eq. \eqref{equ:bKE2} to both sides, we have
\begin{equation}
\begin{aligned}
\mathcal{L} \langle\tau(x_F|x_0, t_0)\rangle 
&= \mathcal{L} \left( \int_{t_0}^{\infty} \mathrm{d}\tau \, \int_{-\infty}^{x_F} \mathrm{d}x \, P(x, t|x_0, t_0) \right) \\
&= \int_{t_0}^{\infty} \mathrm{d}\tau \, \int_{-\infty}^{x_F} \mathrm{d}x \, \mathcal{L} P(x, t|x_0, t_0) \\
&= \int_{t_0}^{\infty} \mathrm{d}\tau \, \int_{-\infty}^{x_F} \mathrm{d}x \, \frac{\partial P(x, t|x_0, t_0)}{\partial t} \\
&= \int_{-\infty}^{x_F} \mathrm{d}x \left. P(x, t_0|x_0, t_0) \right|_{t_0}^{\infty} \\
&= \int_{-\infty}^{x_F} \mathrm{d}x \left( 0 - P(x, t_0|x_0, t_0) \right) \\
&= -1
\end{aligned}
\end{equation}
We have the differential equation for the MFPT as
\begin{equation}
\mathcal{L} \langle\tau(x_F|x_0, t_0)\rangle 
= - \frac{1}{m \gamma} \nabla V(x)|_{x=x_0} \frac{\partial \langle\tau(x_F|x_0, t_0)\rangle}{\partial x_0} + D \frac{\partial^2 \langle\tau(x_F|x_0, t_0)\rangle}{\partial x_0^2}
= -1
\end{equation}
We will show that the solution of the above equation is the \textcolor{red}{Pontryagin formula} as
\begin{equation}
\langle\tau(x_F|x_0, t_0)\rangle = \frac{1}{D} \int_{x_0}^{x_F} \mathrm{d}x \, e^{\beta V(x)} \int_{-\infty}^{x} \mathrm{d}x' \, e^{-\beta V(x')} 
\label{equ:pf}
\end{equation}

\begin{proof}
From the Einstein relation $D = k_B T / (m \gamma)$ and the definition $\beta = 1 / (k_B T)$, we have
\begin{equation}
\frac{\partial^2 \tau}{\partial y^2} - \beta \, \nabla V(y) \frac{\partial \tau}{\partial y} = - \frac{1}{D}, 
\quad \tau \coloneqq \langle\tau(x_F|x_0, t_0)\rangle, \, y \coloneqq x_0
\label{equ:tau_ode}
\end{equation}
To reduce the order of the differential equation, we define $u(y) = \partial \tau / \partial y$, we have
\begin{equation}
\frac{\partial u}{\partial y} - \beta \, \nabla V(y) u = - \frac{1}{D}
\label{equ:u_ode}
\end{equation}
The above ODE can be solved with \textit{integrating factor} method
\begin{equation}
I(y) = \exp\left( \int_{-\infty}^y -\beta \, \nabla V(y') \mathrm{d}y' \right) = \exp\left( -\beta V(y) \right)
\end{equation}
Multiply Eq. \eqref{equ:u_ode} by $I(y)$, we have
\begin{equation}
\frac{\partial}{\partial y} \left( u(y) \exp\left( -\beta V(y) \right) \right) = - \frac{1}{D} \exp\left( -\beta V(y) \right)
\end{equation}
Integrate both sides from $-\infty$ to $y$, we have
\begin{equation}
u(y) \exp\left( -\beta V(y) \right) - u(-\infty) \exp\left( -\beta V(-\infty) \right) 
= - \frac{1}{D} \int_{-\infty}^y \mathrm{d}y' \, \exp\left( -\beta V(y') \right) 
\end{equation}
Use the reflecting boundary condition $u(-\infty) = 0$, we have
\begin{equation}
u(y) = - \frac{1}{D} \exp\left( \beta V(y) \right) \int_{-\infty}^y \mathrm{d}y' \, \exp\left( -\beta V(y') \right) 
\end{equation}
The solution of Eq. \eqref{equ:tau_ode} can be reached by integration again
\begin{equation}
\begin{aligned}
\tau (y)
&= \tau(x_F) - \int_{y}^{x_F} \mathrm{d}x \, u(x) \\
&= 0 + \frac{1}{D} \int_{y}^{x_F} \mathrm{d}x \, e^{\beta V(x)} \int_{-\infty}^x \mathrm{d}x' \, e^{-\beta V(x')} \\
&= \frac{1}{D} \int_{y}^{x_F} \mathrm{d}x \, e^{\beta V(x)} \int_{-\infty}^{x} \mathrm{d}x' \, e^{-\beta V(x')}
\end{aligned}
\end{equation}
which is exactly the Pontryagin's formula in Eq. \eqref{equ:pf}.
\end{proof}







%%%%%%%%%%% If you don't have citations then comment the lines below:
%
\bibliographystyle{abbrvnat}           % if you need a bibliography
\bibliography{mybib}                % assuming yours is named mybib.bib


%%%%%%%%%%% end of doc
\end{document}